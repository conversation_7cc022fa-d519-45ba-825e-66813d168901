import { createApp } from "vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import App from "./App.vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import router from "./router";
import BaseTitle from "./components/baseComponents/baseTitle.vue";

const app = createApp(App);

// 全局属性
app.config.globalProperties.$echarts = echarts;
app.config.globalProperties.$dayjs = dayjs;

// 全局注册组件
app.component("BaseTitle", BaseTitle);

app.use(ElementPlus, {
  locale: zhCn,
});
app.use(router);
app.mount("#app");
