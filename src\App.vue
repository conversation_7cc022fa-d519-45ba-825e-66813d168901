<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import { provideCCBar } from "./composables/useCCBar";

export default {
  name: "App",
  mounted() {
  },
  setup() {
    // 在setup中初始化
    const ccbarConfig = {
      baseURL: "/yc-ccbar-v1",
      wsURL: "", // 暂时不使用WebSocket
      timeout: 10000,
      autoReconnect: false,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatInterval: 30000,
      autoReady: false,
      pollingInterval: 5000,
      entId: localStorage.getItem("userEntId"),
      loginKey: "123",
      productId: localStorage.getItem("userBusiId"),
      debug: true,
      serviceType: "polling", // 使用轮询模式
      disableWebSocket: true, // 禁用WebSocket
    };

    const { ccbarService, ccbarData } = provideCCBar(ccbarConfig);

    // 初始化CCBar服务
    const initCCBar = async () => {
      try {
        const result = await ccbarService.init();
        if (result.state) {
          // 保存初始化数据到全局状态
          ccbarData.value = {
            skillGroups: result.data.result.groups || [],
            busyTypes: result.data.result.busyTypes || [],
            agentPhones: result.data.result.agentPhones || [],
            initialized: true,
          };
        }
      } catch (error) {
        console.error("初始化失败", error);
      }
    };

    initCCBar();



    // 监听轮询强制停止事件
    ccbarService.on("polling:forceStop", (data) => {
      console.log("轮询被强制停止:", data);
    });

    // 监听座席状态变更事件
    ccbarService.on("agent:stateChanged", (e,data) => {
      console.log("座席状态变更:", e,data);
    });
  },
};
</script>

<style lang="scss">
@import "./assets/css/index.scss";
/* 全局样式 */
* {
  padding: 0;
  margin: 0;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  font-family: Source Han Sans CN;
}

.flex {
  display: flex;
  align-items: center;
}

.fontStyle {
  letter-spacing: normal;
  background: linear-gradient(180deg, #ffffff 35%, #9effff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.number {
  font-family: zcoolqingkehuangyouti !important;
}

.bottomStyle {
  margin-bottom: 8px;
}

.bottom {
  height: calc(100% - 82px);
  display: flex;
  /* background-color: #ffffff; */
}

.bottom-right {
  flex: 1;
  overflow: hidden;
  margin-left: 16px;
}

.right-bottom {
  display: flex;
  height: calc(100% - 56px);
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: rgba(0, 20, 50, 0.3);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 128, 255, 0.35);
  border-radius: 4px;
  border: 1px solid rgba(0, 128, 255, 0.45);
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 128, 255, 0.55);
}
::-webkit-scrollbar-corner {
  background: transparent;
}

/* 2. Firefox 浏览器 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 128, 255, 0.35) rgba(0, 20, 50, 0.3);
}
.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform 0.4s cubic-bezier(0.55, 0, 0.1, 1), opacity 0.4s;
}
.slide-right-enter-from,
.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
.slide-right-enter-to,
.slide-right-leave-from {
  transform: translateX(0);
  opacity: 1;
}

[v-cloak] {
  display: none;
}

.is_show {
  opacity: 0;
  visibility: hidden;
}

:root {
  /* 边框颜色 */
  --el-border-color-light: rgba(0, 128, 255, 0.2) !important;
  --el-border-color: rgba(0, 128, 255, 0.3) !important;
  --el-border-color-hover: rgba(0, 255, 255, 0.5) !important;

  /* 背景颜色 */
  --el-bg-color: rgba(12, 33, 75, 1) !important;
  --el-bg-color-overlay: rgba(12, 33, 75, 0.9) !important;
  --el-bg-color-page: rgba(8, 24, 57, 1) !important;

  /* 文字颜色 */
  --el-text-color-primary: #ffffff !important;
  --el-text-color-regular: rgba(255, 255, 255, 0.8) !important;
  --el-text-color-secondary: rgba(255, 255, 255, 0.6) !important;
  --el-text-color-placeholder: rgba(255, 255, 255, 0.4) !important;

  /* 填充颜色 */
  --el-fill-color: rgba(0, 128, 255, 0.1) !important;
  --el-fill-color-light: rgba(0, 128, 255, 0.05) !important;
  --el-fill-color-blank: rgba(12, 33, 75, 1) !important;
  --el-fill-color-darker: rgba(0, 128, 255, 0.2) !important;

  /* 主色 */
  --el-color-primary: #00ffff !important;
  --el-color-primary-light-3: rgba(0, 255, 255, 0.7) !important;
  --el-color-primary-light-5: rgba(0, 255, 255, 0.5) !important;
  --el-color-primary-light-7: rgba(0, 255, 255, 0.3) !important;
  --el-color-primary-light-9: rgba(0, 255, 255, 0.1) !important;
  --el-color-primary-dark-2: #00cccc !important;

  --el-color-info-light-9: rgba(0, 255, 255, 0.1) !important;
  --el-color-info: #00ffff !important;

  /* 禁用状态 */
  --el-disabled-bg-color: rgba(0, 128, 255, 0.05) !important;
  --el-disabled-text-color: rgba(255, 255, 255, 0.3) !important;
  --el-disabled-border-color: rgba(0, 128, 255, 0.1) !important;

  /* 弹出框阴影 */
  --el-box-shadow: 0 2px 12px 0 rgba(0, 128, 255, 0.3) !important;
  --el-box-shadow-light: 0 2px 8px 0 rgba(0, 128, 255, 0.2) !important;
  --el-box-shadow-dark: 0 2px 16px 0 rgba(0, 128, 255, 0.4) !important;
}
</style>
