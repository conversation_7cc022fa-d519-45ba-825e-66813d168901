<template>
  <div class="seat-map">
    <div class="seatBox">
      <!-- 遍历两个数组，简化重复代码 -->
      <div
        v-for="(array, arrayIndex) in [leftArray, rightArray]"
        :key="'container-' + arrayIndex"
        class="skill-groups-container"
      >
        <div
          v-for="(group, groupIndex) in array"
          :key="groupIndex"
          class="skill-group"
        >
          <div class="skill-group-header">
            <div class="skill-group-title">
              {{ group.dataName }} （{{ group.agentCount }}人）
            </div>
            <div
              class="skill-group-count"
              v-if="currentTab === 'queryAgentPlaceWorkGroup'"
              @click="handleWorkGroupInfo(group)"
            >
              <img src="@/assets/image/right/hx.png" alt="" />
            </div>
          </div>
          <div class="skill-group-content">
            <div
              v-if="!group.users || group.users.length === 0"
              class="empty-state"
            >
              <img src="@/assets/image/map/empty.png" alt="暂无人员" />
              <div class="empty-text">暂无人员</div>
            </div>
            <div
              v-else
              v-for="(agent, agentIndex) in group.users"
              :key="agentIndex"
              :class="[
                'seat-item',
                { click_select: agent.select },
                { is_afterLongMsgId: hasWarnings(agent) },
              ]"
              @click="handleView(agent)"
              @mouseenter="(event) => handleSeatEnter(event, agent)"
              @mouseleave="handleSeatLeave"
              :id="`S${agent.seatNo}`"
            >
              <!-- 浮窗提示 -->
              <div
                class="seat-tooltip"
                v-if="currentTooltipItem === agent && hasWarnings(agent)"
              >
                <div class="bgContent">
                  <div class="tooltip-content">
                    <template v-if="agent.extraLongCallMsgId">
                      <div
                        class="tooltip-title"
                        @click.stop="
                          handleAlarm(
                            agent.extraLongCallMsgId,
                            'extraLongCallMsgId',
                            agent
                          )
                        "
                      >
                        超长通话
                      </div>
                    </template>
                    <template v-if="agent.afterLongMsgId">
                      <div
                        class="tooltip-title"
                        @click.stop="
                          handleAlarm(
                            agent.afterLongMsgId,
                            'afterLongMsgId',
                            agent
                          )
                        "
                      >
                        话后超时
                      </div>
                    </template>
                    <template v-if="agent.seekHelpMsgId">
                      <div
                        class="tooltip-title"
                        @click.stop="
                          handleAlarm(
                            agent.seekHelpMsgId,
                            'seekHelpMsgId',
                            agent
                          )
                        "
                      >
                        求助
                      </div>
                    </template>
                    <template v-if="agent.speechSpeedMsgId">
                      <div
                        class="tooltip-title"
                        @click.stop="
                          handleAlarm(
                            agent.speechSpeedMsgId,
                            'speechSpeedMsgId',
                            agent
                          )
                        "
                      >
                        语速过快
                      </div>
                    </template>
                  </div>
                </div>
                <div class="bgBottom"></div>
              </div>

              <!-- 座席头像 -->
              <div class="seat-avatar">
                <img
                  :src="getAvatarByState(agent.currentState)"
                  alt="座席头像"
                />
              </div>
              <div class="jp" v-if="agent?.role == '1' || agent?.role == '2'">
                <img
                  v-if="agent.role == '1'"
                  src="../assets/image/zbz.png"
                  alt=""
                />
                <img
                  v-if="agent.role == '2'"
                  src="../assets/image/bz.png"
                  alt=""
                />
              </div>
              <!-- 座席姓名 -->
              <div class="seat-name">{{ agent.agentName || "--" }}</div>

              <!-- 座席编号 -->
              <div class="seat-id">{{ agent.seatNo }}</div>
              <div class="seat-id">{{ agent.agentId || "--" }}</div>

              <!-- 状态标签 -->
              <div
                class="seat-status blue"
                v-if="[2, 3, 4, 5].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status green"
                v-if="Number(agent.currentState) === 1"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status cyan"
                v-if="[6, 10].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status orange"
                v-if="[7, 8, 9].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status black"
                v-if="Number(agent.currentState) === 0"
              >
                {{ statusList[agent.currentState] }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警处理对话框 -->
    <AlarmDialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :dealObj="currentDealObj"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive } from "vue";
import AlarmDialog from "./AlarmDialog.vue";

const props = defineProps({
  currentTab: {
    type: String,
    required: true,
    default: () => "",
  },
  groupList: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  statusList: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const emit = defineEmits(["view-seat", "view-workgroup", "refresh"]);
const leftArray = ref([]);
const rightArray = ref([]);

// 告警处理相关
const dialogVisible = ref(false);
const currentDealObj = reactive({});
const currentTooltipItem = ref(null);

// 处理数组分配的函数
const distributeGroups = (groupsObj) => {
  // 确保 groupsObj 是对象且不为空
  if (
    !groupsObj ||
    typeof groupsObj !== "object" ||
    Object.keys(groupsObj).length === 0
  ) {
    leftArray.value = [];
    rightArray.value = [];
    return;
  }

  try {
    leftArray.value = [];
    rightArray.value = [];

    // 将对象转换为数组
    const groups = Object.values(groupsObj);

    groups.forEach((group, index) => {
      if (index === 0) {
        leftArray.value.push(group);
      } else if (index === 1) {
        rightArray.value.push(group);
      } else {
        // 计算左右两边的行数
        const leftRows = Math.ceil(
          leftArray.value.reduce(
            (total, g) => total + (g?.users?.length || 0),
            0
          ) / 5
        );
        const rightRows = Math.ceil(
          rightArray.value.reduce(
            (total, g) => total + (g?.users?.length || 0),
            0
          ) / 5
        );

        if (rightRows < leftRows) {
          rightArray.value.push(group);
        } else {
          leftArray.value.push(group);
        }
      }
    });
  } catch (error) {
    console.error("Error in distributeGroups:", error);
    leftArray.value = [];
    rightArray.value = [];
  }
};

// 监听 groupList 变化并更新分组
watch(
  () => props.groupList,
  (newGroups) => {
    if (newGroups && typeof newGroups === "object") {
      distributeGroups(newGroups);
    }
  },
  { immediate: true }
);

// 根据状态获取头像
const getAvatarByState = (state) => {
  const stateNum = Number(state);
  if ([2, 3, 4, 5].includes(stateNum)) {
    return require("@/assets/image/map/th.png"); // 通话
  } else if (stateNum === 1) {
    return require("@/assets/image/map/kx.png"); // 空闲
  } else if ([6, 10].includes(stateNum)) {
    return require("@/assets/image/map/hh.png"); // 话后
  } else if ([7, 8, 9].includes(stateNum)) {
    return require("@/assets/image/map/sm.png"); // 离席
  } else {
    return require("@/assets/image/map/wqr.png"); // 未签入
  }
};

// 检查是否有告警
const hasWarnings = (agent) => {
  return (
    agent.extraLongCallMsgId ||
    agent.speechSpeedMsgId ||
    agent.seekHelpMsgId ||
    agent.afterLongMsgId
  );
};

// 处理鼠标进入座席
const handleSeatEnter = (event, agent) => {
  if (hasWarnings(agent)) {
    currentTooltipItem.value = agent;
  }
};

// 处理鼠标离开座席
const handleSeatLeave = () => {
  currentTooltipItem.value = null;
};

// 处理告警
const handleAlarm = (id, type, agent) => {
  // 根据不同类型的告警，设置不同的告警类型
  let alarmType = 0;

  switch (type) {
    case "extraLongCallMsgId":
      alarmType = 1; // 超长通话
      break;
    case "afterLongMsgId":
      alarmType = 2; // 话后超时
      break;
    case "seekHelpMsgId":
      alarmType = 8; // 求助
      break;
    case "speechSpeedMsgId":
      alarmType = 4; // 语速过快
      break;
    default:
      alarmType = 0; // 默认
      break;
  }

  // 设置当前处理对象
  Object.assign(currentDealObj, {
    type: "deal",
    MSG_ID: id,
    AGENT_ID: agent.agentId,
    ROOM_LOCATION: agent.roomLocation || "",
    ALARM_TYPE: alarmType,
  });

  // 显示告警处理对话框
  dialogVisible.value = true;
};

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false;
  // 通知父组件刷新
  emit("refresh");
};

const handleView = (item) => {
  emit("view-seat", item);
};

const handleWorkGroupInfo = (item) => {
  emit("view-workgroup", item);
};
</script>

<style lang="scss" scoped>
.seat-map {
  position: relative;
  overflow: auto;
  user-select: none;
}

.seatBox {
  flex: 1;
  display: flex;
}

.skill-groups-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
  border-radius: 4px;
  width: 50%;
}

.skill-group {
  border-radius: 8px;
  padding: 16px;
  background: rgba(0, 128, 255, 0.1);
  width: 752px;
  flex: 0 0 auto;
}

.skill-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  // border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.skill-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

.skill-group-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  width: 80px;
  height: 32px;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
  }
  &:hover {
    opacity: 0.8;
  }
}

.skill-group-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: flex-start;
}

.seat-item {
  width: 138px;
  height: 156px;
  border-radius: 2.04px;
  background: rgba(0, 128, 255, 0.1);
  text-align: center;
  padding-top: 22px;
  position: relative;
  cursor: pointer;
  pointer-events: auto;
  flex: 0 0 auto;
  .jp {
    width: 20px;
    height: 24px;
    position: absolute;
    top: 0;
    right: 2px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  img {
    width: 60px;
    height: 60px;
    pointer-events: none;
    -webkit-user-drag: none;
  }
}

.is_afterLongMsgId {
  border-radius: 4px;
  background: rgba(240, 56, 56, 0.2);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(
      166deg,
      #dc0055 6%,
      rgba(220, 0, 85, 0) 46%,
      #dc0055 89%
    )
    1;
}

.click_select {
  border: 1px solid #0555ce;
}

.seat-name {
  color: #fff;
  font-size: 14px;
  margin: 2px 0px 6px 0px;
}

.seat-id {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  &:nth-child(1) {
    margin-top: 2px;
  }
}

.top-seat {
  position: absolute;
  width: 100%;
  top: 0;
  display: flex;
  justify-content: space-between;
}

.seat-box {
  flex: 1;
  box-sizing: border-box;
  padding-left: 4px;
  position: absolute;
  right: 0;
}

.seat-dot-item {
  width: 20px;
  height: 20px;
  position: relative;
  margin-right: 3px;
}

.seat-warn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: tl-h-52 800ms infinite alternate;
}

.dotCont {
  width: 8.15px;
  height: 8.15px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes tl-h-52 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.5);
  }
}

.redDotBox {
  background-color: rgba(220, 0, 85, 0.2);
}
.redDot {
  background-color: rgb(220, 0, 85);
}
.blueDotBox {
  background-color: rgba(64, 158, 255, 0.2);
}
.blueDot {
  background-color: rgb(64, 158, 255);
}
.yellowDotBox {
  background-color: rgba(230, 162, 60, 0.2);
}
.yellowDot {
  background-color: rgb(230, 162, 60);
}
.greenDotBox {
  background-color: rgba(38, 177, 101, 0.2);
}
.greenDot {
  background-color: rgb(38, 177, 101);
}

.seat-status {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 1.02px 4.07px;
  font-size: 14px;
  text-wrap: nowrap;
  position: absolute;
  line-height: 22px;
  left: 0;
  top: 0;
}

.blue {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-th.png) no-repeat center center;
  background-size: 100% 100%;
  color: #00ddff;
}

.green {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-kx.png) no-repeat center center;
  background-size: 100% 100%;
  color: #52c41a;
}

.cyan {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-hh.png) no-repeat center center;
  background-size: 100% 100%;
  color: #0080ff;
}

.orange {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-sm.png) no-repeat center center;
  background-size: 100% 100%;
  color: #fa9904;
}

.black {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-wqr.png) no-repeat center center;
  background-size: 100% 100%;
  color: #868686;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  img {
    width: 60px;
    height: 60px;
  }
  .empty-text {
    margin-top: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
  }
}

// 浮窗样式
.seat-tooltip {
  position: absolute;
  z-index: 9999;
  border-radius: 4px;
  padding: 8px 12px;
  bottom: 95%;
  margin-bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  color: #ffffff;
  flex-direction: column;
  opacity: 0;
  animation: fadeIn 0.2s forwards;

  .seat-item:hover & {
    opacity: 1;
  }

  .seat-item:not(:hover) & {
    animation: fadeOut 1s forwards;
  }

  * {
    pointer-events: auto;
  }

  .bgContent {
    background: url(../assets/image/redtop.png) no-repeat center center;
    background-size: 100% 100%;
    width: 120px;
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
    padding: 8px 8px 4px;
  }

  .bgBottom {
    background: url(../assets/image/redbottom.png) no-repeat center center;
    background-size: 100% 100%;
    height: 12px;
    width: 120px;
    margin-top: -1px;
    position: relative;
    padding: 0 8px;
  }

  .tooltip-content {
    align-items: center;
    justify-content: center;
    .tooltip-title {
      width: 120px;
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      text-align: center;
      &:hover {
        color: #00ddff;
        background: rgba(172, 15, 44, 0.2);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
