const receiptStateOptionsCall = [//外呼结果
    { value: "0", label: "成功" },
    { value: "1", label: "无人应答" },
    { value: "2", label: "用户忙" },
    { value: "3", label: "用户挂机" },
    { value: "4", label: "网络忙" },
    { value: "5", label: "空号" },
    { value: "6", label: "拒接" },
    { value: "7", label: "关机" },
    { value: "8", label: "停机" },
    { value: "9", label: "不在服务区" },
    { value: "999", label: "其他呼叫失败" },
  ];
  const messageTypeOptions = [//消息类别选项（用于助手消息筛选）
    { value: "0", label: "话后超时" },
    { value: "1", label: "超长通话" },
    { value: "2", label: "语速过快" },
    { value: "3", label: "求助" },
    { value: "4", label: "其它" },
    { value: "5", label: "转派" },
    { value: "6", label: "公告" },
    { value: "7", label: "静默" },
    { value: "8", label: "抢话" },
    { value: "9", label: "坐席违规词" },
    { value: "10", label: "市民敏感词" },
];
const sendStateOptions = [// 发送状态选项
    { value: "0", label: "待发送" },
    { value: "1", label: "发送中" },
    { value: "2", label: "发送成功" },
    { value: "3", label: "发送失败" },
  ];
  
  const receiptStateOptions = [//回执状态
    { value: "0", label: "失败" },
    { value: "1", label: "成功" },
    { value: "2", label: "未知" },
  ];

// 获取前一天零点的时分秒（格式："YYYY-MM-DD 00:00:00"）
function defaultStartTime() {
  const now = new Date();
  now.setDate(now.getDate() - 1);
  const pad = (n) => n.toString().padStart(2, "0");
  const y = now.getFullYear();
  const m = pad(now.getMonth() + 1);
  const d = pad(now.getDate());
  return `${y}-${m}-${d} 00:00:00`;
}
// 获取当天零点的时分秒（格式："YYYY-MM-DD 00:00:00"）
function todayStartTime() {
  const now = new Date();
  now.setHours(0, 0, 0, 0);
  const pad = (n) => n.toString().padStart(2, "0");
  const y = now.getFullYear();
  const m = pad(now.getMonth() + 1);
  const d = pad(now.getDate());
  return `${y}-${m}-${d} 00:00:00`;
}

export { receiptStateOptionsCall, messageTypeOptions, sendStateOptions, receiptStateOptions, defaultStartTime, todayStartTime };