import { createRouter, createWebHashHistory } from "vue-router";

// 导入页面组件
const homePage = () => import("../views/homePage.vue");

// 定义路由
const routes = [
  {
    path: "/",
    name: "homePage",
    component: homePage,
    meta: {
      title: "首页",
      keepAlive: true,
    },
  },
  // 添加一个捕获所有未匹配路由的路由
  {
    path: "/:pathMatch(.*)*",
    redirect: "/",
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || "坐席监控系统";
  next();
});

export default router;
