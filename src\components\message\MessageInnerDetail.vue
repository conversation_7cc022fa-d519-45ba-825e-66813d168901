<template>
  <div class="content-box">
    <!-- 筛选条件区域 -->
    <div class="filter-container">
      <div class="filter-item">
        <span>坐席姓名</span>
        <el-input
          v-model="filterParams.agentName"
          clearable
          placeholder="请输入"
        ></el-input>
      </div>
      <div class="filter-item">
        <span>坐席工号</span>
        <el-input
          v-model="filterParams.agentNo"
          clearable
          placeholder="请输入"
        ></el-input>
      </div>
      <div class="filter-item">
        <span>话机号码</span>
        <el-input
          v-model="filterParams.agentPhone"
          clearable
          placeholder="请输入"
        ></el-input>
      </div>
      <div class="filter-item">
        <span>提醒类型</span>
        <el-select
          v-model="filterParams.msgRemindType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in remindTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="filter-item">
        <span>送达状态</span>
        <el-select
          v-model="filterParams.msgSendStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in sendStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="filter-item">
        <span>阅读状态</span>
        <el-select
          v-model="filterParams.msgReadStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in readStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="filter-buttons">
        <div class="filter-button" @click="handleSearch"></div>
        <div class="filter-button" @click="handleReset"></div>
        <div class="line"></div>
        <div class="filter-button" @click="handleExport"></div>
      </div>
    </div>

    <!-- 消息明细表格 -->
    <div class="message-detail-info">
      <!-- 表格形式展示数据 -->
      <div class="message-table-container">
        <el-table
          v-if="info.detailList && info.detailList.length"
          :data="info.detailList"
          style="width: 100%; height: 100%"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
          :border="false"
        >
          <el-table-column prop="AGENT_NAME" label="坐席姓名" min-width="100" />
          <el-table-column prop="AGENT_NO" label="坐席工号" min-width="100" />
          <el-table-column prop="AGENT_SEAT" label="坐席位号" min-width="100" />
          <el-table-column
            prop="AGENT_GROUP"
            label="所在班组"
            min-width="120"
          />
          <el-table-column
            prop="AGENT_PHONE"
            label="话机号码"
            min-width="120"
          />
          <el-table-column label="消息类型" min-width="100">
            <template #default="scope">
              <span class="success-text">{{
                getRemindTypeText(scope.row.MSG_REMIND_TYPE)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="送达状态" min-width="100">
            <template #default="scope">
              <span :class="getSendStatusClass(scope.row.MSG_SEND_STATUS)">
                {{ getSendStatusText(scope.row.MSG_SEND_STATUS) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="阅读状态" min-width="100">
            <template #default="scope">
              <span :class="getReadStatusClass(scope.row.MSG_READ_STATUS)">
                {{ getReadStatusText(scope.row.MSG_READ_STATUS) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="MSG_SEND_TIME"
            label="送达时间"
            min-width="180"
          />
          <el-table-column
            prop="MSG_READ_TIME"
            label="已读时间"
            min-width="180"
          />
        </el-table>

        <div v-else class="empty-state">
          <img src="../../assets/image/empty.png" alt="" class="empty-icon" />
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="info.total || 0"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import {
  defineProps,
  defineEmits,
  reactive,
  watch,
  computed,
  onBeforeUnmount,
} from "vue";
const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["refresh"]);

// 当前页码和每页条数
const currentPage = computed(() => props.info?.currentParams?.page || 1);
const pageSize = computed(() => props.info?.currentParams?.limit || 10);

// 筛选参数
const filterParams = reactive({
  agentName: "",
  agentNo: "",
  agentPhone: "",
  msgRemindType: "",
  msgSendStatus: "",
  msgReadStatus: "",
});

// 监听父组件传入的筛选参数
watch(
  () => props.info?.currentParams,
  (newParams) => {
    if (newParams) {
      filterParams.agentName = newParams.agentName || "";
      filterParams.agentNo = newParams.agentNo || "";
      filterParams.agentPhone = newParams.agentPhone || "";
      filterParams.msgRemindType = newParams.msgRemindType || "";
      filterParams.msgSendStatus = newParams.msgSendStatus || "";
      filterParams.msgReadStatus = newParams.msgReadStatus || "";
    }
  },
  { immediate: true, deep: true }
);

// 表格样式
const headerCellStyle = {
  fontWeight: "normal",
};

const cellStyle = {
  background: "rgba(0, 128, 255, 0.1)",
  color: "#ffffff",
  borderColor: "rgba(0, 128, 255, 0.3)",
};

// 提醒类型选项
const remindTypeOptions = [
  {
    label: "公告",
    value: "6",
  },
  {
    label: "话后超时",
    value: "0",
  },
  {
    label: "超长通话",
    value: "1",
  },
  {
    label: "语速过快",
    value: "2",
  },
  {
    label: "求助",
    value: "3",
  },
  {
    label: "转派",
    value: "5",
  },
  {
    label: "静默",
    value: "7",
  },
  {
    label: "抢话",
    value: "8",
  },
  {
    label: "坐席违规词",
    value: "9",
  },
  {
    label: "市民敏感词",
    value: "10",
  },
  {
    label: "其他",
    value: "4",
  },
];

// 送达状态选项
const sendStatusOptions = [
  { value: "0", label: "已送达" },
  { value: "1", label: "未送达" },
];

// 阅读状态选项
const readStatusOptions = [
  { value: "0", label: "未读" },
  { value: "1", label: "已读" },
];

// 获取提醒类型文本
const getRemindTypeText = (type) => {
  const types = {
    0: "话后超时",
    1: "超长通话",
    2: "语速过快",
    3: "求助",
    4: "其它",
    5: "转派",
    6: "公告",
    7: "静默",
    8: "抢话",
    9: "坐席违规词",
    10: "市民敏感词",
    11: "首发诉求",
  };
  return types[type] || "未知";
};

// 获取送达状态文本
const getSendStatusText = (status) => {
  return status === "0" ? "已送达" : status === "1" ? "未送达" : "未知";
};

// 获取送达状态类名
const getSendStatusClass = (status) => {
  return status === "0"
    ? "success-text"
    : status === "1"
    ? "warning-text"
    : "error-text";
};

// 获取阅读状态文本
const getReadStatusText = (status) => {
  return status === "0" ? "未读" : status === "1" ? "已读" : "未知";
};

// 获取阅读状态类名
const getReadStatusClass = (status) => {
  return status === "0" ? "warning-text" : status === "1" ? "success-text" : "";
};

// 处理查询
const handleSearch = () => {
  emit("refresh", {
    page: 1,
    limit: pageSize.value,
    ...filterParams,
  });
};

// 处理重置
const handleReset = () => {
  Object.keys(filterParams).forEach((key) => {
    filterParams[key] = "";
  });
  emit("refresh", {
    page: 1,
    limit: pageSize.value,
    ...filterParams,
  });
};

let exportTimer = null;

// 处理导出功能
const handleExport = () => {
  console.log(filterParams);

  // 构建查询参数
  const queryParams = new URLSearchParams({
    ...filterParams,
    msgId: props.info.MSG_ID,
    timestamp: Date.now(), // 添加时间戳防止缓存
  }).toString();

  // 从当前URL中获取基础路径
  const currentUrl = window.location.href;
  const baseURL = currentUrl.split("/")[0] + "//" + currentUrl.split("/")[2];
  const exportUrl = `${baseURL}/aiamgr/msgInfo/export.do?${queryParams}`;

  // 创建一个隐藏的a标签并触发点击
  const link = document.createElement("a");
  link.href = exportUrl;
  link.target = "_blank"; // 在新窗口打开
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 停止定时器
const stopExportTimer = () => {
  if (exportTimer) {
    clearInterval(exportTimer);
    exportTimer = null; 
  }
};

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  stopExportTimer();
});

// 处理分页大小变化
const handleSizeChange = (size) => {
  emit("refresh", {
    page: 1,
    limit: size,
    ...filterParams,
  });
};

// 处理当前页变化
const handleCurrentChange = (page) => {
  emit("refresh", {
    page,
    limit: pageSize.value,
    ...filterParams,
  });
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  /* 筛选条件样式 */
  .filter-container {
    // max-width: 1212px;
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    position: relative;
    box-sizing: border-box;

    .filter-item {
      display: flex;
      align-items: center;
      color: #fff;

      span {
        margin-right: 8px;
        white-space: nowrap;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
      }

      :deep(.el-input) {
        width: 160px !important;
      }

      :deep(.el-select) {
        width: 160px !important;
      }

      :deep(.el-input__wrapper) {
        width: 100%;
      }

      :deep(.el-input__inner::placeholder) {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .filter-buttons {
      display: flex;
      gap: 8px;
      margin-left: auto;
      align-items: center;
      .filter-button {
        width: 80px;
        height: 32px;

        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        &:active {
          transform: scale(0.9);
        }
        &:nth-child(1) {
          background: url("../../assets/image/search.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        &:nth-child(2) {
          background: url("../../assets/image/reset.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        &:nth-child(4) {
          background: url("../../assets/image/exportbtn.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
      }

      .line {
        width: 1px;
        background-color: rgba(0, 255, 255, 0.1);
        height: 24px;
      }
    }
  }

  /* 消息明细信息样式 */
  .message-detail-info {
    width: 100%;
    height: calc(100% - 80px);
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;

    .message-detail-header {
      padding: 16px;
      background: rgba(0, 128, 255, 0.1);
      border-radius: 8px;
      margin-bottom: 16px;

      .message-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .message-subtitle {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 16px;
      }

      .message-stats {
        display: flex;
        gap: 24px;

        .stat-item {
          .label {
            color: rgba(255, 255, 255, 0.6);
            margin-right: 4px;
          }

          .value {
            color: #00ffff;
            font-weight: bold;
          }
        }
      }
    }

    /* 表格容器样式 */
    .message-table-container {
      width: 100%;
      height: 100%;
      overflow-x: auto;
      flex: 1;

      :deep(.el-table) {
        background-color: transparent;
        border-color: transparent;
        height: 100%;

        &::before {
          display: none;
        }

        .el-table__inner-wrapper::before {
          display: none;
        }

        .el-table__border-left-patch {
          display: none;
        }

        .el-table__border-right-patch {
          display: none;
        }

        .el-table__cell {
          border: none;
        }

        border-radius: 4px;
        .success-text {
          display: inline-block;
          color: rgb(0, 220, 85);
          background: rgba(0, 220, 85, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }

        .warning-text {
          color: #ffaa00;
          display: inline-block;
          background: rgba(255, 170, 0, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }

        .error-text {
          color: #dc0055;
          display: inline-block;
          background: rgba(220, 0, 85, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }

    .empty-state {
      margin-top: 20vh;
      text-align: center;

      .empty-icon {
        width: 308px;
        height: 252px;
      }

      .empty-text {
        font-size: 14px;
        color: #fff;
      }
    }
  }

  /* 分页样式 */
  .pagination {
    position: relative;
    bottom: 0;
    margin-bottom: 8px;
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    :deep(.el-pagination) {
      .el-input__inner {
        color: #fff;
      }
      .is-active {
        border-radius: 4px;
        background: linear-gradient(
          180deg,
          rgba(0, 128, 255, 0.3) 2%,
          rgba(0, 128, 255, 0.2) 100%
        ) !important;
        background-color: none !important;
        box-sizing: border-box;
        border: 1px solid #0080ff;
        box-shadow: inset 0px 0px 12px 0px #0080ff;
      }
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }

      .el-select {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        height: 32px;
        color: rgba(0, 255, 255, 1);
      }
      .el-select__selected-item {
        color: rgba(0, 255, 255, 1);
      }

      .el-pager {
        gap: 8px;
        margin-left: 8px;
        margin-right: 8px;
      }
      .number,
      .more {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        width: 32px;
        height: 32px;
        color: #fff;
      }
      .btn-prev,
      .btn-next {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        width: 32px;
        height: 32px;
        color: #fff;
      }
    }
  }
}
</style>
