<template>
  <div class="template-add-or-edit">
    <div class="box">
      <div class="template-type-box">
        <div class="template-type-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">模板类型</div>
        </div>
        <div class="template-type-box-navigation">
          <div
            class="arrow-btn left-arrow"
            @click="handlePrevTemplate"
            :class="{ disabled: !canNavigatePrev }"
          >
            <img src="../../assets/image/arrow-icon.png" alt="" />
          </div>
          <div class="template-type-box-content" ref="templateContainer">
            <div
              class="template-type-box-content-item"
              v-for="item in templateList"
              :key="item.ID"
              :class="{ active: currentTemplateId === item.ID }"
              @click="handleSelectTemplate(item.ID)"
              :ref="
                (el) => {
                  if (currentTemplateId === item.ID) activeTemplateRef = el;
                }
              "
            >
              <img
                class="activeRT"
                src="../../assets/image/active-right-top.png"
                alt=""
                v-if="currentTemplateId === item.ID"
              />
              <div class="template-type-box-content-item-title">
                {{ item.TEMPLATE_NAME }}
              </div>
              <div class="template-type-box-content-item-icon">
                <img
                  v-if="currentTemplateId === item.ID"
                  :src="
                    item.TEMPLATE_TYPE == 1
                      ? smsInfoActive
                      : item.TEMPLATE_TYPE == 2
                      ? callInfoActive
                      : assistantInfoActive
                  "
                  alt=""
                />
                <img
                  v-else
                  :src="
                    item.TEMPLATE_TYPE == 1
                      ? smsInfo
                      : item.TEMPLATE_TYPE == 2
                      ? callInfo
                      : assistantInfo
                  "
                  alt=""
                />
              </div>
            </div>
          </div>
          <div
            class="arrow-btn right-arrow"
            @click="handleNextTemplate"
            :class="{ disabled: !canNavigateNext }"
          >
            <img src="../../assets/image/arrow-icon.png" alt="" />
          </div>
        </div>
      </div>

      <div class="template-content-box" v-if="templateType !== '0'">
        <div class="template-content-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">模板参数</div>
        </div>
        <div class="template-content-box-content">
          <div class="fromBox">
            <!-- 参数表单 -->
            <div class="params-container" v-if="paramsArray.length > 0">
              <div
                class="fromItem"
                v-for="param in paramsArray"
                :key="param.key"
              >
                <div class="fromItem-title">参数({{ param.value }})</div>
                <div class="fromItem-input">
                  <el-input
                    type="text"
                    placeholder="请输入"
                    v-model="param.inputValue"
                  />
                </div>
              </div>
            </div>
            <div class="fromItem">
              <div class="fromItem-title">模板名称</div>
              <div class="fromItem-input">
                <div class="preview-content" v-html="previewContent"></div>
              </div>
            </div>
            <!-- 系统参数表单 -->
            <div class="params-container" v-if="systemParamsArray.length > 0">
              <div
                class="fromItem"
                v-for="param in systemParamsArray"
                :key="param.key"
              >
                <div class="fromItem-title">系统参数({{ param.value }})</div>
                <div class="fromItem-input">
                  <el-input type="text" placeholder="自动填充" disabled />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="template-content-box" v-if="templateType === '0'">
        <div class="template-content-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">消息内容</div>
        </div>
        <div class="template-content-box-content">
          <div class="fromBox">
            <div class="fromItem">
              <div class="fromItem-title">消息名称</div>
              <div class="fromItem-input">
                <el-input
                  type="text"
                  placeholder="请输入"
                  v-model="templateName"
                />
              </div>
            </div>
            <div class="fromItem">
              <div class="fromItem-title">主题</div>
              <div class="fromItem-input">
                <el-input
                  type="text"
                  placeholder="请输入"
                  v-model="templateSubject"
                />
              </div>
            </div>
            <div class="fromItem">
              <div class="fromItem-title">详细信息</div>
              <div class="fromItem-input">
                <el-input
                  type="textarea"
                  placeholder="请输入"
                  show-word-limit
                  maxlength="50"
                  :rows="4"
                  v-model="detail"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="template-message-box" v-if="templateType === '0'">
        <div class="template-message-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">消息类型</div>
        </div>
        <div class="template-message-box-content">
          <div
            class="template-message-box-content-item"
            v-for="item in msgTypeList"
            :key="item.value"
            :class="{ active: msgType === item.value }"
            @click="handlemsgType(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="template-content-box">
        <div class="template-content-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">班组</div>
        </div>
        <div class="template-content-box-content">
          <div class="fromBox">
            <div class="fromItem">
              <div class="fromItem-title">选择班组</div>
              <div class="fromItem-input">
                <el-select
                  v-model="workGroupIds"
                  multiple
                  filterable
                  placeholder="请选择"
                  v-if="templateType === '0'"
                >
                  <el-option
                    v-for="item in workGroupOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-select
                  v-model="workGroupIds"
                  multiple
                  filterable
                  placeholder="请选择"
                  v-else
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="template-content-box">
        <div class="template-content-box-title">
          <img src="../../assets/image/titleLeft.png" alt="" />
          <div class="fontStyle">发送时间</div>
        </div>
        <div class="radioBox">
          <div
            class="radioItem"
            :class="{ active: sendType == '1' }"
            @click="handleRadioChange('1')"
          >
            <div class="fontStyle">立即发送</div>
          </div>
          <div
            class="radioItem"
            :class="{ active: sendType == '2' }"
            @click="handleRadioChange('2')"
          >
            <div class="fontStyle">指定时间</div>
          </div>
        </div>
        <div class="timeBox" v-if="sendType == '2'">
          <div class="timeItem">
            <div style="width: 100%">
              <el-date-picker
                v-model="sendTime"
                style="width: 100%"
                type="datetime"
                placeholder="选择日期时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="template-footer">
      <div
        class="save-template"
        :style="{ visibility: templateType === '0' ? 'visible' : 'hidden' }"
        @click="handleSaveAsNewTemplate"
      >
        <img src="../../assets/image/saveicon.png" alt="" />
        <div>存为新模板</div>
      </div>
      <div style="display: flex">
        <div class="template-footer-close" @click="emit('close')">
          <img src="../../assets/image/closeBtn.png" alt="" />
        </div>
        <div class="template-footer-sumbit" @click="handleConfirm">
          <div class="fontStyle">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  defineEmits,
  defineProps,
  watch,
  onMounted,
  computed,
  nextTick,
} from "vue";
import callInfo from "../../assets/image/call-icon.png";
import smsInfo from "../../assets/image/sms-icon.png";
import assistantInfo from "../../assets/image/assistant-icon.png";
import callInfoActive from "../../assets/image/call-icon-active.png";
import smsInfoActive from "../../assets/image/sms-icon-active.png";
import assistantInfoActive from "../../assets/image/assistant-icon-active.png";
import {
  // addTemplate,
  getTemplateDetail,
  getTemplateList,
  createSmsMsg,
  createCallMsg,
  createAssistantMsg,
  saveNewTemplate,
  getWordList,
} from "../../api/index";
import { ElMessage } from "element-plus";

const props = defineProps({
  templateId: {
    type: String,
    default: "",
  },
  templateType: {
    type: String,
    default: "",
  },
  messageType: {
    type: Number,
    default: 0,
  },
  options: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["close", "confirm"]);
console.log(emit, "emit");

const templateType = ref("0");
const msgType = ref("1");
const sendType = ref("1");
const templateName = ref("");
const templateSubject = ref("");
const smsTemplateId = ref("");
const detail = ref("");
const isEdit = ref(false); // 判断是新增还是修改
const templateList = ref([]); // 存储模板列表
const selectedTemplateId = ref(""); // 选中的模板ID
const currentTemplateId = ref(""); // 当前选中的模板ID
const currentTemplateIndex = ref(0); // 当前选中模板的索引
const templateContainer = ref(null); // 模板容器的引用
const activeTemplateRef = ref(null); // 当前活动模板的引用
const sendTime = ref(""); // 发送时间
const workGroupIds = ref([]); // 班组ID
const workGroupOptions = ref([]); // 存储从getWordList获取的班组数据

// 监听当前选中的模板ID变化，确保其在可视区域内
watch(currentTemplateId, () => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    scrollActiveTemplateIntoView();
  });
});

// 将当前选中的模板滚动到可视区域内
const scrollActiveTemplateIntoView = () => {
  if (activeTemplateRef.value && templateContainer.value) {
    const container = templateContainer.value;
    const activeItem = activeTemplateRef.value;

    // 获取容器和选中项的位置信息
    const containerRect = container.getBoundingClientRect();
    const activeItemRect = activeItem.getBoundingClientRect();

    // 判断选中项是否在容器的可视区域外
    if (
      activeItemRect.left < containerRect.left ||
      activeItemRect.right > containerRect.right
    ) {
      // 计算需要滚动的位置，使选中项居中显示
      const scrollLeft =
        activeItem.offsetLeft -
        container.clientWidth / 2 +
        activeItem.offsetWidth / 2;

      // 平滑滚动到计算出的位置
      container.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      });
    }
  }
};

// 存储提取的参数
const paramsArray = ref([]); // 存储 {xxx} 格式的参数
const systemParamsArray = ref([]); // 存储 [xxx] 格式的参数

// 计算替换后的预览内容
const previewContent = computed(() => {
  let content = detail.value;

  // 替换用户输入的参数
  paramsArray.value.forEach((param) => {
    if (param.inputValue) {
      const replacedText = `<span style="color: #00FFFF;">${param.inputValue}</span>`;
      content = content.replace(param.original, replacedText);
    }
  });

  // 替换系统参数（这里只是用红色标记，实际值会由系统填充）
  systemParamsArray.value.forEach((param) => {
    const replacedText = `<span style="color: #ffdc00;">[${param.value}]</span>`;
    content = content.replace(param.original, replacedText);
  });

  return content;
});

// 从模板内容中提取参数
const extractParams = (content) => {
  paramsArray.value = [];
  systemParamsArray.value = [];

  if (!content) return;

  // 提取 {xxx} 格式的参数
  const paramRegex = /\{([^}]+)\}/g;
  let paramMatch;
  let paramIndex = 0;

  while ((paramMatch = paramRegex.exec(content)) !== null) {
    const paramName = paramMatch[1];
    const paramKey = `PARAMS_${paramIndex}`;
    paramsArray.value.push({
      key: paramKey,
      value: paramName,
      original: `{${paramName}}`,
      inputValue: "", // 添加用户输入值
    });
    paramIndex++;
  }

  // 提取 [xxx] 格式的系统参数
  const systemRegex = /\[([^\]]+)\]/g;
  let systemMatch;
  let systemIndex = 0;

  while ((systemMatch = systemRegex.exec(content)) !== null) {
    const systemName = systemMatch[1];
    const systemKey = `SYSTEM_${systemIndex}`;
    systemParamsArray.value.push({
      key: systemKey,
      value: systemName,
      original: `[${systemName}]`,
    });
    systemIndex++;
  }

  console.log("提取的参数:", paramsArray.value);
  console.log("提取的系统参数:", systemParamsArray.value);
};

// 监听参数值变化
watch(
  () => paramsArray.value,
  () => {
    console.log("参数值变化", paramsArray.value);
  },
  { deep: true }
);

const msgTypeList = ref([
  {
    label: "公告",
    value: "6",
  },
  {
    label: "话后超时",
    value: "0",
  },
  {
    label: "超长通话",
    value: "1",
  },
  {
    label: "语速过快",
    value: "2",
  },
  {
    label: "求助",
    value: "3",
  },
  {
    label: "转派",
    value: "5",
  },
  {
    label: "静默",
    value: "7",
  },
  {
    label: "抢话",
    value: "8",
  },
  {
    label: "坐席违规词",
    value: "9",
  },
  {
    label: "市民敏感词",
    value: "10",
  },
  {
    label: "其他",
    value: "4",
  },
]);

// 计算是否可以导航到上一个模板
const canNavigatePrev = computed(() => {
  return currentTemplateIndex.value > 0;
});

// 计算是否可以导航到下一个模板
const canNavigateNext = computed(() => {
  return (
    templateList.value.length > 0 &&
    currentTemplateIndex.value < templateList.value.length - 1
  );
});

// 处理导航到上一个模板
const handlePrevTemplate = () => {
  if (!canNavigatePrev.value) return;
  currentTemplateIndex.value--;
  const prevTemplate = templateList.value[currentTemplateIndex.value];
  handleSelectTemplate(prevTemplate.ID);
  // 滚动到当前选中项
  nextTick(() => {
    scrollActiveTemplateIntoView();
  });
};

// 处理导航到下一个模板
const handleNextTemplate = () => {
  if (!canNavigateNext.value) return;
  currentTemplateIndex.value++;
  const nextTemplate = templateList.value[currentTemplateIndex.value];
  handleSelectTemplate(nextTemplate.ID);
  // 滚动到当前选中项
  nextTick(() => {
    scrollActiveTemplateIntoView();
  });
};

// 处理选择模板
const handleSelectTemplate = (id) => {
  console.log("选择模板:", id);
  if (id) {
    currentTemplateId.value = id;
    // 更新当前模板索引
    const index = templateList.value.findIndex((item) => item.ID === id);
    if (index !== -1) {
      currentTemplateIndex.value = index;
    }
    getTemplateDetailData(id);
  }
};

const handleRadioChange = (value) => {
  sendType.value = value;
};

// 根据消息类型获取模板列表
const getTemplateListByType = async (type) => {
  try {
    const params = {
      pageType: "3",
      pageIndex: "1",
      pageSize: "999",
      templateType: String(type),
    };

    const res = await getTemplateList(params);

    if (res.data && res.data.data) {
      templateList.value = res.data.data;

      // 如果有模板数据，自动获取第一个模板的详情
      if (templateList.value.length > 0) {
        const firstTemplate = templateList.value[0];
        console.log("自动获取第一个模板详情:", firstTemplate);
        selectedTemplateId.value = firstTemplate.ID; // 设置选中的模板ID
        currentTemplateId.value = firstTemplate.ID; // 同时设置当前选中的模板ID，使UI显示选中状态
        currentTemplateIndex.value = 0; // 设置当前选中模板的索引
        await getTemplateDetailData(firstTemplate.ID);

        // 确保第一个模板在视图中
        nextTick(() => {
          scrollActiveTemplateIntoView();
        });
      }
    } else {
      console.log("未获取到模板列表数据");
      templateList.value = [];
      selectedTemplateId.value = ""; // 清空选中的模板ID
      currentTemplateId.value = ""; // 清空当前选中的模板ID
      currentTemplateIndex.value = 0; // 设置当前选中模板的索引
    }
  } catch (error) {
    console.error("获取模板列表失败:", error);
    ElMessage.error("获取模板列表失败");
    templateList.value = [];
    selectedTemplateId.value = ""; // 清空选中的模板ID
    currentTemplateId.value = ""; // 清空当前选中的模板ID
    currentTemplateIndex.value = 0; // 设置当前选中模板的索引
  }
};

// 监听传入的消息类型
watch(
  () => props.messageType,
  (newVal) => {
    if (newVal !== undefined) {
      // 将数字类型转为字符串类型
      templateType.value = String(newVal);
      // 根据消息类型获取模板列表
      getTemplateListByType(newVal);
    }
  },
  { immediate: true }
);

// 监听传入的模板类型
watch(
  () => props.templateType,
  (newVal) => {
    if (newVal) {
      templateType.value = newVal;
    }
  },
  { immediate: true }
);

const handlemsgType = (value) => {
  msgType.value = value;
};

// 获取模板详情
const getTemplateDetailData = async (id) => {
  try {
    const res = await getTemplateDetail({
      templateId: id,
      templateType: templateType.value,
    });
    console.log("获取模板详情结果:", res);
    const templateData = res.data.data;
    // 如果没有传入模板类型或者需要覆盖传入的模板类型，则使用接口返回的模板类型
    if (
      !props.templateType ||
      templateType.value !== templateData.TEMPLATE_TYPE
    ) {
      templateType.value = templateData.TEMPLATE_TYPE || "0";
    }
    console.log(
      "当前模板类型:",
      templateType.value,
      "（1-短信，2-外呼，0-助手）"
    );

    templateName.value = templateData.TEMPLATE_NAME || "";
    templateSubject.value = templateData.TEMPLATE_SUBJECT || "";
    smsTemplateId.value = templateData.SMS_TEMPLATE_ID || "";
    detail.value = templateData.DETAIL || "";
    msgType.value = templateData.MESSAGE_TYPE || "1";
    isEdit.value = false; // 这里是新建消息，不是编辑模板

    // 提取参数
    extractParams(detail.value);
  } catch (error) {
    console.error("获取模板详情失败:", error);
    ElMessage.error(error.message || "获取模板详情失败");
  }
};

// 监听模板ID变化
watch(
  () => props.templateId,
  (newVal) => {
    if (newVal) {
      getTemplateDetailData(newVal);
    }
  },
  { immediate: true }
);

// 获取班组数据
const getWorkGroupData = async () => {
  try {
    const res = await getWordList({
      type: "workGroup", // 假设type参数为workGroup，请根据实际接口参数调整
    });
    console.log("获取班组数据结果:", res);
    if (res.data && res.data.data) {
      // 转换接口返回的数据为下拉框需要的格式
      workGroupOptions.value = res.data.data.map((item) => ({
        label: item.AGENTWORKGROUP || item.NAME, // 根据实际接口返回的字段调整
        value: item.WORKGROUPID || item.WORD_ID, // 根据实际接口返回的字段调整
      }));

      workGroupOptions.value.unshift({
        label: "全部",
        value: "-1",
      });

      console.log("班组数据:", workGroupOptions.value);
    } else {
      console.log("未获取到班组数据");
      workGroupOptions.value = [];
    }
  } catch (error) {
    console.error("获取班组数据失败:", error);
    ElMessage.error("获取班组数据失败");
    workGroupOptions.value = [];
  }
};

// 监听模板类型变化，当为助手消息时获取班组数据
watch(
  () => templateType.value,
  (newVal) => {
    if (newVal === "0") {
      // 当模板类型为助手消息时，获取班组数据
      getWorkGroupData();
    }
  }
);

onMounted(() => {
  // 如果已有消息类型，则获取对应的模板列表
  if (props.messageType !== undefined) {
    getTemplateListByType(props.messageType);
  }

  // 如果初始模板类型为助手消息，则获取班组数据
  if (templateType.value === "0") {
    getWorkGroupData();
  }
});

const handleConfirm = async () => {
  console.log(
    "确认发送，当前模板类型:",
    templateType.value,
    "（1-短信，2-外呼，0-助手）"
  );

  // 替换模板中的参数为用户输入的值（不带HTML标签）
  let finalContent = detail.value;
  paramsArray.value.forEach((param) => {
    if (param.inputValue) {
      finalContent = finalContent.replace(param.original, param.inputValue);
    }
  });

  try {
    let res;

    // 根据模板类型选择调用不同的接口和使用不同的参数格式
    if (templateType.value == "0") {
      // 助手消息
      const assistantParams = {
        templateId: currentTemplateId.value, // 助手消息为新建，无模板ID
        templateName: templateName.value,
        templateSubject: templateSubject.value,
        detail: finalContent,
        msgType: msgType.value,
        workGroupIds: workGroupIds.value.join(","),
        sendType: sendType.value == "1" ? "0" : "1", // 1-立即发送  2-指定时间
      };

      // 只有当选择指定时间发送时，才添加sendTime参数
      if (sendType.value === "2" && sendTime.value) {
        assistantParams.sendTime = sendTime.value;
      }

      console.log("创建助手消息参数:", assistantParams);
      console.log("调用助手消息接口");
      res = await createAssistantMsg(assistantParams);

      if (res.data.state === 1) {
        ElMessage.success("消息发送成功");
        emit("confirm", { success: true });
        emit("close");
      } else {
        ElMessage.error(res.data?.msg || "消息发送失败");
      }
    } else if (templateType.value === "2") {
      // 外呼消息
      const callParams = {
        templateId: currentTemplateId.value,
        content: finalContent,
        workGroupIds: workGroupIds.value.join(","),
        sendType: sendType.value, // 1-立即发送  2-指定时间
      };

      // 只有当选择指定时间发送时，才添加sendTime参数
      if (sendType.value === "2" && sendTime.value) {
        callParams.sendTime = sendTime.value;
      }

      console.log("创建外呼消息参数:", callParams);
      console.log("调用外呼消息接口");
      res = await createCallMsg(callParams);

      if (res.data.state === 1) {
        ElMessage.success("消息发送成功");
        emit("confirm", { success: true });
        emit("close");
      } else {
        ElMessage.error(res.data?.msg || "消息发送失败");
      }
    } else {
      // 短信消息
      const smsParams = {
        templateId: currentTemplateId.value,
        content: finalContent,
        workGroupIds: workGroupIds.value.join(","),
        sendType: sendType.value, // 1-立即发送  2-指定时间
      };

      // 只有当选择指定时间发送时，才添加sendTime参数
      if (sendType.value === "2" && sendTime.value) {
        smsParams.sendTime = sendTime.value;
      }

      console.log("创建短信消息参数:", smsParams);
      console.log("调用短信消息接口");
      res = await createSmsMsg(smsParams);

      if (res.data.state === 1) {
        ElMessage.success("消息发送成功");
        emit("confirm", { success: true });
        emit("close");
      } else {
        ElMessage.error(res.data?.msg || "消息发送失败");
      }
    }

    console.log("创建消息结果:", res);
  } catch (error) {
    console.error("消息发送失败:", error);
    ElMessage.error(error.message || "消息发送失败");
  }
};

const handleSaveAsNewTemplate = async () => {
  // 验证必填字段
  if (!templateName.value) {
    ElMessage.warning("请输入模板名称");
    return;
  }

  if (!detail.value) {
    ElMessage.warning("请输入详细信息");
    return;
  }

  // 如果是助手消息，需要选择消息类型
  if (templateType.value === "0" && !msgType.value) {
    ElMessage.warning("请选择消息类型");
    return;
  }

  // 替换模板中的参数为用户输入的值（不带HTML标签）
  let finalContent = detail.value;
  paramsArray.value.forEach((param) => {
    if (param.inputValue) {
      finalContent = finalContent.replace(param.original, param.inputValue);
    }
  });

  // 构建参数对象，根据接口要求
  const params = {
    templateName: templateName.value,
    templateSubject: templateSubject.value || "", // 确保不传undefined
    detail: finalContent,
    msgType: msgType.value,
    templateType: templateType.value, // 添加模板类型
  };

  console.log("保存新模板参数:", params);

  try {
    const res = await saveNewTemplate(params);

    console.log("保存新模板结果:", res);
    if (res.data && res.data.state === 1) {
      ElMessage.success("新模板保存成功");
      // 不关闭当前对话框，让用户可以继续编辑或发送

      // 如果需要，可以刷新模板列表
      if (templateType.value) {
        getTemplateListByType(templateType.value);
      }
    } else {
      ElMessage.error(res.data?.msg || "保存新模板失败");
    }
  } catch (error) {
    console.error("保存新模板失败:", error);
    ElMessage.error(error.message || "保存新模板失败");
  }
};
</script>

<style lang="scss" scoped>
.template-add-or-edit {
  width: 652px;
  height: 100%;
  .box {
    height: 93%;
    overflow: auto;
  }
  .template-type-box {
    margin-bottom: 24px;

    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }

    &-navigation {
      display: flex;
      align-items: center;
      gap: 16px;

      .arrow-btn {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.2);
        box-sizing: border-box;
        border: 1px solid rgba(0, 255, 255, 0.2);
        width: 32px;
        height: 82px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        z-index: 1; // 确保箭头位于上层

        &:hover {
          background: rgba(0, 255, 255, 0.2);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background: rgba(0, 255, 255, 0.05);
        }

        img {
          width: 24px;
          height: 24px;
        }
      }
      .left-arrow {
        img {
          transform: rotate(180deg);
        }
      }
    }

    &-content {
      display: flex;
      flex: 1;
      gap: 16px;
      overflow-x: auto; // 允许水平滚动
      overflow-y: hidden; // 禁止垂直滚动
      scroll-behavior: smooth; // 平滑滚动效果
      /* 隐藏默认滚动条 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* Internet Explorer 10+ */
      &::-webkit-scrollbar {
        /* WebKit */
        width: 0;
        height: 0;
      }
      padding: 0 5px; // 添加一些内边距，使内容不会紧贴边缘

      &-item {
        flex: none;
        width: 206px;
        height: 82px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.2);
        box-sizing: border-box;
        border: 1px solid rgba(0, 255, 255, 0.2);
        line-height: 82px;
        padding-left: 16px;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        color: #ffffff;
        position: relative;
        cursor: pointer;
        &-title {
          text-wrap: nowrap;
          width: 114px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &-icon {
          width: 80px;
          height: 80px;
          position: absolute;
          right: 0;
          bottom: 0;
          user-select: none;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .active {
        border-radius: 4px;
        background: rgba(255, 220, 0, 0.2);
        box-sizing: border-box;
        box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
        border: 1px solid #fff197;
        color: #ffdc00;
      }
    }

    .activeRT {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  /* 添加模板选择器样式 */
  .template-select-box {
    margin-bottom: 24px;

    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }

    &-content {
      .template-select {
        width: 100%;
      }
    }
  }

  .template-content-box {
    margin-bottom: 24px;
    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }
    &-content {
      display: flex;
      justify-content: space-between;
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      padding: 16px;
      .fromBox {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 16px;
        .fromItem {
          width: 100%;
          .fromItem-title {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            letter-spacing: normal;
            color: #ffffff;
            margin-bottom: 10px;
          }
          .fromItem-input {
            width: 100%;
          }
          .fromItem-add {
            margin-top: 10px;
            display: inline-block;
            border-radius: 4px;
            background: linear-gradient(
              180deg,
              rgba(0, 255, 255, 0.3) 2%,
              rgba(0, 255, 255, 0.2) 100%
            );
            box-sizing: border-box;
            border: 1px solid #00ffff;
            box-shadow: inset 0px 0px 12px 0px #00ffff;
            padding: 5px 24px;
            font-size: 14px;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
            &:active {
              transform: scale(0.98);
            }
          }
        }
      }
    }
  }
  .template-message-box {
    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }

    &-content {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      &-item {
        width: 151px;
        height: 48px;
        box-sizing: border-box;
        cursor: pointer;
        font-family: Alibaba PuHuiTi 3;
        font-size: 16px;
        font-weight: bold;
        line-height: 48px;
        letter-spacing: normal;
        color: #ffffff;
        padding-left: 50px;
        &.active {
          color: #ffdc00;
        }
        &:nth-child(1) {
          background: url("../../assets/image/type-gg.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-gg-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(2) {
          background: url("../../assets/image/type-hhcs.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-hhcs-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(3) {
          background: url("../../assets/image/type-ccth.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ccth-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(4) {
          background: url("../../assets/image/type-ysgk.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ysgk-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(5) {
          background: url("../../assets/image/type-jm.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-jm-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(6) {
          background: url("../../assets/image/type-qh.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-qh-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(7) {
          background: url("../../assets/image/type-wgc.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-wgc-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(8) {
          background: url("../../assets/image/type-qt.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-qt-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(9) {
          background: url("../../assets/image/type-gg.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-gg-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(10) {
          background: url("../../assets/image/type-wgc.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-wgc-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(11) {
          background: url("../../assets/image/type-ccth.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ccth-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
      }
    }
  }

  .template-footer {
    // height: 72px;
    position: absolute;
    bottom: 16px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .save-template {
      padding: 9px 16px;
      border-radius: 4px;
      background: rgba(0, 255, 255, 0.1);
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: normal;
      /* 主色/卓越青 */
      color: #00ffff;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
      &:active {
        transform: scale(0.98);
      }
      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
    &-close {
      width: 112px;
      height: 40px;
      cursor: pointer;
      margin-right: 16px;
      &:hover {
        opacity: 0.8;
      }
      &:active {
        transform: scale(0.98);
      }
      img {
        width: 100%;
      }
    }
    &-sumbit {
      width: 112px;
      height: 40px;
      //   padding: 8px 40px;
      text-align: center;
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 255, 255, 0.3) 2%,
        rgba(0, 255, 255, 0.2) 100%
      );
      box-sizing: border-box;
      border: 1px solid #00ffff;
      box-shadow: inset 0px 0px 12px 0px #00ffff;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
      &:active {
        transform: scale(0.98);
      }
      .fontStyle {
        font-size: 16px;
        line-height: 40px;
        font-weight: normal;
        color: #fff;
      }
    }
  }

  .params-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    .params-title {
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 12px;
    }

    .params-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .param-item {
        display: flex;
        align-items: center;

        .param-label {
          width: 200px;
          font-size: 14px;
          color: #fff;
        }

        .param-input {
          flex: 1;
        }

        .param-value {
          flex: 1;
          color: rgba(0, 255, 255, 0.7);
          font-style: italic;
        }
      }
    }
  }
}
.radioBox {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  .radioItem {
    border-radius: 4px;
    background: rgba(0, 128, 255, 0.2);
    padding: 5px 16px;
    box-sizing: border-box;
    cursor: pointer;
    span {
      font-size: 14px;
    }
    .fontStyle {
      font-size: 14px;
    }
  }
  .active {
    border-radius: 4px;
    background: rgba(255, 220, 0, 0.2);
    box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
    .fontStyle {
      letter-spacing: normal;
      background: #ffdc00;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
}
.preview-content {
  padding: 12px;
  background: rgba(0, 128, 255, 0.1);
  border-radius: 4px;
  color: #fff;
  line-height: 1.6;
  min-height: 80px;
  white-space: pre-wrap;
  word-break: break-word;
  box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2);
  user-select: none;
  cursor: not-allowed;
}
:deep(.el-textarea__inner) {
  background: rgba(0, 128, 255, 0.2);
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}
:deep(.el-input__count) {
  background: transparent;
}
:deep(.el-input__inner) {
  color: #fff;
}
:deep(.el-textarea__inner) {
  color: #fff;
}
</style>
