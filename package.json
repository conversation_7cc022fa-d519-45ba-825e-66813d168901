{"name": "agent-seat-vue3", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.2", "core-js": "^3.8.3", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.3", "qs": "^6.14.0", "vue": "^3.2.13", "vue-count-to": "^1.0.13", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.16", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.7.1", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.3.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.89.0", "sass-loader": "^13.3.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}