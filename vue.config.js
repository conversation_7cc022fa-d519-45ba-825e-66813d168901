const { defineConfig } = require("@vue/cli-service");
// let target = "http://132.91.197.34:9060"; // UAT环境
// let target = "http://132.77.218.10:9060"; // 线上地址
let target = "http://132.90.188.136:9060"; // 测试地址
module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: "./",
  assetsDir: "static",
  devServer: {
    proxy: {
      // "/": {
      //   target: target, // UAT环境
      //   changeOrigin: true,
      //   rewrite: (path) => path,
      // },
      "/cx-monitordata-12345": {
        target: target, // UAT环境
        changeOrigin: true,
        rewrite: (path) => path,
      },
      "/yc-ccbar-v1": {
        target: target, // UAT环境
        changeOrigin: true,
        rewrite: (path) => path,
      },
      "/aiamgr": {
        target: target, // UAT环境
        changeOrigin: true,
        rewrite: (path) => path,
      },
      "/cc-base": {
        target: target, // UAT环境
        changeOrigin: true,
        rewrite: (path) => path,
      },
    },
  },
});
