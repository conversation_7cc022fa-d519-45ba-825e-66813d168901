import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建axios实例
const service = axios.create({
  baseURL: '', // 可根据环境配置
  timeout: 15000, // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 可以在这里设置请求头等信息
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    // 根据业务状态码处理
    if (res.result && res.result !== '000') {
      ElMessage.error(res.msg || '请求失败');
      return Promise.reject(new Error(res.msg || '请求失败'));
    }
    return res;
  },
  error => {
    // 处理被取消的请求
    if (axios.isCancel(error)) {
      console.log('请求被取消:', error.message);
      return Promise.reject(error);
    }
    
    // 其他错误处理
    console.error('响应错误:', error);
    ElMessage.error(error.message || '网络异常，请稍后重试');
    return Promise.reject(error);
  }
);

export default service; 