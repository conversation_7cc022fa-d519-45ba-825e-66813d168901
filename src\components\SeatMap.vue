<template>
  <div
    class="seat-map"
    @mousedown="startDrag"
    @mousemove="onDrag"
    @mouseup="stopDrag"
    @mouseleave="stopDrag"
    @wheel.prevent="handleWheel"
    ref="seatMapRef"
  >
    <div
      class="seatBox"
      :key="key"
      :style="{
        transform: `scale(${displayZoom}) translate(${dragX}px, ${dragY}px)`,
        transformOrigin: '0 0',
      }"
    >
      <div class="seat" v-for="(arr, index) in processedSeatList" :key="index">
        <div
          :class="[
            'seat-item',
            { click_select: item.select },
            { is_show: item.isNull == '2' },
            {
              is_afterLongMsgId:
                item.afterLongMsgId ||
                item.extraLongCallMsgId ||
                item.seekHelpMsgId ||
                item.speechSpeedMsgId ||
                item.muteMsgId ||
                item.violationWordMsgId ||
                item.sensitiveWordMsgId ||
                item.robTalkMsgId,
            },
          ]"
          v-for="(item, index) in arr"
          :key="index"
          @click="handleView(item)"
          @mouseenter="(event) => handleSeatEnter(event, item)"
          @mouseleave="handleSeatLeave"
          :id="'S' + item.seatNo"
        >
          <!-- 浮窗提示 -->
          <div
            class="seat-tooltip"
            v-if="
              currentTooltipItem === item &&
              (item.afterLongMsgId ||
                item.extraLongCallMsgId ||
                item.seekHelpMsgId ||
                item.speechSpeedMsgId ||
                item.muteMsgId ||
                item.violationWordMsgId ||
                item.sensitiveWordMsgId ||
                item.robTalkMsgId)
            "
          >
            <div class="bgContent">
              <div class="tooltip-content">
                <template v-if="item.extraLongCallMsgId">
                  <!-- <div class="tooltip-icon">
                    <img src="../assets/image/call-icon.png" alt="" />
                  </div> -->
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(
                        item.extraLongCallMsgId,
                        'extraLongCallMsgId',
                        item
                      )
                    "
                  >
                    超长通话
                  </div>
                </template>
                <template v-if="item.afterLongMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(item.afterLongMsgId, 'afterLongMsgId', item)
                    "
                  >
                    话后超时
                  </div>
                </template>
                <template v-if="item.seekHelpMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(item.seekHelpMsgId, 'seekHelpMsgId', item)
                    "
                  >
                    求助
                  </div>
                </template>
                <template v-if="item.speechSpeedMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(
                        item.speechSpeedMsgId,
                        'speechSpeedMsgId',
                        item
                      )
                    "
                  >
                    语速过快
                  </div>
                </template>
                <template v-if="item.muteMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="handleAlarm(item.muteMsgId, 'muteMsgId', item)"
                  >
                    静音
                  </div>
                </template>
                <template v-if="item.violationWordMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(
                        item.violationWordMsgId,
                        'violationWordMsgId',
                        item
                      )
                    "
                  >
                    坐席违规词
                  </div>
                </template>
                <template v-if="item.sensitiveWordMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(
                        item.sensitiveWordMsgId,
                        'sensitiveWordMsgId',
                        item
                      )
                    "
                  >
                    市民敏感词
                  </div>
                </template>
                <template v-if="item.robTalkMsgId">
                  <div
                    class="tooltip-title"
                    @click.stop="
                      handleAlarm(item.robTalkMsgId, 'robTalkMsgId', item)
                    "
                  >
                    抢话
                  </div>
                </template>
              </div>
            </div>
            <div class="bgBottom"></div>
          </div>
          <div class="jp" v-if="item?.role == '1' || item?.role == '2'">
            <img
              v-if="item?.role == '1'"
              src="../assets/image/zbz.png"
              alt=""
            />
            <img v-if="item?.role == '2'" src="../assets/image/bz.png" alt="" />
          </div>
          <div v-if="item.warnType != '1'">
            <!-- 检查是否存在特殊状态，如果存在则使用redSeat图片 -->
            <img
              src="../assets/image/redSeat.png"
              alt=""
              v-if="
                item.afterLongMsgId ||
                item.extraLongCallMsgId ||
                item.seekHelpMsgId ||
                item.speechSpeedMsgId
              "
            />
            <!-- 占位 -->
            <img
              src="../assets/image/map/empty.png"
              alt=""
              v-else-if="!item.currentState"
            />
            <!-- 空闲 -->
            <img
              src="../assets/image/map/kx.png"
              alt=""
              v-else-if="item.currentState == '1'"
            />
            <!-- 通话 -->
            <img
              src="../assets/image/map/th.png"
              alt=""
              v-else-if="
                item.currentState == '2' ||
                item.currentState == '3' ||
                item.currentState == '4' ||
                item.currentState == '5'
              "
            />
            <img
              src="../assets/image/map/hh.png"
              alt=""
              v-else-if="item.currentState == '6' || item.currentState == '10'"
            />
            <!-- 离席 -->
            <img
              src="../assets/image/map/sm.png"
              alt=""
              v-else-if="
                item.currentState == '7' ||
                item.currentState == '8' ||
                item.currentState == '9'
              "
            />
            <img
              src="../assets/image/map/wqr.png"
              alt=""
              v-else-if="item.currentState == '0'"
            />
          </div>
          <div v-else>
            <img src="../assets/image/map/pd.png" alt="" />
          </div>
          <div class="seat-name">
            {{ item.agentName ? item.agentName : "--" }}
          </div>
          <div class="seat-id">{{ item.agentId ? item.agentId : "--" }}</div>
          <div class="seat-id">{{ item.seatNo ? item.seatNo : "--" }}</div>
          <div class="top-seat">
            <div class="seat-box">
              <!-- 语速过快 -->
              <!-- <div class="seat-dot-item" v-if="item.speechSpeedMsgId">
                <div class="seat-warn greenDotBox"></div>
                <div class="dotCont greenDot"></div>
              </div> -->
              <!-- 求助 -->
              <!-- <div class="seat-dot-item" v-if="item.seekHelpMsgId">
                <div class="seat-warn blueDotBox"></div>
                <div class="dotCont blueDot"></div>
              </div> -->
              <!-- 话后超时 -->
              <!-- <div class="seat-dot-item" v-if="item.afterLongMsgId">
                <div class="seat-warn redDotBox"></div>
                <div class="dotCont redDot"></div>
              </div> -->
              <!-- 超长通话 -->
              <!-- <div class="seat-dot-item" v-if="item.extraLongCallMsgId">
                <div class="seat-warn yellowDotBox"></div>
                <div class="dotCont yellowDot"></div>
              </div> -->
            </div>
            <div
              class="seat-status blue"
              v-if="[2, 3, 4, 5].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status green"
              v-if="Number(item.currentState) === 1"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status cyan"
              v-if="[6, 10].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status orange"
              v-if="[7, 8, 9].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status black"
              v-if="Number(item.currentState) === 0"
            >
              {{ statusList[item.currentState] }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警处理对话框 -->
    <AlarmDialog
      v-model:visible="dialogVisible"
      :dealObj="currentDealObj"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import AlarmDialog from "./AlarmDialog.vue";

export default {
  name: "SeatMap",
  components: {
    AlarmDialog,
  },
  props: {
    seatList: {
      type: Array,
      required: true,
    },
    statusList: {
      type: Object,
      required: true,
    },
    zoomNumber: {
      type: Number,
      default: 1,
    },
  },
  setup(props, { emit }) {
    const {
      ref,
      computed,
      onUnmounted,
      reactive,
      onMounted,
      watch,
    } = require("vue");
    // 添加节流控制变量
    let lastRefreshTime = 0;
    const REFRESH_THROTTLE = 3000; // 3秒内只刷新一次

    // 节流函数：指定时间内只执行一次
    const throttleRefresh = () => {
      const now = Date.now();
      if (now - lastRefreshTime > REFRESH_THROTTLE) {
        lastRefreshTime = now;
        emit("refresh");
        // console.log("触发刷新接口");
      }
    };

    // 添加一个强制重绘DOM的函数
    const forceRepaint = () => {
      if (!seatMapRef.value) return;

      const seatMap = seatMapRef.value;
      // 暂时隐藏元素，然后强制重绘
      const originalDisplay = seatMap.style.display;
      seatMap.style.display = "none";
      // 触发重排/重绘
      void seatMap.offsetHeight;
      // 恢复显示
      seatMap.style.display = originalDisplay;

      // console.log("强制重绘DOM完成");
    };

    // 添加强制重渲染功能
    const key = ref(0);
    const forceRerender = () => {
      key.value++;
      // console.log("强制重渲染组件");

      // 重渲染后重新获取座位数据
      throttleRefresh();
    };

    // 添加缩放计时器
    let zoomDebounceTimer = null;

    // 添加初始渲染标志
    const initialRenderComplete = ref(false);
    // 内部缩放值，初始设为最大值4
    const internalZoom = ref(4);
    // 存储上一次的缩放值，用于检测大幅度变化
    const lastZoomValue = ref(1);

    // 在组件挂载时实现高分辨率渲染后缩回正常大小
    onMounted(() => {
      // 首先用最大缩放渲染DOM
      internalZoom.value = 4; // 使用最大缩放比例

      // 等待DOM完全渲染后再缩回正常大小
      setTimeout(() => {
        internalZoom.value = props.zoomNumber; // 缩回到传入的缩放值
        lastZoomValue.value = props.zoomNumber; // 初始化上一次缩放值
        initialRenderComplete.value = true;
        // console.log("初始渲染完成，缩放值重置为", internalZoom.value);
        // 不再触发父组件的zoom-change事件，避免出错
      }, 100);
    });

    // 监听props.zoomNumber的变化
    watch(
      () => props.zoomNumber,
      (newValue) => {
        if (initialRenderComplete.value) {
          const zoomDiff = Math.abs(newValue - lastZoomValue.value);
          internalZoom.value = newValue;
          lastZoomValue.value = newValue;

          // 如果缩放变化幅度大于阈值，立即强制重绘
          if (zoomDiff > 0.1) {
            // console.log("检测到大幅度缩放变化:", zoomDiff, "执行强制重绘");
            // 短暂延时以确保缩放值已应用
            setTimeout(() => {
              forceRepaint();
            }, 50);
          }

          // 清除之前的计时器
          if (zoomDebounceTimer) {
            clearTimeout(zoomDebounceTimer);
          }

          // 设置新的计时器，在缩放停止后重新渲染组件
          zoomDebounceTimer = setTimeout(() => {
            // console.log("缩放停止，强制重渲染");
            forceRerender();
          }, 300);
        }
      }
    );

    // 告警处理对话框相关状态
    const dialogVisible = ref(false);
    const currentDealObj = reactive({});

    // 处理告警
    const handleAlarm = (id, type, item) => {
      // 根据不同类型的告警，设置不同的告警类型
      let alarmType = 0;
      // 1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民敏感词 8.求助
      switch (type) {
        case "extraLongCallMsgId":
          alarmType = 1; // 超长通话
          break;
        case "afterLongMsgId":
          alarmType = 2; // 话后超时
          break;
        case "seekHelpMsgId":
          alarmType = 8; // 求助
          break;
        case "speechSpeedMsgId":
          alarmType = 4; // 语速过快
          break;
        case "violationWordMsgId":
          alarmType = 6; // 坐席违规词
          break;
        case "sensitiveWordMsgId":
          alarmType = 7; // 市民敏感词
          break;
        case "robTalkMsgId":
          alarmType = 5; // 抢话
          break;
        default:
          alarmType = 0; // 默认
          break;
      }

      // 设置当前处理对象
      Object.assign(currentDealObj, {
        type: "deal",
        MSG_ID: id,
        AGENT_ID: item.agentId,
        ROOM_LOCATION: item.roomLocation || "",
        ALARM_TYPE: alarmType,
      });

      // 显示告警处理对话框
      dialogVisible.value = true;
    };

    // 处理对话框关闭
    const handleDialogClose = () => {
      dialogVisible.value = false;
      // 通知父组件刷新待办列表
      emit("refresh");
    };

    // 处理座席数据，将一维数组转换为二维数组
    const groupedSeatList = computed(() => {
      const array = props.seatList || [];
      return array
        .reduce((acc, obj) => {
          let group = acc.find((g) => g[0]?.horizontal === obj.horizontal);
          if (group) {
            group.push(obj);
          } else {
            acc.push([obj]);
          }
          return acc;
        }, [])
        .map((group) => {
          return group.sort((a, b) => a.horizontal - b.horizontal);
        });
    });

    const isDragging = ref(false);
    const startX = ref(0);
    const startY = ref(0);
    const dragX = ref(0);
    const dragY = ref(0);
    const seatMapRef = ref(null);
    let wheelTimer = null;
    let rafId = null; // 用于requestAnimationFrame
    let lastDragEvent = null; // 存储最后一次拖动事件
    
    // 添加拖动检测相关变量
    const isDragThresholdExceeded = ref(false);
    const dragThreshold = 5; // 设置拖动阈值为5px，比100px小一些，更流畅
    const initialMousePosX = ref(0);
    const initialMousePosY = ref(0);

    // const debounce = (fn, delay) => {
    //   return (...args) => {
    //     if (wheelTimer) clearTimeout(wheelTimer);
    //     wheelTimer = setTimeout(() => {
    //       fn.apply(null, args);
    //     }, delay);
    //   };
    // };

    // 移除之前的resize模拟重绘，现在使用最大缩放预渲染
    // const emitZoomChange = debounce((newZoom) => {
    //   // 更新内部缩放值
    //   internalZoom.value = newZoom;
    //   // 通知父组件
    //   emit("zoom-change", newZoom);
    //   console.log("缩放改变为:", newZoom);
    // }, 16); // 约一帧的时间

    const handleWheel = (e) => {
      e.preventDefault();
      // console.log("滚轮事件触发", initialRenderComplete.value);
      // 只有在初始渲染完成后才响应滚轮事件
      if (initialRenderComplete.value) {
        const direction = e.deltaY < 0 ? 1 : -1;
        // 计算新的缩放值
        const newZoom = Math.max(
          0.1,
          Math.min(4, internalZoom.value + direction * 0.02)
        );
        // console.log("新缩放值计算为:", newZoom);

        // 如果变化太小，不触发事件
        if (Math.abs(newZoom - internalZoom.value) < 0.01) {
          return;
        }

        // 直接更新内部缩放值
        internalZoom.value = newZoom;
        lastZoomValue.value = newZoom;

        // 确保父组件数据已初始化后才发送事件
        if (props.seatList && props.seatList.length > 0) {
          try {
            emit("zoom-change", newZoom);

            // 清除之前的缩放计时器
            if (zoomDebounceTimer) {
              clearTimeout(zoomDebounceTimer);
            }

            // 设置新的计时器，在滚轮缩放停止后重新渲染组件
            zoomDebounceTimer = setTimeout(() => {
              // console.log("滚轮缩放停止，强制重渲染");
              forceRerender();
            }, 300);
          } catch (error) {
            console.error("缩放事件处理错误:", error);
          }
        }
      }
    };

    const handleView = (item) => {
      // 如果超过拖动阈值，不触发点击事件
      if (!isDragging.value && !isDragThresholdExceeded.value) {
        emit("view-seat", item);
      }
    };

    const updateDragPosition = () => {
      if (lastDragEvent && isDragging.value) {
        // 计算拖动距离
        const dragDistanceX = Math.abs(lastDragEvent.clientX - initialMousePosX.value);
        const dragDistanceY = Math.abs(lastDragEvent.clientY - initialMousePosY.value);
        
        // 如果拖动距离超过阈值，设置标志为true
        if (dragDistanceX > dragThreshold || dragDistanceY > dragThreshold) {
          isDragThresholdExceeded.value = true;
        }
        
        dragX.value = lastDragEvent.clientX - startX.value;
        dragY.value = lastDragEvent.clientY - startY.value;
        rafId = requestAnimationFrame(updateDragPosition);
      }
    };

    const startDrag = (e) => {
      isDragging.value = true;
      // 重置拖动阈值标志
      isDragThresholdExceeded.value = false;
      // 记录初始鼠标位置
      initialMousePosX.value = e.clientX;
      initialMousePosY.value = e.clientY;
      
      startX.value = e.clientX - dragX.value;
      startY.value = e.clientY - dragY.value;
      lastDragEvent = e;
      rafId = requestAnimationFrame(updateDragPosition);
    };

    const onDrag = (e) => {
      if (isDragging.value) {
        e.preventDefault();
        lastDragEvent = e;
      }
    };

    const stopDrag = () => {
      isDragging.value = false;
      if (rafId) {
        cancelAnimationFrame(rafId);
        rafId = null;
      }
      lastDragEvent = null;

      // 拖动结束后延迟一段时间重置拖动阈值标志
      setTimeout(() => {
        isDragThresholdExceeded.value = false;
      }, 100);

      // 拖动结束后也触发延迟重渲染
      if (initialRenderComplete.value) {
        // 清除之前的计时器
        if (zoomDebounceTimer) {
          clearTimeout(zoomDebounceTimer);
        }

        // 设置新的计时器，在拖动停止后重新渲染组件
        zoomDebounceTimer = setTimeout(() => {
          // console.log("拖动停止，强制重渲染");
          forceRerender();
        }, 300);
      }
    };

    // 添加浮窗相关的响应式变量
    const showTooltipFlag = ref(false);
    const currentTooltipItem = ref(null);
    const currentSeatElement = ref(null);

    // 处理鼠标进入座席
    const handleSeatEnter = (event, item) => {
      if (
        item.afterLongMsgId ||
        item.extraLongCallMsgId ||
        item.seekHelpMsgId ||
        item.speechSpeedMsgId
      ) {
        currentTooltipItem.value = item;
        currentSeatElement.value = event.currentTarget;
      }
    };

    // 处理鼠标离开座席
    const handleSeatLeave = () => {
      currentTooltipItem.value = null;
      currentSeatElement.value = null;
    };

    // 组件卸载时清理
    onUnmounted(() => {
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
      if (wheelTimer) {
        clearTimeout(wheelTimer);
      }
      if (zoomDebounceTimer) {
        clearTimeout(zoomDebounceTimer);
      }
    });

    return {
      handleView,
      startDrag,
      onDrag,
      stopDrag,
      dragX,
      dragY,
      seatMapRef,
      handleWheel,
      processedSeatList: groupedSeatList,
      // 使用计算的internalZoom替代直接使用props.zoomNumber
      displayZoom: computed(() => internalZoom.value),

      // 添加告警处理相关方法和状态
      handleAlarm,
      dialogVisible,
      currentDealObj,
      handleDialogClose,
      forceRerender,
      // 添加浮窗相关的方法
      showTooltipFlag,
      currentTooltipItem,
      key,
      // 添加新的事件处理方法
      handleSeatEnter,
      handleSeatLeave,
    };
  },
};
</script>

<style lang="scss" scoped>
.seat-map {
  position: relative;
  overflow: hidden;
  user-select: none;
  cursor: grab;
  padding: 40px 30px;
  &:active {
    cursor: grabbing;
  }
  * {
    user-select: none;
    -webkit-user-drag: none;
  }
}

.seatBox {
  flex: 0 0 100%;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  transform-style: preserve-3d;
  transition: none; // 移除过渡效果，避免拖动时的延迟
  pointer-events: auto;
}

.seat {
  flex: 1;
  grid-gap: 10px;
  display: flex;
  margin-bottom: 10px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.seat-item {
  min-width: 70.26px;
  min-height: 84.38px;
  border-radius: 2.04px;
  background: rgba(0, 128, 255, 0.1);
  text-align: center;
  padding-top: 15px;
  position: relative;
  cursor: pointer;
  pointer-events: auto;

  * {
    pointer-events: none; // 确保子元素不接收鼠标事件
  }

  .seat-tooltip {
    pointer-events: auto; // 让浮窗可以接收鼠标事件
    * {
      pointer-events: auto; // 让浮窗的子元素也可以接收鼠标事件
    }
  }

  img {
    width: 30.55px;
    height: 30.55px;
    -webkit-user-drag: none;
  }

  .jp {
    width: 20px;
    height: 24px;
    position: absolute;
    top: 0;
    right: 2px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.click_select {
  border: 1px solid #0555ce;
}

.seat-name {
  color: #fff;
  font-size: 7.13px;
  margin: 2px 0px 6px 0px;
}

.seat-id {
  font-size: 7.13px;
  color: rgba(255, 255, 255, 0.6);
  &:nth-child(1) {
    margin-top: 2px;
  }
}

.top-seat {
  position: absolute;
  width: 100%;
  top: 0;
  display: flex;
  justify-content: space-between;
}

.seat-box {
  flex: 1;
  // padding-top: 4px;
  box-sizing: border-box;
  padding-left: 4px;
  position: absolute;
  right: 0;
}

.seat-dot-item {
  width: 20px;
  height: 20px;
  position: relative;
  margin-right: 3px;
}

.seat-warn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: tl-h-52 800ms infinite alternate;
}

.dotCont {
  width: 8.15px;
  height: 8.15px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes tl-h-52 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.5);
  }
}

.is_afterLongMsgId {
  border-radius: 4px;
  background: rgba(240, 56, 56, 0.2);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(
      166deg,
      #dc0055 6%,
      rgba(220, 0, 85, 0) 46%,
      #dc0055 89%
    )
    1;
}

.redDotBox {
  background-color: rgba(220, 0, 85, 0.2);
}

.redDot {
  background-color: rgb(220, 0, 85);
}

.blueDotBox {
  background-color: rgba(64, 158, 255, 0.2);
}

.blueDot {
  background-color: rgb(64, 158, 255);
}

.yellowDotBox {
  background-color: rgba(230, 162, 60, 0.2);
}

.yellowDot {
  background-color: rgb(230, 162, 60);
}

.greenDotBox {
  background-color: rgba(38, 177, 101, 0.2);
}

.greenDot {
  background-color: rgb(38, 177, 101);
}

.seat-status {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 1.02px 4.07px;
  font-size: 7.13px;
  text-wrap: nowrap;
  position: absolute;
  line-height: 14.04px;
  left: 0;
}

.blue {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-th.png) no-repeat center center;
  background-size: 100% 100%;
  color: #00ddff;
}

.green {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-kx.png) no-repeat center center;
  background-size: 100% 100%;
  color: #52c41a;
}

.cyan {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-hh.png) no-repeat center center;
  background-size: 100% 100%;
  color: #0080ff;
}

.orange {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-sm.png) no-repeat center center;
  background-size: 100% 100%;
  color: #fa9904;
}

.black {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-wqr.png) no-repeat center center;
  background-size: 100% 100%;
  color: #868686;
}

// 修改浮窗样式
.seat-tooltip {
  position: absolute;
  z-index: 9999;
  border-radius: 4px;
  padding: 8px 12px;
  bottom: 90%; /* 修改：使用bottom: 100%替代top: -60px */
  margin-bottom: 5px; /* 添加：设置与父元素的间距 */
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  color: #ffffff;
  flex-direction: column;
  opacity: 0;
  animation: fadeIn 0.2s forwards;

  .seat-item:hover & {
    opacity: 1;
  }

  .seat-item:not(:hover) & {
    animation: fadeOut 1s forwards;
  }

  * {
    pointer-events: auto;
  }

  .bgContent {
    background: url(../assets/image/redtop.png) no-repeat center center;
    background-size: 100% 100%;
    width: 100px;
    position: relative;
    cursor: pointer; // 添加指针样式
    transition: transform 0.2s ease; // 添加过渡效果
    padding: 8px 8px 4px;
  }

  .bgBottom {
    background: url(../assets/image/redbottom.png) no-repeat center center;
    background-size: 100% 100%;
    height: 12px;
    width: 100px;
    margin-top: -1px;
    position: relative;
    padding: 0 8px;
  }

  .tooltip-icon {
    margin-right: 8px;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .tooltip-content {
    align-items: center;
    justify-content: center;
    .tooltip-title {
      width: 100px;
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      text-align: center;
      &:hover {
        color: #00ddff;
        background: rgba(172, 15, 44, 0.2);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
