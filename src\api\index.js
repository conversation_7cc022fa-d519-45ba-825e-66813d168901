import axios from "axios";
import qs from "qs";

// 创建axios实例
const request = axios.create({
  baseURL: "",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 如果是x-www-form-urlencoded格式，转换数据
    if (
      config.headers["Content-Type"] === "application/x-www-form-urlencoded"
    ) {
      config.data = qs.stringify(config.data);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 查询场所
export function queryPhoneRoom() {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: { messageId: "queryPhoneRoom" },
  });
}

export function getSkillGroupList() {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: { messageId: "getSkillGroupList" },
  });
}

export function getWorkGroupList() {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: { messageId: "getWorkGroupList" },
  });
}

// /cx-monitordata-12345/servlet/pointMapMonitor?action=Interface
export function getToDuList(data) {
  data.messageId = "getToDuList";
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data,
  });
}

// 根据座席ID查询座席
export function queryAgentById(agentId) {
  return request({
    url: "/cx-report-12345/post/interface?action=Interface",
    method: "post",
    data: {
      messageId: "queryAgentById",
      agentId,
    },
  });
}

// 查询座席位置
export function queryAgentPlace(data, options = {}) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: data.messageId || "queryAgentPlace",
      ...data,
    },
    ...options, // 添加自定义选项
  });
}

// 根据座席ID查询座席详情
export function queryAgentInfoById(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: "queryAgentInfoById",
      ...data,
    },
  });
}

// 获取历史转写记录
export function getCallData(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/GetCallData?action=SelectCallData",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data,
  });
}

// /cx-monitordata-12345/servlet/pointMapMonitor?action=Interface
export function getWorkGroupInfo(data) {
  data.messageId = "queryWorkGroupInfoById";
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface ",
    method: "post",
    data,
  });
}
// 获取用户类型
export function getUsetType() {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: { messageId: "getUsetType" },
  });
}

// /cx-monitordata-12345/servlet/pointMapMonitor?action=Interface
export function getHelpRecord(data) {
  data.messageId = "queryHelpList";
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data,
  });
}

// 获取告警模板
export function getWarnTemp(msgType) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: "getWarnTemp",
      msgType,
    },
  });
}

// 查询转派人员
export function queryUserAcc(msgId) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: "queryUserAcc",
      msgId,
    },
  });
}

// 更新座席告警
export function handleAlarm(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: "handleAlarm",
      ...data,
    },
  });
}

// 转派告警
export function transferWarn(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/pointMapMonitor?action=Interface",
    method: "post",
    data: {
      messageId: "transferWarn",
      ...data,
    },
  });
}

// 添加告警模板
export function addTemplate(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/noticeTemplate?action=save",
    method: "post",
    data: { data },
  });
}

// 获取模版列表
export function getTemplateList(data) {
  return request({
    url: "/cx-monitordata-12345/webcall?action=noticeTemplateDao.getList",
    method: "post",
    data: { data },
  });
}

// 删除模版
export function deleteTemplate(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/noticeTemplate?action=delete",
    method: "post",
    data: { data },
  });
}

// 获取模版详情
export function getTemplateDetail(data) {
  return request({
    url: "/cx-monitordata-12345/webcall?action=noticeTemplateDao.getDetail",
    method: "post",
    data: { data },
  });
}

// 创建短信消息 /cx-monitordata-12345/servlet/notice?action=createSmsMsg
export function createSmsMsg(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/notice?action=createSmsMsg",
    method: "post",
    data: { data },
  });
}

// 创建外呼消息 /cx-monitordata-12345/servlet/notice?action=createCallMsg
export function createCallMsg(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/notice?action=createCallMsg",
    method: "post",
    data: { data },
  });
}

// 创建助手消息 /cx-monitordata-12345/servlet/notice?action=createAssistantMsg
export function createAssistantMsg(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/notice?action=createAssistantMsg",
    method: "post",
    data: { data },
  });
}

// 另存为新的模板 /cx-monitordata-12345/servlet/notice?action=saveNewTemplate
export function saveNewTemplate(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/notice?action=saveNewTemplate",
    method: "post",
    data: { data },
  });
}

// 获取短信下发明细消息列表
export function getSmsList(data) {
  return request({
    url: "/cx-monitordata-12345/webcall?action=noticeDao.getSmsList",
    method: "post",
    data: { data },
  });
}

// 获取外呼通知明细消息列表
export function getCallList(data) {
  return request({
    url: "/cx-monitordata-12345/webcall?action=noticeDao.getCallList",
    method: "post",
    data: { data },
  });
}

// 获取助手消息明细消息列表 /aiamgr/msg/list.do
export function getAssistantList(data) {
  return request({
    url: "/aiamgr/msg/list.do",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
// 助手获取班组下拉 /aiamgr/msg/getWordList.do
export function getWordList(data) {
  return request({
    url: "/aiamgr/msg/getWordList.do",
    method: "get",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// 获取消息详情 /msgInfo/list.do
export function getMessageDetail(data) {
  return request({
    url: "/aiamgr/msgInfo/list.do",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// aiamgr/agentmgr/getAgentCallInfo.do
export function getAgentCallInfo(data) {
  return request({
    url: "/aiamgr/agentmgr/getAgentCallInfo.do",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// aiamgr/record/play.do
export function playRecord(data) {
  return request({
    url: "/aiamgr/record/play.do",
    method: "get",
    params: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// /aiamgr/msgInfo/export.do
export function exportMsgInfo(data) {
  return request({
    url: "/aiamgr/msgInfo/export.do",
    method: "get",
    params: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
// /cx-monitordata-12345/servlet/export 外呼明细导出
export function exportCallDetail(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/export?action=exportCallDetail",
    method: "post",
    data: { data },
    headers: {
     'Content-Type': 'application/json'
    },
     responseType: 'blob'
  });
}
// /cx-monitordata-12345/servlet/export 短信明细导出
export function exportSmsDetail(data) {
  return request({
    url: "/cx-monitordata-12345/servlet/export?action=exportSmsDetail",
    method: "post",
    data: { data },
    headers: {
     'Content-Type': 'application/json'
    },
    responseType: 'blob'
  });
}
