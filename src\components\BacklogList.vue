<template>
  <div class="content-box">
    <div class="history-record-info">
      <div class="history-record-list" v-if="backlogList?.length">
        <div
          v-for="(item, index) in backlogList"
          :key="index"
          class="history-record-item"
        >
          <div class="item-left"></div>
          <div class="item-right">
            <div class="phone-number">
              <span class="fontStyle">{{ item.AGENT_NAME }}</span>
              <div
                class="alarm-type"
                :class="getAlarmTypeClass(AlarmType[item.ALARM_TYPE])"
              >
                {{ AlarmType[item.ALARM_TYPE] }}
              </div>
            </div>

            <div class="call-info">
              <div class="time">
                <span class="label">坐席工号：</span>
                <span class="value number">{{ item.AGENT_ID }}</span>
              </div>
              <div class="time">
                <span class="label">触发时间：</span>
                <span class="value number">{{ item.ALARM_TIME }}</span>
              </div>
            </div>

            <div class="btnBox">
              <div
                class="btn"
                v-if="item.ALARM_TYPE == 8 && userType == 1"
                @click="handleTransfer(item)"
              >
                转派
              </div>
              <div
                class="btn"
                v-if="userType == 2 || userType == 1"
                @click="handleDeal(item)"
              >
                处理
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <img src="../assets/image/empty.png" alt="" class="empty-icon" />
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page="pageIndex"
        :page-size="pageSize"
        :page-sizes="[15, 30, 50, 100]"
        :background="true"
        layout="total,  prev, pager, next, jumper, sizes"
        :total="total"
        @size-change="handleSizeChange"
        pager-count="4"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";
const emit = defineEmits(["update:pageIndex", "update:pageSize", "refresh"]);

// import PentagonChart from './PentagonChart.vue';

defineProps({
  backlogList: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  pageIndex: {
    type: Number,
    default: 1,
  },
});

const userType = localStorage.getItem("userType");
// 获取告警类型对应的样式类名
const getAlarmTypeClass = (type) => {
  const classMap = {
    超长通话: "type-long",
    话后超时: "type-long",
    静音: "type-fast",
    语速过快: "type-interrupt",
    抢话: "type-violation",
    坐席违规词: "type-sensitive",
    市民敏感词: "type-sensitive",
    求助: "type-sensitive",
  };
  return classMap[type] || "";
};
//告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民敏感词 8.求助
const AlarmType = {
  1: "超长通话",
  2: "话后超时",
  3: "静音",
  4: "语速过快",
  5: "抢话",
  6: "坐席违规词",
  7: "市民敏感词",
  8: "求助",
};

const handleDeal = (item) => {
  emit("update:deal", { ...item, type: "deal" });
};

const handleTransfer = (item) => {
  emit("update:deal", { ...item, type: "transfer" });
};

const handleSizeChange = (val) => {
  emit("update:pageSize", val);
  emit("update:pageIndex", 1);
  emit("refresh");
};

const handleCurrentChange = (val) => {
  emit("update:pageIndex", val);
  emit("refresh");
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;
  .history-record-info {
    width: 632px;
    height: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    .history-record-list {
      margin-top: 16px;
      flex: 1;

      .history-record-item {
        background: rgba(0, 128, 255, 0.1);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;

        .item-left {
          margin-right: 16px;
          background: url("../assets/image/backlog.png") no-repeat center center;
          background-size: 100% 100%;
          width: 64px;
          height: 64px;
        }

        .item-right {
          flex: 1;

          .phone-number {
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 8px;
            display: flex;
            gap: 12px;
            align-items: center;
            span {
              font-size: 24px;
            }
          }

          .call-info {
            display: flex;
            gap: 24px;

            .time {
              .label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
              }
              .value {
                font-size: 14px;
                font-weight: normal;
                text-align: center;
                letter-spacing: normal;
                color: #ffffff;
              }
            }
          }
        }

        .alarm-type {
          padding: 4px 12px;
          border-radius: 4px;
          font-size: 14px;

          &.type-long {
            background: rgba(220, 0, 85, 0.1);
            color: #dc0055;
          }

          &.type-silent {
            background: rgba(0, 150, 136, 0.1);
            color: #009688;
          }

          &.type-fast {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
          }

          &.type-interrupt {
            background: rgba(156, 39, 176, 0.1);
            color: #9c27b0;
          }

          &.type-violation {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
          }

          &.type-sensitive {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
          }
        }
      }
      .active {
        background: rgba(255, 220, 0, 0.2) !important;
        box-sizing: border-box;
        /* 辅助色/湘叶 */
        border: 1px solid #ffdc00 !important;
        box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72) !important;
        position: relative;
      }
    }

    .empty-state {
      margin-top: 20vh;
      text-align: center;

      .empty-icon {
        width: 308px;
        height: 252px;
      }

      .empty-text {
        font-size: 14px;
        color: #fff;
      }
    }

    .btnBox {
      display: flex;
      gap: 8px;
      position: absolute;
      bottom: 16px;
      right: 16px;
      .btn {
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        padding: 5px 16px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);

        &:hover {
          background: rgba(0, 128, 255, 0.2);
        }
      }
    }
  }
}

.pagination {
  position: relative;
  bottom: 0;
  margin-bottom: 8px;
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  :deep(.el-pagination) {
    .el-input__inner {
      color: #fff;
    }
    .is-active {
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 128, 255, 0.3) 2%,
        rgba(0, 128, 255, 0.2) 100%
      ) !important;
      background-color: none !important;
      box-sizing: border-box;
      border: 1px solid #0080ff;
      box-shadow: inset 0px 0px 12px 0px #0080ff;
    }
    .el-pagination__total {
      color: #fff;
    }
    .el-pagination__jump {
      color: #fff;
    }

    .el-select {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      height: 32px;
      color: rgba(0, 255, 255, 1);
    }
    .el-select__selected-item {
      color: rgba(0, 255, 255, 1);
    }

    .el-pager {
      gap: 8px;
      margin-left: 8px;
      margin-right: 8px;
    }
    .number,
    .more {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
    .btn-prev,
    .btn-next {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
  }
}
</style>
