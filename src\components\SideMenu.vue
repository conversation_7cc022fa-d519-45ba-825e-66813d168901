<template>
  <div class="side-menu" :class="{ reduce: isReduce }">
    <div class="menu-title">
      <span class="fontStyle">{{ data.NAME }}</span>
      <img
        src="../assets/image/left-indent.png"
        alt="缩小"
        @click="handleReduce"
      />
    </div>
    <div class="menu-content">
      <div class="menu-group">
        <div
          class="group-title"
          @click="handleMenuClick($event, data.CODE, data)"
          :class="{
            active: expandedGroups.includes(data.CODE),
            menuActive: currentIndex === data.CODE,
          }"
        >
          {{ data.NAME }}
          <!-- <div
            v-if="data.hfWarnList && data.hfWarnList.length"
            class="warn-dot"
          ></div> -->
        </div>
        <div v-show="expandedGroups.includes(data.CODE)" class="group-content">
          <div
            v-for="level1 in data.children"
            :key="level1.CODE"
            class="sub-group"
          >
            <div
              class="sub-group-title"
              @click="handleMenuClick($event, level1.CODE, level1)"
              :class="{
                active: expandedSubGroups.includes(level1.CODE),
                menuActive: currentIndex === level1.CODE,
              }"
            >
              {{ level1.NAME }}
              <!-- <div
                v-if="level1.hfWarnList && level1.hfWarnList.length"
                class="warn-dot"
              ></div> -->
            </div>
            <div v-show="expandedSubGroups.includes(level1.CODE)">
              <div
                v-for="level2 in level1.children"
                :key="level2.CODE"
                :class="[
                  'menu-item',
                  { menuActive: currentIndex === level2.CODE },
                ]"
                @click="handleMenu(level2.CODE, level2)"
              >
                {{ level2.NAME }}
                <!-- <div
                  v-if="level2.hfWarnList && level2.hfWarnList.length"
                  class="warn-dot"
                ></div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="menu-footer">
      <div class="menu-footer-item hover-trigger" @click="handleTodoClick">
        <div class="menu-footer-item-content">
          <img src="../assets/image/left/todo-icon.png" alt="待办列表" />
          <span class="fontStyle">待办列表</span>
        </div>
        <div class="dot" v-if="totalValue > 0"></div>
      </div>
      <div
        class="menu-footer-item hover-trigger"
        @mouseleave="showMessageMenu = false"
        @mouseover="showMessageMenu = true"
      >
        <div class="menu-footer-item-content">
          <img src="../assets/image/left/message-icon.png" alt="消息明细" />
          <span class="fontStyle">消息明细</span>
        </div>
        <!-- <div class="dot"></div> -->

        <downBg style="top: -320%; left: 0" v-show="showMessageMenu">
          <div class="down-menu" @mouseover="showMessageMenu = true">
            <div
              class="down-menu-item hover-trigger"
              @click="handleMessageClick(0)"
            >
              <img src="../assets/image/left/zsxxmx.png" alt="助手消息明细" />
              <span class="fontStyle">助手消息明细</span>
            </div>
            <div
              class="down-menu-item hover-trigger"
              @click="handleMessageClick(1)"
            >
              <img src="../assets/image/left/dxxfmx.png" alt="短信下发明细" />
              <span class="fontStyle">短信下发明细</span>
            </div>
            <div
              class="down-menu-item hover-trigger"
              @click="handleMessageClick(2)"
            >
              <img src="../assets/image/left/whtzmx.png" alt="外呼通知明细" />
              <span class="fontStyle">外呼通知明细</span>
            </div>
          </div>
        </downBg>
      </div>
      <div
        class="menu-footer-item hover-trigger"
        @mouseleave="showTemplateMenu = false"
        @mouseover="showTemplateMenu = true"
      >
        <div class="menu-footer-item-content">
          <img src="../assets/image/left/template-icon.png" alt="模板管理" />
          <span class="fontStyle">模板管理</span>
        </div>
        <!-- <div class="dot"></div> -->

        <downBg style="top: -240%; left: 0" v-show="showTemplateMenu">
          <div class="down-menu" @mouseover="showTemplateMenu = true">
            <div
              class="down-menu-item hover-trigger"
              @click="handleTemplateClick"
            >
              <img src="../assets/image/left/edit-icon.png" alt="新建模版" />
              <span class="fontStyle">新建模版</span>
            </div>
            <div
              class="down-menu-item hover-trigger"
              @click="handleTemplateListClick"
            >
              <img
                src="../assets/image/left/tempList-icon.png"
                alt="新建模版"
              />
              <span class="fontStyle">模板列表</span>
            </div>
          </div>
        </downBg>
      </div>
    </div>
  </div>
  <div>
    <img
      v-if="isReduce"
      class="reduce-btn"
      src="../assets/image/btnsx.png"
      alt="缩小"
      @click="handleReduce"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits, defineProps, computed } from "vue";
import downBg from "./downBg.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  currentIndex: {
    type: [String, Number],
    default: "",
  },
  csList: Object,
  form: Object,
  contentCards: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits([
  "update:form",
  "menu-click",
  "todo-click",
  "template-list-click",
  "template-click",
  "show-message-list",
]);

const showMessageMenu = ref(false);
const showTemplateMenu = ref(false);
const isReduce = ref(false);

const handleMenu = (index, item) => {
  emit("menu-click", index, item);
};

const handleReduce = () => {
  isReduce.value = !isReduce.value;
};
// 存储展开的一级和二级菜单
const expandedGroups = ref([]);
const expandedSubGroups = ref([]);

const handleTemplateClick = () => {
  emit("template-click");
};

// 计算 contentCards 中 value 的总和
const totalValue = computed(() => {
  return (
    props.contentCards?.reduce(
      (sum, card) => sum + (Number(card.value) || 0),
      0
    ) || 0
  );
});

// 默认展开菜单
onMounted(() => {
  setTimeout(() => {
    if (props.data) {
      // 默认展开最外层节点
      expandedGroups.value = [props.data.CODE];

      // 默认展开第一个一级节点
      if (props.data.children && props.data.children.length > 0) {
        expandedSubGroups.value = [props.data.children[0].CODE];
        localStorage.setItem("currentHfCode", props.data.CODE);
      }
    }
  }, 500);
});

// 处理菜单点击，同时处理展开/折叠
const handleMenuClick = (event, code, item) => {
  // 阻止事件冒泡
  event.stopPropagation();
  localStorage.setItem("currentHfCode", code);
  // 处理展开/折叠
  if (item.children) {
    if (item === props.data) {
      // 最外层节点
      const index = expandedGroups.value.indexOf(code);
      if (index === -1) {
        expandedGroups.value.push(code);
      } else {
        expandedGroups.value.splice(index, 1);
      }
    } else {
      // 一级节点
      const index = expandedSubGroups.value.indexOf(code);
      if (index === -1) {
        expandedSubGroups.value.push(code);
      } else {
        expandedSubGroups.value.splice(index, 1);
      }
    }
  }

  // 添加这一行，让父级菜单项也可以被选中
  emit("menu-click", code, item);
};

// 处理待办列表点击事件
const handleTodoClick = () => {
  emit("todo-click");
};

// 处理模板列表点击事件
const handleTemplateListClick = () => {
  emit("template-list-click");
};

// 处理消息明细点击事件
const handleMessageClick = (type) => {
  emit("show-message-list", type);
};
</script>

<style lang="scss" scoped>
.reduce {
  width: 0px !important;
}
.reduce-btn {
  width: 32px;
  height: 38px;
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 0px;
}
.side-menu {
  width: 264px;
  background: rgba(0, 128, 255, 0.1);
  border-radius: 4px;
  padding: 16px 0 0;
  margin-right: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .menu-title {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    padding: 0 24px;
    // margin-bottom: 16px;
    flex: none;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    img {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }

  .menu-content {
    position: relative;
    flex: 1;

    .menu-group {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: 24px;
        top: 0;
        bottom: 0;
        width: 1px;
        background: rgba(0, 255, 255, 0.1);
      }

      .group-title {
        padding: 0 24px 0 44px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          left: 24px;
          top: 50%;
          width: 12px;
          height: 1px;
          background: rgba(0, 255, 255, 0.1);
        }

        &:hover {
          background: rgba(0, 128, 255, 0.2);
        }

        &.active {
          color: #00ffff;
          &::before {
            background: #00ffff;
          }
        }

        &.menuActive {
          background: #0555ce;
          color: #fff;
          &::before {
            background: #fff;
          }
        }

        .warn-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #f5222d;
          margin-left: 8px;
        }
      }

      .group-content {
        position: relative;

        .sub-group {
          position: relative;

          &::before {
            content: "";
            position: absolute;
            left: 44px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(0, 255, 255, 0.1);
          }

          .sub-group-title {
            padding: 0 24px 0 64px;
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s;
            position: relative;

            &::before {
              content: "";
              position: absolute;
              left: 44px;
              top: 50%;
              width: 12px;
              height: 1px;
              background: rgba(0, 255, 255, 0.1);
            }

            &:hover {
              background: rgba(0, 128, 255, 0.2);
            }

            &.active {
              color: #00ffff;
              &::before {
                background: #00ffff;
              }
            }

            &.menuActive {
              background: #0555ce;
              color: #fff;
              &::before {
                background: #fff;
              }
            }

            .warn-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: #f5222d;
              margin-left: 8px;
            }
          }

          .menu-item {
            padding: 0 24px 0 84px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            position: relative;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &::before {
              content: "";
              position: absolute;
              left: 64px;
              top: 50%;
              width: 12px;
              height: 1px;
              background: rgba(0, 255, 255, 0.1);
            }

            &:hover {
              background: rgba(0, 128, 255, 0.2);
            }

            &.menuActive {
              background: #0555ce;
              color: #fff;
              &::before {
                background: #fff;
              }
            }

            .warn-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: #f5222d;
            }
          }
        }
      }
    }
  }

  .menu-footer {
    flex: none;
    font-size: 16px;
    color: #fff;
    border-top: 1px solid rgba(0, 255, 255, 0.1);

    &-item {
      height: 48px;
      line-height: 48px;
      padding: 0 24px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      position: relative;

      &-content {
        display: flex;
        align-items: center;
        padding: 0 8px;
        border-radius: 4px;
      }
      &.hover-trigger:hover {
        background: rgba(0, 128, 255, 0.2);
      }
      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }

      .dot {
        width: 8px;
        height: 8px;
        background: #dc0055;
        border-radius: 50%;
      }
    }
  }
  .menu-item {
    padding: 0 40px;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background: rgba(0, 128, 255, 0.2);
    }

    &.menuActive {
      background: #0555ce;
      color: #fff;
    }

    .warn-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #f5222d;
    }
  }
}

.side-select {
  width: 160px;
}

.down-menu {
  font-size: 14px;
  // background: rgba(0, 128, 255, 0.1);
  border-radius: 4px;
  padding: 8px 0;

  &-item {
    line-height: 40px;
    padding: 0 16px;
    cursor: pointer;
    display: flex;
    align-items: center;

    &.hover-trigger:hover {
      background: rgba(0, 128, 255, 0.2);
    }

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
}

:deep(.el-input__inner) {
  color: #fff;
  background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
  color: #fff;
}

:deep(.el-select__input::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
  color: #00ffff;
}

:deep(.el-select) {
  background-color: transparent;
}

:deep(.downBg) {
  position: absolute;
  z-index: 999;
}
</style>
