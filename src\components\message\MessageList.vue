<template>
  <div class="content-box">
    <!-- 添加筛选条件区域 -->
    <div class="filter-container" v-if="messageType !== undefined">
      <div class="filter-item">
        <span>时间</span>
        <el-date-picker
          v-model="filterParams.startTime"
          type="datetime"
          clearable
          placeholder="开始时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="date-picker"
        ></el-date-picker>
        <span class="filter-item-separator">-</span>
        <el-date-picker
          v-model="filterParams.endTime"
          type="datetime"
          clearable
          placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        ></el-date-picker>
      </div>
      <div class="filter-item"  v-if="messageType === 0">
          <span>消息类别</span>
          <el-select
            v-model="filterParams.msgRemindType"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in messageTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      <!-- 短信和外呼通知明细的筛选条件 -->
      <template v-if="messageType === 2 || messageType === 1">
        <div class="filter-item" v-if="messageType === 1">
          <span>发送状态</span>
          <el-select
            v-model="filterParams.sendState"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in sendStateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
      </div>
      <div class="filter-item" v-if="messageType === 1">
        <span>回执状态</span>
        <el-select
          v-model="filterParams.receiptState"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in receiptStateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
        <div class="filter-item">
          <span>坐席姓名</span>
          <el-input
            v-model="filterParams.agentName"
            clearable
            placeholder="请输入"
          ></el-input>
        </div>
        <div class="filter-item">
          <span>坐席工号</span>
          <el-input
            v-model="filterParams.agentNum"
            clearable
            placeholder="请输入"
          ></el-input>
        </div>
        <div class="filter-item">
          <span>坐席手机号</span>
          <el-input
            v-model="filterParams.phone"
            clearable
            placeholder="请输入"
          ></el-input>
        </div>
        <div class="filter-item">
          <span>提交人</span>
          <el-input
            v-model="filterParams.submitUser"
            clearable
            placeholder="请输入"
          ></el-input>
        </div>
      </template>
      <div class="filter-item" v-if="messageType === 2">
        <span>呼叫结果</span>
        <el-select
          v-model="filterParams.callResult"
          clearable
          placeholder="请选择"
          @clear="filterParams.callResult = ''"
        >
          <el-option
            v-for="item in receiptStateOptionsCall"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="filter-buttons">
        <div class="filter-button" @click="handleSearch"></div>
        <div class="filter-button" @click="handleReset"></div>
        <div class="filter-button" @click="handleExport" v-if="messageType != 0"></div>
      </div>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    <div v-else class="history-record-info">
      <div class="history-record-list" v-if="backlogList?.length">
        <div
          v-for="(item, index) in backlogList"
          :key="index"
          class="history-record-item"
        >
          <div class="item-right" v-if="messageType != 0">
            <div class="phone-number">
              <span>{{ item.CUST_NAME }}</span>
            </div>
            <div class="call-info">
              <!-- 短信和外呼通知明细的显示内容 -->
              <template v-if="messageType === 2 || messageType === 1">
                <div class="time" v-if="item.AGENT_PHONE">
                  <span class="label">坐席工号：</span>
                  <span class="value number">{{ item.AGENT_PHONE }}</span>
                </div>
                <div class="time" v-if="item.PHONE">
                  <span class="label">坐席手机号：</span>
                  <span class="value number">{{ item.PHONE }}</span>
                </div>
                <div class="time" v-if="item.WORK_GROUP_NAME">
                  <span class="label">所在班组：</span>
                  <span class="value">{{ item.WORK_GROUP_NAME }}</span>
                </div>
                <div class="time" v-if="item.CREATED_TIME && messageType === 1">
                  <span class="label">发送时间：</span>
                  <span class="value number">{{ item.CREATED_TIME }}</span>
                </div>
                <div class="time" v-if="messageType === 1">
                  <span class="label">回执状态：</span>
                  <span
                    class="value addNumber"
                    :class="{
                      success: item.RECEIPT_STATE === '1',
                      error: item.RECEIPT_STATE === '0',
                      unknown: item.RECEIPT_STATE === '2',
                    }"
                    >{{ getReceiptStateText(item.RECEIPT_STATE) || "暂无" }}</span
                  >
                </div>
                <div class="time" v-if="messageType === 2">
                  <span class="label">呼叫结果：</span>
                  <span
                    class="value addNumber"
                    :class="{
                      success: item.CALL_RESULT === '0',
                      error: item.CALL_RESULT !== '0',
                      unknown: item.CALL_RESULT === '',
                    }"
                    >{{ getCallResultText(item.CALL_RESULT) || "未知" }}</span
                  >
                </div>
                <div class="time" v-if="messageType === 2">
                  <span class="label">外呼开始时间：</span>
                  <span class="value">{{ item.START_TIME || "暂无" }}</span>
                </div>
                <div class="time" v-if="messageType === 2">
                  <span class="label">外呼结束时间：</span>
                  <span class="value">{{ item.END_TIME || "暂无" }}</span>
                </div>
                <div class="time" v-if="item.CREATED_BY">
                  <span class="label">提交人：</span>
                  <span class="value">{{ item.CREATED_BY || "暂无" }}</span>
                </div>
                <div class="time">
                  <span class="label">提交时间：</span>
                  <span class="value">{{
                    item.SUBMIT_TIME || item.CREATED_TIME || "暂无"
                  }}</span>
                </div>
              </template>
                 <!-- 根据消息类型显示信息 -->
              <template v-else>
                <div class="time" v-if="item.ALARM_TIME">
                  <span class="label">触发时间：</span>
                  <span class="value number">{{ item.ALARM_TIME }}</span>
                </div>
              </template>
            </div>

            <div class="call-info-content">
              <div class="content-title">
                <span v-if="messageType == 1">短信内容:</span>
                <span v-if="messageType == 2">外呼内容:</span>
              </div>
              <div class="content-text">
                {{ item.CONTENT }}
              </div>
            </div>
            <div class="rtTag" v-if="messageType == 1">
              <span :class="getSendStateClass(item.SEND_STATE)">{{
                getSendStateText(item.SEND_STATE)
              }}</span>
            </div>
          </div>
          <div class="item-assess" v-if="messageType == 0">
            <div class="phone-number">
              <span>{{ item.MSG_NAME }}</span>
            </div>
            <div class="call-info">
              <div class="time" v-if="item.MSG_TIME || item.PUBLISH_TIME">
                <span class="label">消息发布时间：</span>
                <span class="value number">{{
                  item.MSG_TIME || item.PUBLISH_TIME
                }}</span>
              </div>
              <div class="time" v-if="item.PUBLISHER">
                <span class="label">提交人：</span>
                <span class="value">{{ item.PUBLISHER }}</span>
              </div>
            </div>

            <div class="call-info-number">
              <div class="item">
                <span class="label">送达数：</span>
                <span class="value number">{{ item.SEND_COUNT || 0 }}</span>
              </div>
              <div class="item">
                <span class="label">已读数：</span>
                <span class="value number">{{ item.READ_COUNT || 0 }}</span>
              </div>
              <div class="item">
                <span class="label">已读率：</span>
                <span class="value number">{{ item.READ_RATE || 0 }}</span>
              </div>
            </div>
            <div class="rtTag">
              <span :class="getMessageStatusClass(item.MSG_STATUS)">{{
                getMessageStatusText(item.MSG_STATUS)
              }}</span>
            </div>
            <div class="btnBox">
              <div class="btn" @click="handleViewDetail(item)">查看详情</div>
              <div class="btn" @click="handleDeal(item)">查看明细</div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <img src="../../assets/image/empty.png" alt="" class="empty-icon" />
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page="pageIndex"
        :page-size="pageSize"
        :page-sizes="[15, 30, 50, 100]"
        :background="true"
        layout="total,  prev, pager, next, jumper, sizes"
        :total="total"
        @size-change="handleSizeChange"
        pager-count="4"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
// import { exportCallDetail,exportSmsDetail } from "@/api/index";
import { defaultStartTime, todayStartTime, receiptStateOptionsCall, messageTypeOptions, sendStateOptions, receiptStateOptions } from "@/utils/messageList/messageList";
import {ElMessageBox,ElMessage } from 'element-plus'
import { defineProps, defineEmits, reactive, watch } from "vue";
const emit = defineEmits([
  "update:pageIndex",
  "update:pageSize",
  "refresh",
  "update:deal",
  "view-detail",
  "view-inner-detail",
  "loading",
]);

const props = defineProps({
  messageType: {
    type: Number,
    default: undefined,
  },
  backlogList: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  pageIndex: {
    type: Number,
    default: 1,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
// 筛选参数
const filterParams = reactive({
  msgRemindType: "",//消息类型
  startTime: defaultStartTime(),//开始时间
  endTime: todayStartTime(),//结束时间
  agentName: "",//坐席姓名
  agentNum: "",//坐席工号
  phone: "",//坐席手机号
  submitUser: "",//提交人
  sendState: "",//发送状态
  receiptState: "",//回执状态
  callResult: "",//外呼结果
  pageIndex: 1,
  pageSize: 15,
});

// 监听页码和每页条数变化
watch(() => props.pageIndex,
  (newVal) => {
    filterParams.pageIndex = newVal;
  },
  { immediate: true }
);

watch( () => props.pageSize,
  (newVal) => {
    filterParams.pageSize = newVal;
  },
  { immediate: true }
);

// 获取发送状态文本
const getSendStateText = (state) => {
  const states = {
    0: "待发送",
    1: "发送中",
    2: "发送成功",
    3: "发送失败",
  };
  console.log(states[Number(state)]);
  return states[Number(state)] || "未知";
};

// 获取发送状态对应的类名
const getSendStateClass = (state) => {
  const stateNum = Number(state);
  if (stateNum === 2) return "success"; // 发送成功
  if (stateNum === 1) return "warning"; // 发送中
  if (stateNum === 3) return "error"; // 发送失败
  return ""; // 待发送或未知
};

// 获取消息状态文本
const getMessageStatusText = (status) => {
  const statuses = {
    0: "已发送",
    1: "等待发送",
    2: "取消发送",
  };
  return statuses[status] || "已发布";
};

// 获取消息状态对应的类名
const getMessageStatusClass = (status) => {
  const statusNum = Number(status);
  if (statusNum === 0) return "success"; // 已发送
  if (statusNum === 1) return "warning"; // 等待发送
  if (statusNum === 2) return "error"; // 取消发送
  return "success"; // 默认为已发布
};

// 获取回执状态文本
const getReceiptStateText = (state) => {
  const states = {
    0: "失败",
    1: "成功",
    2: "未知",
  };
  return states[state] || "未知";
};

// 获取呼叫结果文本
const getCallResultText = (result) => {
  const results = {
    0: "成功",
    1: "无人应答",
    2: "用户忙",
    3: "用户挂机",
    4: "网络忙",
    5: "空号",
    6: "用户拒绝",
    7: "关机",
    8: "暂停服务",
    9: "不在服务区",
    999: "其他呼叫失败",
  };
  return results[result] || "未知";
};

const handleDeal = (item) => {
  console.log("查看明细:", item);
  emit("view-inner-detail", item);
};

const handleViewDetail = (item) => {
  console.log("查看详情:", item);
  emit("view-detail", item);
};

const handleSizeChange = (val) => {
  filterParams.pageSize = val;
  filterParams.pageIndex = 1;
  emit("refresh", { ...filterParams });
};

const handleCurrentChange = (val) => {
  filterParams.pageIndex = val;
  emit("refresh", { ...filterParams });
};

// 处理查询
const handleSearch = () => {
  // emit("loading", true);
  filterParams.pageIndex = 1;
  emit("refresh", { ...filterParams });
};

// 处理重置
const handleReset = () => {
    filterParams.agentName = "";
    filterParams.agentNum = "";
    filterParams.phone = "";
    filterParams.submitUser = "";
    filterParams.sendState = "";
    filterParams.receiptState = "";
    filterParams.callResult = "";
    filterParams.startTime = defaultStartTime();
    filterParams.endTime = todayStartTime();
    filterParams.pageIndex = 1;
    filterParams.pageSize = 15;
    filterParams.msgRemindType = "";
  // 触发刷新
  emit("refresh", { ...filterParams });
};


const handleExport = () => {//messageType为2时，导出外呼明细报表，消息类型为1时，导出短信明细报表
  if (!filterParams.startTime || !filterParams.endTime) {
    ElMessage.warning('请选择开始时间和结束时间')
    return;
  }
  if (props.messageType === 2) {//外呼明细导出
    ElMessageBox.confirm('是否导出外呼明细报表', '导出提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
        const paramsOrigin ={
          pageType:"3",
          pageIndex:filterParams.pageIndex,
          pageSize:filterParams.pageSize,
          startTime:filterParams.startTime,
          endTime:filterParams.endTime,
          agentName:filterParams.agentName,
          agentNum:filterParams.agentNum,
          phone:filterParams.phone,
          submitUser:filterParams.submitUser,
          callResult:filterParams.callResult
      }
        const paramsUrl = new URLSearchParams()
        Object.keys(paramsOrigin).forEach((key) => {
          paramsUrl.append(key, paramsOrigin[key] || '')
        })
        window.location.href = `/cx-monitordata-12345/servlet/export?action=exportCallDetail&${paramsUrl.toString()}`
      })
      .catch(() => {})
  } else if (props.messageType === 1) {//短信明细导出
    ElMessageBox.confirm('是否导出短信明细报表', '导出提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
        const paramsOrigin2 ={
          pageType:"3",
          pageIndex:filterParams.pageIndex,
          pageSize:filterParams.pageSize,
          startTime:filterParams.startTime,
          endTime:filterParams.endTime,
          agentName:filterParams.agentName,
          agentNum:filterParams.agentNum,
          phone:filterParams.phone,
          submitUser:filterParams.submitUser,
          receiptState:filterParams.receiptState,
          sendState:filterParams.sendState,
        }
      const paramsUrl = new URLSearchParams()
        Object.keys(paramsOrigin2).forEach((key) => {
          paramsUrl.append(key, paramsOrigin2[key] || '')
        })
        window.location.href = `/cx-monitordata-12345/servlet/export?action=exportSmsDetail&${paramsUrl.toString()}`

      })
      .catch(() => {})
  }
  // // 这里可以调用 API 导出数据
  // emit("export", { ...filterParams });
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  /* 筛选条件样式 */
  .filter-container {
    max-width: 1212px;
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    position: relative;
    box-sizing: border-box;
    .filter-item {
      display: flex;
      align-items: center;
      color: #fff;
      span {
        margin-right: 8px;
        white-space: nowrap;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
      }
      .filter-item-separator {
        margin-left: 8px;
      }

      // :deep(.el-input) {
      //   width: 200px !important;
      // }

      :deep(.el-select) {
        width: 140px !important;
      }

      :deep(.el-date-editor.el-input),
      :deep(.el-date-editor--datetime) {
         width: 200px !important;
      }

      // :deep(.el-input__wrapper) {
      //   width: 100%;
      // }

      // :deep(.el-input__inner::placeholder) {
      //   color: rgba(255, 255, 255, 0.5);
      // }
    }

    .filter-buttons {
      display: flex;
      gap: 8px;
      // margin-left: auto;
      .filter-button {
        width: 80px;
        height: 32px;

        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        &:active {
          transform: scale(0.9);
        }
        &:nth-child(1) {
          background: url("../../assets/image/search.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        &:nth-child(2) {
          background: url("../../assets/image/reset.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        &:nth-child(3) {
          background: url("../../assets/image/exportbtn.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
      }

      .export-button {
        width: 80px;
        height: 32px;
      }
    }
  }

  .history-record-info {
    width: 1212px;
    height: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    .history-record-list {
      margin-top: 16px;
      flex: 1;

      .history-record-item {
        .item-right {
          background: rgba(0, 128, 255, 0.1);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          // display: flex;
          align-items: center;
          cursor: pointer;
          transition: all 0.3s;
          position: relative;
          background-image: url("../../assets/image/mxbg.png");
          background-size: 120px 120px;
          background-repeat: no-repeat;
          background-position: 40px top;
          .rtTag {
            position: absolute;
            top: 0;
            right: 0;
            overflow: hidden;
            width: 80px;
            height: 80px;

            span {
              position: absolute;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 120px;
              height: 28px;
              background: #00ffff;
              font-size: 12px;
              font-weight: bold;
              transform: rotate(45deg);
              transform-origin: bottom right;
              top: 50px;
              right: -10px;
              text-align: center;

              &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
              }
            }

            /* 不同状态使用不同颜色 */
            .success {
              background: rgba(0, 220, 85, 0.1);
              color: rgb(0, 220, 85);
            }

            .warning {
              background: rgba(255, 170, 0, 0.1);
              color: #ffaa00;
            }

            .error {
              background: rgba(220, 0, 85, 0.1);
              color: #dc0055;
            }
          }
          .phone-number {
            font-size: 16px;
            color: #ffffff;
            display: flex;
            gap: 12px;
            align-items: center;
            span {
              font-size: 24px;
            }
          }

          .call-info {
            display: grid;
            grid-template-columns:repeat(4, 1fr);
            gap: 8px;
            flex-wrap: wrap;
            .time {
              display: flex;
              align-items: center;
              .label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
                flex-shrink: 0;
              }
              .value {
                flex-grow: 1;
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                letter-spacing: normal;
                color: #ffffff;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .addNumber {
                max-width: 120px !important;
              }
              .success {
                color: #00ff00;
                background: rgba(0, 255, 0, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
              .error {
                color: #ff0000;
                background: rgba(255, 0, 0, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
              .unknown {
                color: rgb(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
            }
          }

          .call-info-content {
            margin-top: 16px;
            border-radius: 4px;
            background: rgba(0, 128, 255, 0.1);
            padding: 8px 16px;
            .content-title {
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              letter-spacing: normal;
              color: rgba(255, 255, 255, 0.6);
            }
          }
        }
        .item-assess {
          background: rgba(0, 128, 255, 0.1);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          // display: flex;
          align-items: center;
          cursor: pointer;
          transition: all 0.3s;
          position: relative;
          background-image: url("../../assets/image/zsbg.png");
          background-size: 120px 120px;
          background-repeat: no-repeat;
          background-position: 40px top;
          .rtTag {
            position: absolute;
            top: 0;
            right: 0;
            overflow: hidden;
            width: 80px;
            height: 80px;

            span {
              position: absolute;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 120px;
              height: 28px;
              background: #00ffff;
              font-size: 12px;
              font-weight: bold;
              transform: rotate(45deg);
              transform-origin: bottom right;
              top: 50px;
              right: -10px;
              text-align: center;

              &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
              }
            }

            /* 不同状态使用不同颜色 */
            .success {
              background: rgba(0, 220, 85, 0.1);
              color: rgb(0, 220, 85);
            }

            .warning {
              background: rgba(255, 170, 0, 0.1);
              color: #ffaa00;
            }

            .error {
              background: rgba(220, 0, 85, 0.1);
              color: #dc0055;
            }
          }
          .phone-number {
            font-size: 16px;
            color: #ffffff;
            display: flex;
            gap: 12px;
            align-items: center;
            span {
              font-size: 24px;
            }
          }

          .call-info {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 8px;
            flex-wrap: wrap;

            .time {
              display: flex;
              align-items: center;
              .label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
                flex-shrink: 0;
              }
              .value {
                flex-grow: 1;
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                letter-spacing: normal;
                color: #ffffff;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .success {
                color: #00ff00;
                background: rgba(0, 255, 0, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
              .error {
                color: #ff0000;
                background: rgba(255, 0, 0, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
              .unknown {
                color: rgb(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                text-align: center;
              }
            }
          }
          .call-info-number {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            .item {
              padding: 6px 16px;
              border-radius: 4px;
              background: rgba(0, 128, 255, 0.1);
              display: flex;
              .label {
                font-size: 14px;
                font-weight: normal;
                letter-spacing: normal;
                color: rgba(255, 255, 255, 0.6);
              }
              .value {
                font-size: 14px;
                font-weight: normal;
                letter-spacing: normal;
                color: #00ffff;
              }
            }
          }
        }
        .alarm-type {
          padding: 4px 12px;
          border-radius: 4px;
          font-size: 14px;

          &.type-long {
            background: rgba(220, 0, 85, 0.1);
            color: #dc0055;
          }

          &.type-silent {
            background: rgba(0, 150, 136, 0.1);
            color: #009688;
          }

          &.type-fast {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
          }

          &.type-interrupt {
            background: rgba(156, 39, 176, 0.1);
            color: #9c27b0;
          }

          &.type-violation {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
          }

          &.type-sensitive {
            background: rgba(103, 58, 183, 0.1);
            color: #673ab7;
          }
        }

        .btnBox {
          display: flex;
          gap: 8px;
          position: absolute;
          bottom: 16px;
          right: 16px;
          .btn {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            letter-spacing: normal;
            /* 主色/卓越青 */
            color: #00ffff;
            padding: 5px 16px;
            border-radius: 4px;
            background: rgba(0, 128, 255, 0.1);

            &:hover {
              background: rgba(0, 128, 255, 0.2);
            }
          }
        }
      }
    }

    .empty-state {
      margin-top: 20vh;
      text-align: center;

      .empty-icon {
        width: 308px;
        height: 252px;
      }

      .empty-text {
        font-size: 14px;
        color: #fff;
      }
    }
  }
}
/* 添加loading样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 1212px;
  height: 100%;
  color: #ffffff;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
  border: 4px solid rgba(0, 255, 255, 0.2);
  border-top: 4px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #00ffff;
}
.pagination {
  position: relative;
  bottom: 0;
  margin-bottom: 8px;
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  :deep(.el-pagination) {
    .el-input__inner {
      color: #fff;
    }
    .is-active {
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 128, 255, 0.3) 2%,
        rgba(0, 128, 255, 0.2) 100%
      ) !important;
      background-color: none !important;
      box-sizing: border-box;
      border: 1px solid #0080ff;
      box-shadow: inset 0px 0px 12px 0px #0080ff;
    }
    .el-pagination__total {
      color: #fff;
    }
    .el-pagination__jump {
      color: #fff;
    }

    .el-select {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      height: 32px;
      color: rgba(0, 255, 255, 1);
    }
    .el-select__selected-item {
      color: rgba(0, 255, 255, 1);
    }

    .el-pager {
      gap: 8px;
      margin-left: 8px;
      margin-right: 8px;
    }
    .number,
    .more {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
    .btn-prev,
    .btn-next {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
  }
}
</style>
