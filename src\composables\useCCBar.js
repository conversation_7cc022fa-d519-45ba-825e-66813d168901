import { inject, provide, ref } from 'vue'
import { CCBarService } from '../utils/ccbar/ccbar.es.js'

const CCBAR_INJECTION_KEY = Symbol('ccbar')
const CCBAR_DATA_KEY = Symbol('ccbar-data')

// 提供CCBar服务
export function provideCCBar(config) {
  const ccbarService = CCBarService.getInstance(config)
  const ccbarData = ref({
    skillGroups: [],
    busyTypes: [],
    agentPhones: [],
    initialized: false
  })
  
  provide(CCBAR_INJECTION_KEY, ccbarService)
  provide(CCBAR_DATA_KEY, ccbarData)
  
  return { ccbarService, ccbarData }
}

// 使用CCBar服务
export function useCCBar() {
  const ccbarService = inject(CCBAR_INJECTION_KEY)
  const ccbarData = inject(CCBAR_DATA_KEY)
  
  if (!ccbarService) {
    throw new Error('CCBar服务未初始化')
  }
  
  return { ccbarService, ccbarData }
}


