.el-form-item__label {
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: normal;
    color: #FFFFFF;
}

.el-input {
    background: rgba(0, 128, 255, 0.2);
}

.el-input__wrapper {
    border-radius: 4px;
    background: rgba(0, 128, 255, 0.2);
    box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}

.el-form-item {
    margin: 0;
}

.el-select__wrapper {
    background: rgba(0, 128, 255, 0.2);
    box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}


:deep(.el-input__inner) {
    color: #fff;
    background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
    color: #fff;
}

:deep(.el-select__input::placeholder) {
    color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
    color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
    color: #409eff;
}

:deep(.el-select) {
    background-color: transparent;
}

:deep(.el-tag) {
    background-color: rgba(64, 158, 255, 0.2);
    color: #fff;
    border-color: transparent;
}