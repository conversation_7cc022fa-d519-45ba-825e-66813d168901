<template>
  <div class="base-title">
    <span>
      <slot></slot>
    </span>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.base-title {
  width: 340px;
  height: 42px;
  background: url("../../assets/image/titleBg.png") no-repeat center bottom;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  span {
    /* 文字/运营一级标题 */
    /* 样式描述：适用于推广Banner标题及其他特殊运营场景 */
    font-family: Alibaba PuHuiTi 3;
    font-size: 32px;
    font-weight: bold;
    line-height: 48px;
    text-align: center;
    letter-spacing: normal;
    background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
}
</style>
