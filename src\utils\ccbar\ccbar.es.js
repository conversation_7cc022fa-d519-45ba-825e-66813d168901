import { defineComponent, openBlock, createElementBlock, createElementVNode, computed, ref, reactive, resolveComponent, createBlock, withCtx, createVNode, createTextVNode, toDisplayString, createCommentVNode, withModifiers, Fragment, renderList, watch, onMounted, onUnmounted, normalizeClass, onBeforeUnmount, nextTick, withDirectives, vModelText, unref, vModelCheckbox } from "vue";
import { ElMessage } from "element-plus";
class EventEmitter {
  constructor() {
    this.listeners = {};
    this.isEmitting = {};
  }
  /**
   * 获取事件发射器单例
   */
  static getInstance() {
    if (!EventEmitter.instance) {
      EventEmitter.instance = new EventEmitter();
    }
    return EventEmitter.instance;
  }
  /**
   * 添加事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  on(type, fn) {
    if (typeof type === "string" && typeof fn === "function") {
      if (!this.listeners[type]) {
        this.listeners[type] = [];
      }
      this.listeners[type].push(fn);
    }
    return this;
  }
  /**
   * 添加多个事件监听
   * @param obj 事件映射对象
   */
  addEvents(obj) {
    for (const type in obj) {
      if (obj.hasOwnProperty(type) && typeof obj[type] === "function") {
        this.on(type, obj[type]);
      }
    }
    return this;
  }
  /**
   * 触发事件
   * @param type 事件类型
   * @param args 事件参数
   */
  emit(type, ...args) {
    if (this.isEmitting[type]) {
      console.warn(`检测到可能的事件循环: ${type}`);
      return this;
    }
    this.isEmitting[type] = true;
    // console.log("触发事件", type, args);
    try {
      if (type && this.listeners[type]) {
        const events = {
          type,
          target: this
        };
        const handlers = [...this.listeners[type]];
        for (let i = 0; i < handlers.length; i++) {
          try {
            handlers[i].apply(this, [events, ...args]);
          } catch (e) {
            console.error(`Error in event handler for ${type}:`, e);
          }
        }
      }
    } finally {
      this.isEmitting[type] = false;
    }
    return this;
  }
  /**
   * 删除事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  off(type, fn) {
    const listeners = this.listeners[type];
    if (Array.isArray(listeners)) {
      if (typeof fn === "function") {
        const index = listeners.indexOf(fn);
        if (index !== -1) {
          listeners.splice(index, 1);
        }
      } else {
        delete this.listeners[type];
      }
    }
    return this;
  }
  /**
   * 移除多个事件监听
   * @param types 事件类型数组或事件映射对象
   */
  removeEvents(types) {
    if (Array.isArray(types)) {
      for (let i = 0; i < types.length; i++) {
        this.off(types[i]);
      }
    } else if (typeof types === "object") {
      for (const type in types) {
        if (types.hasOwnProperty(type)) {
          this.off(type, types[type]);
        }
      }
    }
    return this;
  }
  /**
   * 一次性事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  once(type, fn) {
    const wrapper = (...args) => {
      fn.apply(this, args);
      this.off(type, wrapper);
    };
    return this.on(type, wrapper);
  }
}
function bind(fn, thisArg) {
  return function wrap() {
    return fn.apply(thisArg, arguments);
  };
}
const { toString } = Object.prototype;
const { getPrototypeOf } = Object;
const kindOf = ((cache) => (thing) => {
  const str = toString.call(thing);
  return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
})(/* @__PURE__ */ Object.create(null));
const kindOfTest = (type) => {
  type = type.toLowerCase();
  return (thing) => kindOf(thing) === type;
};
const typeOfTest = (type) => (thing) => typeof thing === type;
const { isArray } = Array;
const isUndefined = typeOfTest("undefined");
function isBuffer$1(val) {
  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
}
const isArrayBuffer = kindOfTest("ArrayBuffer");
function isArrayBufferView(val) {
  let result;
  if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
    result = ArrayBuffer.isView(val);
  } else {
    result = val && val.buffer && isArrayBuffer(val.buffer);
  }
  return result;
}
const isString = typeOfTest("string");
const isFunction = typeOfTest("function");
const isNumber = typeOfTest("number");
const isObject = (thing) => thing !== null && typeof thing === "object";
const isBoolean = (thing) => thing === true || thing === false;
const isPlainObject = (val) => {
  if (kindOf(val) !== "object") {
    return false;
  }
  const prototype2 = getPrototypeOf(val);
  return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);
};
const isDate = kindOfTest("Date");
const isFile = kindOfTest("File");
const isBlob = kindOfTest("Blob");
const isFileList = kindOfTest("FileList");
const isStream = (val) => isObject(val) && isFunction(val.pipe);
const isFormData = (thing) => {
  let kind;
  return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || // detect form-data instance
  kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
};
const isURLSearchParams = kindOfTest("URLSearchParams");
const [isReadableStream, isRequest, isResponse, isHeaders] = ["ReadableStream", "Request", "Response", "Headers"].map(kindOfTest);
const trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function forEach(obj, fn, { allOwnKeys = false } = {}) {
  if (obj === null || typeof obj === "undefined") {
    return;
  }
  let i;
  let l;
  if (typeof obj !== "object") {
    obj = [obj];
  }
  if (isArray(obj)) {
    for (i = 0, l = obj.length; i < l; i++) {
      fn.call(null, obj[i], i, obj);
    }
  } else {
    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
    const len = keys.length;
    let key;
    for (i = 0; i < len; i++) {
      key = keys[i];
      fn.call(null, obj[key], key, obj);
    }
  }
}
function findKey(obj, key) {
  key = key.toLowerCase();
  const keys = Object.keys(obj);
  let i = keys.length;
  let _key;
  while (i-- > 0) {
    _key = keys[i];
    if (key === _key.toLowerCase()) {
      return _key;
    }
  }
  return null;
}
const _global = (() => {
  if (typeof globalThis !== "undefined")
    return globalThis;
  return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
})();
const isContextDefined = (context) => !isUndefined(context) && context !== _global;
function merge() {
  const { caseless } = isContextDefined(this) && this || {};
  const result = {};
  const assignValue = (val, key) => {
    const targetKey = caseless && findKey(result, key) || key;
    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
      result[targetKey] = merge(result[targetKey], val);
    } else if (isPlainObject(val)) {
      result[targetKey] = merge({}, val);
    } else if (isArray(val)) {
      result[targetKey] = val.slice();
    } else {
      result[targetKey] = val;
    }
  };
  for (let i = 0, l = arguments.length; i < l; i++) {
    arguments[i] && forEach(arguments[i], assignValue);
  }
  return result;
}
const extend = (a, b, thisArg, { allOwnKeys } = {}) => {
  forEach(b, (val, key) => {
    if (thisArg && isFunction(val)) {
      a[key] = bind(val, thisArg);
    } else {
      a[key] = val;
    }
  }, { allOwnKeys });
  return a;
};
const stripBOM = (content) => {
  if (content.charCodeAt(0) === 65279) {
    content = content.slice(1);
  }
  return content;
};
const inherits = (constructor, superConstructor, props, descriptors2) => {
  constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
  constructor.prototype.constructor = constructor;
  Object.defineProperty(constructor, "super", {
    value: superConstructor.prototype
  });
  props && Object.assign(constructor.prototype, props);
};
const toFlatObject = (sourceObj, destObj, filter2, propFilter) => {
  let props;
  let i;
  let prop;
  const merged = {};
  destObj = destObj || {};
  if (sourceObj == null)
    return destObj;
  do {
    props = Object.getOwnPropertyNames(sourceObj);
    i = props.length;
    while (i-- > 0) {
      prop = props[i];
      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
        destObj[prop] = sourceObj[prop];
        merged[prop] = true;
      }
    }
    sourceObj = filter2 !== false && getPrototypeOf(sourceObj);
  } while (sourceObj && (!filter2 || filter2(sourceObj, destObj)) && sourceObj !== Object.prototype);
  return destObj;
};
const endsWith = (str, searchString, position) => {
  str = String(str);
  if (position === void 0 || position > str.length) {
    position = str.length;
  }
  position -= searchString.length;
  const lastIndex = str.indexOf(searchString, position);
  return lastIndex !== -1 && lastIndex === position;
};
const toArray = (thing) => {
  if (!thing)
    return null;
  if (isArray(thing))
    return thing;
  let i = thing.length;
  if (!isNumber(i))
    return null;
  const arr = new Array(i);
  while (i-- > 0) {
    arr[i] = thing[i];
  }
  return arr;
};
const isTypedArray = ((TypedArray) => {
  return (thing) => {
    return TypedArray && thing instanceof TypedArray;
  };
})(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
const forEachEntry = (obj, fn) => {
  const generator = obj && obj[Symbol.iterator];
  const iterator = generator.call(obj);
  let result;
  while ((result = iterator.next()) && !result.done) {
    const pair = result.value;
    fn.call(obj, pair[0], pair[1]);
  }
};
const matchAll = (regExp, str) => {
  let matches;
  const arr = [];
  while ((matches = regExp.exec(str)) !== null) {
    arr.push(matches);
  }
  return arr;
};
const isHTMLForm = kindOfTest("HTMLFormElement");
const toCamelCase = (str) => {
  return str.toLowerCase().replace(
    /[-_\s]([a-z\d])(\w*)/g,
    function replacer(m, p1, p2) {
      return p1.toUpperCase() + p2;
    }
  );
};
const hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
const isRegExp = kindOfTest("RegExp");
const reduceDescriptors = (obj, reducer) => {
  const descriptors2 = Object.getOwnPropertyDescriptors(obj);
  const reducedDescriptors = {};
  forEach(descriptors2, (descriptor, name) => {
    let ret;
    if ((ret = reducer(descriptor, name, obj)) !== false) {
      reducedDescriptors[name] = ret || descriptor;
    }
  });
  Object.defineProperties(obj, reducedDescriptors);
};
const freezeMethods = (obj) => {
  reduceDescriptors(obj, (descriptor, name) => {
    if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name) !== -1) {
      return false;
    }
    const value = obj[name];
    if (!isFunction(value))
      return;
    descriptor.enumerable = false;
    if ("writable" in descriptor) {
      descriptor.writable = false;
      return;
    }
    if (!descriptor.set) {
      descriptor.set = () => {
        throw Error("Can not rewrite read-only method '" + name + "'");
      };
    }
  });
};
const toObjectSet = (arrayOrString, delimiter) => {
  const obj = {};
  const define = (arr) => {
    arr.forEach((value) => {
      obj[value] = true;
    });
  };
  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));
  return obj;
};
const noop = () => {
};
const toFiniteNumber = (value, defaultValue) => {
  return value != null && Number.isFinite(value = +value) ? value : defaultValue;
};
function isSpecCompliantForm(thing) {
  return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === "FormData" && thing[Symbol.iterator]);
}
const toJSONObject = (obj) => {
  const stack = new Array(10);
  const visit = (source, i) => {
    if (isObject(source)) {
      if (stack.indexOf(source) >= 0) {
        return;
      }
      if (!("toJSON" in source)) {
        stack[i] = source;
        const target = isArray(source) ? [] : {};
        forEach(source, (value, key) => {
          const reducedValue = visit(value, i + 1);
          !isUndefined(reducedValue) && (target[key] = reducedValue);
        });
        stack[i] = void 0;
        return target;
      }
    }
    return source;
  };
  return visit(obj, 0);
};
const isAsyncFn = kindOfTest("AsyncFunction");
const isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
const _setImmediate = ((setImmediateSupported, postMessageSupported) => {
  if (setImmediateSupported) {
    return setImmediate;
  }
  return postMessageSupported ? ((token, callbacks) => {
    _global.addEventListener("message", ({ source, data }) => {
      if (source === _global && data === token) {
        callbacks.length && callbacks.shift()();
      }
    }, false);
    return (cb) => {
      callbacks.push(cb);
      _global.postMessage(token, "*");
    };
  })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);
})(
  typeof setImmediate === "function",
  isFunction(_global.postMessage)
);
const asap = typeof queueMicrotask !== "undefined" ? queueMicrotask.bind(_global) : typeof process !== "undefined" && process.nextTick || _setImmediate;
const utils$1 = {
  isArray,
  isArrayBuffer,
  isBuffer: isBuffer$1,
  isFormData,
  isArrayBufferView,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isPlainObject,
  isReadableStream,
  isRequest,
  isResponse,
  isHeaders,
  isUndefined,
  isDate,
  isFile,
  isBlob,
  isRegExp,
  isFunction,
  isStream,
  isURLSearchParams,
  isTypedArray,
  isFileList,
  forEach,
  merge,
  extend,
  trim,
  stripBOM,
  inherits,
  toFlatObject,
  kindOf,
  kindOfTest,
  endsWith,
  toArray,
  forEachEntry,
  matchAll,
  isHTMLForm,
  hasOwnProperty,
  hasOwnProp: hasOwnProperty,
  // an alias to avoid ESLint no-prototype-builtins detection
  reduceDescriptors,
  freezeMethods,
  toObjectSet,
  toCamelCase,
  noop,
  toFiniteNumber,
  findKey,
  global: _global,
  isContextDefined,
  isSpecCompliantForm,
  toJSONObject,
  isAsyncFn,
  isThenable,
  setImmediate: _setImmediate,
  asap
};
function AxiosError(message, code, config, request, response) {
  Error.call(this);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(this, this.constructor);
  } else {
    this.stack = new Error().stack;
  }
  this.message = message;
  this.name = "AxiosError";
  code && (this.code = code);
  config && (this.config = config);
  request && (this.request = request);
  if (response) {
    this.response = response;
    this.status = response.status ? response.status : null;
  }
}
utils$1.inherits(AxiosError, Error, {
  toJSON: function toJSON() {
    return {
      // Standard
      message: this.message,
      name: this.name,
      // Microsoft
      description: this.description,
      number: this.number,
      // Mozilla
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      // Axios
      config: utils$1.toJSONObject(this.config),
      code: this.code,
      status: this.status
    };
  }
});
const prototype$1 = AxiosError.prototype;
const descriptors = {};
[
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION",
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
  // eslint-disable-next-line func-names
].forEach((code) => {
  descriptors[code] = { value: code };
});
Object.defineProperties(AxiosError, descriptors);
Object.defineProperty(prototype$1, "isAxiosError", { value: true });
AxiosError.from = (error, code, config, request, response, customProps) => {
  const axiosError = Object.create(prototype$1);
  utils$1.toFlatObject(error, axiosError, function filter2(obj) {
    return obj !== Error.prototype;
  }, (prop) => {
    return prop !== "isAxiosError";
  });
  AxiosError.call(axiosError, error.message, code, config, request, response);
  axiosError.cause = error;
  axiosError.name = error.name;
  customProps && Object.assign(axiosError, customProps);
  return axiosError;
};
const httpAdapter = null;
function isVisitable(thing) {
  return utils$1.isPlainObject(thing) || utils$1.isArray(thing);
}
function removeBrackets(key) {
  return utils$1.endsWith(key, "[]") ? key.slice(0, -2) : key;
}
function renderKey(path, key, dots) {
  if (!path)
    return key;
  return path.concat(key).map(function each(token, i) {
    token = removeBrackets(token);
    return !dots && i ? "[" + token + "]" : token;
  }).join(dots ? "." : "");
}
function isFlatArray(arr) {
  return utils$1.isArray(arr) && !arr.some(isVisitable);
}
const predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {
  return /^is[A-Z]/.test(prop);
});
function toFormData(obj, formData, options) {
  if (!utils$1.isObject(obj)) {
    throw new TypeError("target must be an object");
  }
  formData = formData || new FormData();
  options = utils$1.toFlatObject(options, {
    metaTokens: true,
    dots: false,
    indexes: false
  }, false, function defined(option, source) {
    return !utils$1.isUndefined(source[option]);
  });
  const metaTokens = options.metaTokens;
  const visitor = options.visitor || defaultVisitor;
  const dots = options.dots;
  const indexes = options.indexes;
  const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
  const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);
  if (!utils$1.isFunction(visitor)) {
    throw new TypeError("visitor must be a function");
  }
  function convertValue(value) {
    if (value === null)
      return "";
    if (utils$1.isDate(value)) {
      return value.toISOString();
    }
    if (!useBlob && utils$1.isBlob(value)) {
      throw new AxiosError("Blob is not supported. Use a Buffer instead.");
    }
    if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {
      return useBlob && typeof Blob === "function" ? new Blob([value]) : Buffer.from(value);
    }
    return value;
  }
  function defaultVisitor(value, key, path) {
    let arr = value;
    if (value && !path && typeof value === "object") {
      if (utils$1.endsWith(key, "{}")) {
        key = metaTokens ? key : key.slice(0, -2);
        value = JSON.stringify(value);
      } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, "[]")) && (arr = utils$1.toArray(value))) {
        key = removeBrackets(key);
        arr.forEach(function each(el, index) {
          !(utils$1.isUndefined(el) || el === null) && formData.append(
            // eslint-disable-next-line no-nested-ternary
            indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + "[]",
            convertValue(el)
          );
        });
        return false;
      }
    }
    if (isVisitable(value)) {
      return true;
    }
    formData.append(renderKey(path, key, dots), convertValue(value));
    return false;
  }
  const stack = [];
  const exposedHelpers = Object.assign(predicates, {
    defaultVisitor,
    convertValue,
    isVisitable
  });
  function build(value, path) {
    if (utils$1.isUndefined(value))
      return;
    if (stack.indexOf(value) !== -1) {
      throw Error("Circular reference detected in " + path.join("."));
    }
    stack.push(value);
    utils$1.forEach(value, function each(el, key) {
      const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(
        formData,
        el,
        utils$1.isString(key) ? key.trim() : key,
        path,
        exposedHelpers
      );
      if (result === true) {
        build(el, path ? path.concat(key) : [key]);
      }
    });
    stack.pop();
  }
  if (!utils$1.isObject(obj)) {
    throw new TypeError("data must be an object");
  }
  build(obj);
  return formData;
}
function encode$1(str) {
  const charMap = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\0"
  };
  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
    return charMap[match];
  });
}
function AxiosURLSearchParams(params, options) {
  this._pairs = [];
  params && toFormData(params, this, options);
}
const prototype = AxiosURLSearchParams.prototype;
prototype.append = function append(name, value) {
  this._pairs.push([name, value]);
};
prototype.toString = function toString2(encoder) {
  const _encode = encoder ? function(value) {
    return encoder.call(this, value, encode$1);
  } : encode$1;
  return this._pairs.map(function each(pair) {
    return _encode(pair[0]) + "=" + _encode(pair[1]);
  }, "").join("&");
};
function encode(val) {
  return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function buildURL(url, params, options) {
  if (!params) {
    return url;
  }
  const _encode = options && options.encode || encode;
  if (utils$1.isFunction(options)) {
    options = {
      serialize: options
    };
  }
  const serializeFn = options && options.serialize;
  let serializedParams;
  if (serializeFn) {
    serializedParams = serializeFn(params, options);
  } else {
    serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);
  }
  if (serializedParams) {
    const hashmarkIndex = url.indexOf("#");
    if (hashmarkIndex !== -1) {
      url = url.slice(0, hashmarkIndex);
    }
    url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
  }
  return url;
}
class InterceptorManager {
  constructor() {
    this.handlers = [];
  }
  /**
   * Add a new interceptor to the stack
   *
   * @param {Function} fulfilled The function to handle `then` for a `Promise`
   * @param {Function} rejected The function to handle `reject` for a `Promise`
   *
   * @return {Number} An ID used to remove interceptor later
   */
  use(fulfilled, rejected, options) {
    this.handlers.push({
      fulfilled,
      rejected,
      synchronous: options ? options.synchronous : false,
      runWhen: options ? options.runWhen : null
    });
    return this.handlers.length - 1;
  }
  /**
   * Remove an interceptor from the stack
   *
   * @param {Number} id The ID that was returned by `use`
   *
   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
   */
  eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  }
  /**
   * Clear all interceptors from the stack
   *
   * @returns {void}
   */
  clear() {
    if (this.handlers) {
      this.handlers = [];
    }
  }
  /**
   * Iterate over all the registered interceptors
   *
   * This method is particularly useful for skipping over any
   * interceptors that may have become `null` calling `eject`.
   *
   * @param {Function} fn The function to call for each interceptor
   *
   * @returns {void}
   */
  forEach(fn) {
    utils$1.forEach(this.handlers, function forEachHandler(h) {
      if (h !== null) {
        fn(h);
      }
    });
  }
}
const InterceptorManager$1 = InterceptorManager;
const transitionalDefaults = {
  silentJSONParsing: true,
  forcedJSONParsing: true,
  clarifyTimeoutError: false
};
const URLSearchParams$1 = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams;
const FormData$1 = typeof FormData !== "undefined" ? FormData : null;
const Blob$1 = typeof Blob !== "undefined" ? Blob : null;
const platform$1 = {
  isBrowser: true,
  classes: {
    URLSearchParams: URLSearchParams$1,
    FormData: FormData$1,
    Blob: Blob$1
  },
  protocols: ["http", "https", "file", "blob", "url", "data"]
};
const hasBrowserEnv = typeof window !== "undefined" && typeof document !== "undefined";
const _navigator = typeof navigator === "object" && navigator || void 0;
const hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || ["ReactNative", "NativeScript", "NS"].indexOf(_navigator.product) < 0);
const hasStandardBrowserWebWorkerEnv = (() => {
  return typeof WorkerGlobalScope !== "undefined" && // eslint-disable-next-line no-undef
  self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
})();
const origin = hasBrowserEnv && window.location.href || "http://localhost";
const utils = /* @__PURE__ */ Object.freeze({
  __proto__: null,
  hasBrowserEnv,
  hasStandardBrowserEnv,
  hasStandardBrowserWebWorkerEnv,
  navigator: _navigator,
  origin
});
const platform = {
  ...utils,
  ...platform$1
};
function toURLEncodedForm(data, options) {
  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({
    visitor: function(value, key, path, helpers) {
      if (platform.isNode && utils$1.isBuffer(value)) {
        this.append(key, value.toString("base64"));
        return false;
      }
      return helpers.defaultVisitor.apply(this, arguments);
    }
  }, options));
}
function parsePropPath(name) {
  return utils$1.matchAll(/\w+|\[(\w*)]/g, name).map((match) => {
    return match[0] === "[]" ? "" : match[1] || match[0];
  });
}
function arrayToObject(arr) {
  const obj = {};
  const keys = Object.keys(arr);
  let i;
  const len = keys.length;
  let key;
  for (i = 0; i < len; i++) {
    key = keys[i];
    obj[key] = arr[key];
  }
  return obj;
}
function formDataToJSON(formData) {
  function buildPath(path, value, target, index) {
    let name = path[index++];
    if (name === "__proto__")
      return true;
    const isNumericKey = Number.isFinite(+name);
    const isLast = index >= path.length;
    name = !name && utils$1.isArray(target) ? target.length : name;
    if (isLast) {
      if (utils$1.hasOwnProp(target, name)) {
        target[name] = [target[name], value];
      } else {
        target[name] = value;
      }
      return !isNumericKey;
    }
    if (!target[name] || !utils$1.isObject(target[name])) {
      target[name] = [];
    }
    const result = buildPath(path, value, target[name], index);
    if (result && utils$1.isArray(target[name])) {
      target[name] = arrayToObject(target[name]);
    }
    return !isNumericKey;
  }
  if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {
    const obj = {};
    utils$1.forEachEntry(formData, (name, value) => {
      buildPath(parsePropPath(name), value, obj, 0);
    });
    return obj;
  }
  return null;
}
function stringifySafely(rawValue, parser, encoder) {
  if (utils$1.isString(rawValue)) {
    try {
      (parser || JSON.parse)(rawValue);
      return utils$1.trim(rawValue);
    } catch (e) {
      if (e.name !== "SyntaxError") {
        throw e;
      }
    }
  }
  return (encoder || JSON.stringify)(rawValue);
}
const defaults = {
  transitional: transitionalDefaults,
  adapter: ["xhr", "http", "fetch"],
  transformRequest: [function transformRequest(data, headers) {
    const contentType = headers.getContentType() || "";
    const hasJSONContentType = contentType.indexOf("application/json") > -1;
    const isObjectPayload = utils$1.isObject(data);
    if (isObjectPayload && utils$1.isHTMLForm(data)) {
      data = new FormData(data);
    }
    const isFormData2 = utils$1.isFormData(data);
    if (isFormData2) {
      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;
    }
    if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {
      return data;
    }
    if (utils$1.isArrayBufferView(data)) {
      return data.buffer;
    }
    if (utils$1.isURLSearchParams(data)) {
      headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
      return data.toString();
    }
    let isFileList2;
    if (isObjectPayload) {
      if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
        return toURLEncodedForm(data, this.formSerializer).toString();
      }
      if ((isFileList2 = utils$1.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
        const _FormData = this.env && this.env.FormData;
        return toFormData(
          isFileList2 ? { "files[]": data } : data,
          _FormData && new _FormData(),
          this.formSerializer
        );
      }
    }
    if (isObjectPayload || hasJSONContentType) {
      headers.setContentType("application/json", false);
      return stringifySafely(data);
    }
    return data;
  }],
  transformResponse: [function transformResponse(data) {
    const transitional2 = this.transitional || defaults.transitional;
    const forcedJSONParsing = transitional2 && transitional2.forcedJSONParsing;
    const JSONRequested = this.responseType === "json";
    if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {
      return data;
    }
    if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
      const silentJSONParsing = transitional2 && transitional2.silentJSONParsing;
      const strictJSONParsing = !silentJSONParsing && JSONRequested;
      try {
        return JSON.parse(data);
      } catch (e) {
        if (strictJSONParsing) {
          if (e.name === "SyntaxError") {
            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);
          }
          throw e;
        }
      }
    }
    return data;
  }],
  /**
   * A timeout in milliseconds to abort a request. If set to 0 (default) a
   * timeout is not created.
   */
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: platform.classes.FormData,
    Blob: platform.classes.Blob
  },
  validateStatus: function validateStatus(status) {
    return status >= 200 && status < 300;
  },
  headers: {
    common: {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
utils$1.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
  defaults.headers[method] = {};
});
const defaults$1 = defaults;
const ignoreDuplicateOf = utils$1.toObjectSet([
  "age",
  "authorization",
  "content-length",
  "content-type",
  "etag",
  "expires",
  "from",
  "host",
  "if-modified-since",
  "if-unmodified-since",
  "last-modified",
  "location",
  "max-forwards",
  "proxy-authorization",
  "referer",
  "retry-after",
  "user-agent"
]);
const parseHeaders = (rawHeaders) => {
  const parsed = {};
  let key;
  let val;
  let i;
  rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
    i = line.indexOf(":");
    key = line.substring(0, i).trim().toLowerCase();
    val = line.substring(i + 1).trim();
    if (!key || parsed[key] && ignoreDuplicateOf[key]) {
      return;
    }
    if (key === "set-cookie") {
      if (parsed[key]) {
        parsed[key].push(val);
      } else {
        parsed[key] = [val];
      }
    } else {
      parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
    }
  });
  return parsed;
};
const $internals = Symbol("internals");
function normalizeHeader(header) {
  return header && String(header).trim().toLowerCase();
}
function normalizeValue(value) {
  if (value === false || value == null) {
    return value;
  }
  return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);
}
function parseTokens(str) {
  const tokens = /* @__PURE__ */ Object.create(null);
  const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
  let match;
  while (match = tokensRE.exec(str)) {
    tokens[match[1]] = match[2];
  }
  return tokens;
}
const isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
function matchHeaderValue(context, value, header, filter2, isHeaderNameFilter) {
  if (utils$1.isFunction(filter2)) {
    return filter2.call(this, value, header);
  }
  if (isHeaderNameFilter) {
    value = header;
  }
  if (!utils$1.isString(value))
    return;
  if (utils$1.isString(filter2)) {
    return value.indexOf(filter2) !== -1;
  }
  if (utils$1.isRegExp(filter2)) {
    return filter2.test(value);
  }
}
function formatHeader(header) {
  return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w, char, str) => {
    return char.toUpperCase() + str;
  });
}
function buildAccessors(obj, header) {
  const accessorName = utils$1.toCamelCase(" " + header);
  ["get", "set", "has"].forEach((methodName) => {
    Object.defineProperty(obj, methodName + accessorName, {
      value: function(arg1, arg2, arg3) {
        return this[methodName].call(this, header, arg1, arg2, arg3);
      },
      configurable: true
    });
  });
}
class AxiosHeaders {
  constructor(headers) {
    headers && this.set(headers);
  }
  set(header, valueOrRewrite, rewrite) {
    const self2 = this;
    function setHeader(_value, _header, _rewrite) {
      const lHeader = normalizeHeader(_header);
      if (!lHeader) {
        throw new Error("header name must be a non-empty string");
      }
      const key = utils$1.findKey(self2, lHeader);
      if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
        self2[key || _header] = normalizeValue(_value);
      }
    }
    const setHeaders = (headers, _rewrite) => utils$1.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
    if (utils$1.isPlainObject(header) || header instanceof this.constructor) {
      setHeaders(header, valueOrRewrite);
    } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
      setHeaders(parseHeaders(header), valueOrRewrite);
    } else if (utils$1.isHeaders(header)) {
      for (const [key, value] of header.entries()) {
        setHeader(value, key, rewrite);
      }
    } else {
      header != null && setHeader(valueOrRewrite, header, rewrite);
    }
    return this;
  }
  get(header, parser) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils$1.findKey(this, header);
      if (key) {
        const value = this[key];
        if (!parser) {
          return value;
        }
        if (parser === true) {
          return parseTokens(value);
        }
        if (utils$1.isFunction(parser)) {
          return parser.call(this, value, key);
        }
        if (utils$1.isRegExp(parser)) {
          return parser.exec(value);
        }
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(header, matcher) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils$1.findKey(this, header);
      return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
    }
    return false;
  }
  delete(header, matcher) {
    const self2 = this;
    let deleted = false;
    function deleteHeader(_header) {
      _header = normalizeHeader(_header);
      if (_header) {
        const key = utils$1.findKey(self2, _header);
        if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
          delete self2[key];
          deleted = true;
        }
      }
    }
    if (utils$1.isArray(header)) {
      header.forEach(deleteHeader);
    } else {
      deleteHeader(header);
    }
    return deleted;
  }
  clear(matcher) {
    const keys = Object.keys(this);
    let i = keys.length;
    let deleted = false;
    while (i--) {
      const key = keys[i];
      if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
        delete this[key];
        deleted = true;
      }
    }
    return deleted;
  }
  normalize(format) {
    const self2 = this;
    const headers = {};
    utils$1.forEach(this, (value, header) => {
      const key = utils$1.findKey(headers, header);
      if (key) {
        self2[key] = normalizeValue(value);
        delete self2[header];
        return;
      }
      const normalized = format ? formatHeader(header) : String(header).trim();
      if (normalized !== header) {
        delete self2[header];
      }
      self2[normalized] = normalizeValue(value);
      headers[normalized] = true;
    });
    return this;
  }
  concat(...targets) {
    return this.constructor.concat(this, ...targets);
  }
  toJSON(asStrings) {
    const obj = /* @__PURE__ */ Object.create(null);
    utils$1.forEach(this, (value, header) => {
      value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(", ") : value);
    });
    return obj;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([header, value]) => header + ": " + value).join("\n");
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(thing) {
    return thing instanceof this ? thing : new this(thing);
  }
  static concat(first, ...targets) {
    const computed2 = new this(first);
    targets.forEach((target) => computed2.set(target));
    return computed2;
  }
  static accessor(header) {
    const internals = this[$internals] = this[$internals] = {
      accessors: {}
    };
    const accessors = internals.accessors;
    const prototype2 = this.prototype;
    function defineAccessor(_header) {
      const lHeader = normalizeHeader(_header);
      if (!accessors[lHeader]) {
        buildAccessors(prototype2, _header);
        accessors[lHeader] = true;
      }
    }
    utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
    return this;
  }
}
AxiosHeaders.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
utils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key) => {
  let mapped = key[0].toUpperCase() + key.slice(1);
  return {
    get: () => value,
    set(headerValue) {
      this[mapped] = headerValue;
    }
  };
});
utils$1.freezeMethods(AxiosHeaders);
const AxiosHeaders$1 = AxiosHeaders;
function transformData(fns, response) {
  const config = this || defaults$1;
  const context = response || config;
  const headers = AxiosHeaders$1.from(context.headers);
  let data = context.data;
  utils$1.forEach(fns, function transform(fn) {
    data = fn.call(config, data, headers.normalize(), response ? response.status : void 0);
  });
  headers.normalize();
  return data;
}
function isCancel(value) {
  return !!(value && value.__CANCEL__);
}
function CanceledError(message, config, request) {
  AxiosError.call(this, message == null ? "canceled" : message, AxiosError.ERR_CANCELED, config, request);
  this.name = "CanceledError";
}
utils$1.inherits(CanceledError, AxiosError, {
  __CANCEL__: true
});
function settle(resolve, reject, response) {
  const validateStatus2 = response.config.validateStatus;
  if (!response.status || !validateStatus2 || validateStatus2(response.status)) {
    resolve(response);
  } else {
    reject(new AxiosError(
      "Request failed with status code " + response.status,
      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
      response.config,
      response.request,
      response
    ));
  }
}
function parseProtocol(url) {
  const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
  return match && match[1] || "";
}
function speedometer(samplesCount, min) {
  samplesCount = samplesCount || 10;
  const bytes = new Array(samplesCount);
  const timestamps = new Array(samplesCount);
  let head = 0;
  let tail = 0;
  let firstSampleTS;
  min = min !== void 0 ? min : 1e3;
  return function push(chunkLength) {
    const now = Date.now();
    const startedAt = timestamps[tail];
    if (!firstSampleTS) {
      firstSampleTS = now;
    }
    bytes[head] = chunkLength;
    timestamps[head] = now;
    let i = tail;
    let bytesCount = 0;
    while (i !== head) {
      bytesCount += bytes[i++];
      i = i % samplesCount;
    }
    head = (head + 1) % samplesCount;
    if (head === tail) {
      tail = (tail + 1) % samplesCount;
    }
    if (now - firstSampleTS < min) {
      return;
    }
    const passed = startedAt && now - startedAt;
    return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
  };
}
function throttle$1(fn, freq) {
  let timestamp = 0;
  let threshold = 1e3 / freq;
  let lastArgs;
  let timer;
  const invoke = (args, now = Date.now()) => {
    timestamp = now;
    lastArgs = null;
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    fn.apply(null, args);
  };
  const throttled = (...args) => {
    const now = Date.now();
    const passed = now - timestamp;
    if (passed >= threshold) {
      invoke(args, now);
    } else {
      lastArgs = args;
      if (!timer) {
        timer = setTimeout(() => {
          timer = null;
          invoke(lastArgs);
        }, threshold - passed);
      }
    }
  };
  const flush = () => lastArgs && invoke(lastArgs);
  return [throttled, flush];
}
const progressEventReducer = (listener, isDownloadStream, freq = 3) => {
  let bytesNotified = 0;
  const _speedometer = speedometer(50, 250);
  return throttle$1((e) => {
    const loaded = e.loaded;
    const total = e.lengthComputable ? e.total : void 0;
    const progressBytes = loaded - bytesNotified;
    const rate = _speedometer(progressBytes);
    const inRange = loaded <= total;
    bytesNotified = loaded;
    const data = {
      loaded,
      total,
      progress: total ? loaded / total : void 0,
      bytes: progressBytes,
      rate: rate ? rate : void 0,
      estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
      event: e,
      lengthComputable: total != null,
      [isDownloadStream ? "download" : "upload"]: true
    };
    listener(data);
  }, freq);
};
const progressEventDecorator = (total, throttled) => {
  const lengthComputable = total != null;
  return [(loaded) => throttled[0]({
    lengthComputable,
    total,
    loaded
  }), throttled[1]];
};
const asyncDecorator = (fn) => (...args) => utils$1.asap(() => fn(...args));
const isURLSameOrigin = platform.hasStandardBrowserEnv ? ((origin2, isMSIE) => (url) => {
  url = new URL(url, platform.origin);
  return origin2.protocol === url.protocol && origin2.host === url.host && (isMSIE || origin2.port === url.port);
})(
  new URL(platform.origin),
  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)
) : () => true;
const cookies = platform.hasStandardBrowserEnv ? (
  // Standard browser envs support document.cookie
  {
    write(name, value, expires, path, domain, secure) {
      const cookie = [name + "=" + encodeURIComponent(value)];
      utils$1.isNumber(expires) && cookie.push("expires=" + new Date(expires).toGMTString());
      utils$1.isString(path) && cookie.push("path=" + path);
      utils$1.isString(domain) && cookie.push("domain=" + domain);
      secure === true && cookie.push("secure");
      document.cookie = cookie.join("; ");
    },
    read(name) {
      const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
      return match ? decodeURIComponent(match[3]) : null;
    },
    remove(name) {
      this.write(name, "", Date.now() - 864e5);
    }
  }
) : (
  // Non-standard browser env (web workers, react-native) lack needed support.
  {
    write() {
    },
    read() {
      return null;
    },
    remove() {
    }
  }
);
function isAbsoluteURL(url) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
}
function combineURLs(baseURL, relativeURL) {
  return relativeURL ? baseURL.replace(/\/?\/$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
}
function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {
  let isRelativeUrl = !isAbsoluteURL(requestedURL);
  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {
    return combineURLs(baseURL, requestedURL);
  }
  return requestedURL;
}
const headersToObject = (thing) => thing instanceof AxiosHeaders$1 ? { ...thing } : thing;
function mergeConfig(config1, config2) {
  config2 = config2 || {};
  const config = {};
  function getMergedValue(target, source, prop, caseless) {
    if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {
      return utils$1.merge.call({ caseless }, target, source);
    } else if (utils$1.isPlainObject(source)) {
      return utils$1.merge({}, source);
    } else if (utils$1.isArray(source)) {
      return source.slice();
    }
    return source;
  }
  function mergeDeepProperties(a, b, prop, caseless) {
    if (!utils$1.isUndefined(b)) {
      return getMergedValue(a, b, prop, caseless);
    } else if (!utils$1.isUndefined(a)) {
      return getMergedValue(void 0, a, prop, caseless);
    }
  }
  function valueFromConfig2(a, b) {
    if (!utils$1.isUndefined(b)) {
      return getMergedValue(void 0, b);
    }
  }
  function defaultToConfig2(a, b) {
    if (!utils$1.isUndefined(b)) {
      return getMergedValue(void 0, b);
    } else if (!utils$1.isUndefined(a)) {
      return getMergedValue(void 0, a);
    }
  }
  function mergeDirectKeys(a, b, prop) {
    if (prop in config2) {
      return getMergedValue(a, b);
    } else if (prop in config1) {
      return getMergedValue(void 0, a);
    }
  }
  const mergeMap = {
    url: valueFromConfig2,
    method: valueFromConfig2,
    data: valueFromConfig2,
    baseURL: defaultToConfig2,
    transformRequest: defaultToConfig2,
    transformResponse: defaultToConfig2,
    paramsSerializer: defaultToConfig2,
    timeout: defaultToConfig2,
    timeoutMessage: defaultToConfig2,
    withCredentials: defaultToConfig2,
    withXSRFToken: defaultToConfig2,
    adapter: defaultToConfig2,
    responseType: defaultToConfig2,
    xsrfCookieName: defaultToConfig2,
    xsrfHeaderName: defaultToConfig2,
    onUploadProgress: defaultToConfig2,
    onDownloadProgress: defaultToConfig2,
    decompress: defaultToConfig2,
    maxContentLength: defaultToConfig2,
    maxBodyLength: defaultToConfig2,
    beforeRedirect: defaultToConfig2,
    transport: defaultToConfig2,
    httpAgent: defaultToConfig2,
    httpsAgent: defaultToConfig2,
    cancelToken: defaultToConfig2,
    socketPath: defaultToConfig2,
    responseEncoding: defaultToConfig2,
    validateStatus: mergeDirectKeys,
    headers: (a, b, prop) => mergeDeepProperties(headersToObject(a), headersToObject(b), prop, true)
  };
  utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
    const merge2 = mergeMap[prop] || mergeDeepProperties;
    const configValue = merge2(config1[prop], config2[prop], prop);
    utils$1.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
  });
  return config;
}
const resolveConfig = (config) => {
  const newConfig = mergeConfig({}, config);
  let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;
  newConfig.headers = headers = AxiosHeaders$1.from(headers);
  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);
  if (auth) {
    headers.set(
      "Authorization",
      "Basic " + btoa((auth.username || "") + ":" + (auth.password ? unescape(encodeURIComponent(auth.password)) : ""))
    );
  }
  let contentType;
  if (utils$1.isFormData(data)) {
    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {
      headers.setContentType(void 0);
    } else if ((contentType = headers.getContentType()) !== false) {
      const [type, ...tokens] = contentType ? contentType.split(";").map((token) => token.trim()).filter(Boolean) : [];
      headers.setContentType([type || "multipart/form-data", ...tokens].join("; "));
    }
  }
  if (platform.hasStandardBrowserEnv) {
    withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));
    if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {
      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);
      if (xsrfValue) {
        headers.set(xsrfHeaderName, xsrfValue);
      }
    }
  }
  return newConfig;
};
const isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
const xhrAdapter = isXHRAdapterSupported && function(config) {
  return new Promise(function dispatchXhrRequest(resolve, reject) {
    const _config = resolveConfig(config);
    let requestData = _config.data;
    const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();
    let { responseType, onUploadProgress, onDownloadProgress } = _config;
    let onCanceled;
    let uploadThrottled, downloadThrottled;
    let flushUpload, flushDownload;
    function done() {
      flushUpload && flushUpload();
      flushDownload && flushDownload();
      _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);
      _config.signal && _config.signal.removeEventListener("abort", onCanceled);
    }
    let request = new XMLHttpRequest();
    request.open(_config.method.toUpperCase(), _config.url, true);
    request.timeout = _config.timeout;
    function onloadend() {
      if (!request) {
        return;
      }
      const responseHeaders = AxiosHeaders$1.from(
        "getAllResponseHeaders" in request && request.getAllResponseHeaders()
      );
      const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
      const response = {
        data: responseData,
        status: request.status,
        statusText: request.statusText,
        headers: responseHeaders,
        config,
        request
      };
      settle(function _resolve(value) {
        resolve(value);
        done();
      }, function _reject(err) {
        reject(err);
        done();
      }, response);
      request = null;
    }
    if ("onloadend" in request) {
      request.onloadend = onloadend;
    } else {
      request.onreadystatechange = function handleLoad() {
        if (!request || request.readyState !== 4) {
          return;
        }
        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
          return;
        }
        setTimeout(onloadend);
      };
    }
    request.onabort = function handleAbort() {
      if (!request) {
        return;
      }
      reject(new AxiosError("Request aborted", AxiosError.ECONNABORTED, config, request));
      request = null;
    };
    request.onerror = function handleError() {
      reject(new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request));
      request = null;
    };
    request.ontimeout = function handleTimeout() {
      let timeoutErrorMessage = _config.timeout ? "timeout of " + _config.timeout + "ms exceeded" : "timeout exceeded";
      const transitional2 = _config.transitional || transitionalDefaults;
      if (_config.timeoutErrorMessage) {
        timeoutErrorMessage = _config.timeoutErrorMessage;
      }
      reject(new AxiosError(
        timeoutErrorMessage,
        transitional2.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,
        config,
        request
      ));
      request = null;
    };
    requestData === void 0 && requestHeaders.setContentType(null);
    if ("setRequestHeader" in request) {
      utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
        request.setRequestHeader(key, val);
      });
    }
    if (!utils$1.isUndefined(_config.withCredentials)) {
      request.withCredentials = !!_config.withCredentials;
    }
    if (responseType && responseType !== "json") {
      request.responseType = _config.responseType;
    }
    if (onDownloadProgress) {
      [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);
      request.addEventListener("progress", downloadThrottled);
    }
    if (onUploadProgress && request.upload) {
      [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);
      request.upload.addEventListener("progress", uploadThrottled);
      request.upload.addEventListener("loadend", flushUpload);
    }
    if (_config.cancelToken || _config.signal) {
      onCanceled = (cancel) => {
        if (!request) {
          return;
        }
        reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);
        request.abort();
        request = null;
      };
      _config.cancelToken && _config.cancelToken.subscribe(onCanceled);
      if (_config.signal) {
        _config.signal.aborted ? onCanceled() : _config.signal.addEventListener("abort", onCanceled);
      }
    }
    const protocol = parseProtocol(_config.url);
    if (protocol && platform.protocols.indexOf(protocol) === -1) {
      reject(new AxiosError("Unsupported protocol " + protocol + ":", AxiosError.ERR_BAD_REQUEST, config));
      return;
    }
    request.send(requestData || null);
  });
};
const composeSignals = (signals, timeout) => {
  const { length } = signals = signals ? signals.filter(Boolean) : [];
  if (timeout || length) {
    let controller = new AbortController();
    let aborted;
    const onabort = function(reason) {
      if (!aborted) {
        aborted = true;
        unsubscribe();
        const err = reason instanceof Error ? reason : this.reason;
        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));
      }
    };
    let timer = timeout && setTimeout(() => {
      timer = null;
      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));
    }, timeout);
    const unsubscribe = () => {
      if (signals) {
        timer && clearTimeout(timer);
        timer = null;
        signals.forEach((signal2) => {
          signal2.unsubscribe ? signal2.unsubscribe(onabort) : signal2.removeEventListener("abort", onabort);
        });
        signals = null;
      }
    };
    signals.forEach((signal2) => signal2.addEventListener("abort", onabort));
    const { signal } = controller;
    signal.unsubscribe = () => utils$1.asap(unsubscribe);
    return signal;
  }
};
const composeSignals$1 = composeSignals;
const streamChunk = function* (chunk, chunkSize) {
  let len = chunk.byteLength;
  if (!chunkSize || len < chunkSize) {
    yield chunk;
    return;
  }
  let pos = 0;
  let end;
  while (pos < len) {
    end = pos + chunkSize;
    yield chunk.slice(pos, end);
    pos = end;
  }
};
const readBytes = async function* (iterable, chunkSize) {
  for await (const chunk of readStream(iterable)) {
    yield* streamChunk(chunk, chunkSize);
  }
};
const readStream = async function* (stream) {
  if (stream[Symbol.asyncIterator]) {
    yield* stream;
    return;
  }
  const reader = stream.getReader();
  try {
    for (; ; ) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }
      yield value;
    }
  } finally {
    await reader.cancel();
  }
};
const trackStream = (stream, chunkSize, onProgress, onFinish) => {
  const iterator = readBytes(stream, chunkSize);
  let bytes = 0;
  let done;
  let _onFinish = (e) => {
    if (!done) {
      done = true;
      onFinish && onFinish(e);
    }
  };
  return new ReadableStream({
    async pull(controller) {
      try {
        const { done: done2, value } = await iterator.next();
        if (done2) {
          _onFinish();
          controller.close();
          return;
        }
        let len = value.byteLength;
        if (onProgress) {
          let loadedBytes = bytes += len;
          onProgress(loadedBytes);
        }
        controller.enqueue(new Uint8Array(value));
      } catch (err) {
        _onFinish(err);
        throw err;
      }
    },
    cancel(reason) {
      _onFinish(reason);
      return iterator.return();
    }
  }, {
    highWaterMark: 2
  });
};
const isFetchSupported = typeof fetch === "function" && typeof Request === "function" && typeof Response === "function";
const isReadableStreamSupported = isFetchSupported && typeof ReadableStream === "function";
const encodeText = isFetchSupported && (typeof TextEncoder === "function" ? ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) : async (str) => new Uint8Array(await new Response(str).arrayBuffer()));
const test = (fn, ...args) => {
  try {
    return !!fn(...args);
  } catch (e) {
    return false;
  }
};
const supportsRequestStream = isReadableStreamSupported && test(() => {
  let duplexAccessed = false;
  const hasContentType = new Request(platform.origin, {
    body: new ReadableStream(),
    method: "POST",
    get duplex() {
      duplexAccessed = true;
      return "half";
    }
  }).headers.has("Content-Type");
  return duplexAccessed && !hasContentType;
});
const DEFAULT_CHUNK_SIZE = 64 * 1024;
const supportsResponseStream = isReadableStreamSupported && test(() => utils$1.isReadableStream(new Response("").body));
const resolvers = {
  stream: supportsResponseStream && ((res) => res.body)
};
isFetchSupported && ((res) => {
  ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((type) => {
    !resolvers[type] && (resolvers[type] = utils$1.isFunction(res[type]) ? (res2) => res2[type]() : (_, config) => {
      throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);
    });
  });
})(new Response());
const getBodyLength = async (body) => {
  if (body == null) {
    return 0;
  }
  if (utils$1.isBlob(body)) {
    return body.size;
  }
  if (utils$1.isSpecCompliantForm(body)) {
    const _request = new Request(platform.origin, {
      method: "POST",
      body
    });
    return (await _request.arrayBuffer()).byteLength;
  }
  if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {
    return body.byteLength;
  }
  if (utils$1.isURLSearchParams(body)) {
    body = body + "";
  }
  if (utils$1.isString(body)) {
    return (await encodeText(body)).byteLength;
  }
};
const resolveBodyLength = async (headers, body) => {
  const length = utils$1.toFiniteNumber(headers.getContentLength());
  return length == null ? getBodyLength(body) : length;
};
const fetchAdapter = isFetchSupported && (async (config) => {
  let {
    url,
    method,
    data,
    signal,
    cancelToken,
    timeout,
    onDownloadProgress,
    onUploadProgress,
    responseType,
    headers,
    withCredentials = "same-origin",
    fetchOptions
  } = resolveConfig(config);
  responseType = responseType ? (responseType + "").toLowerCase() : "text";
  let composedSignal = composeSignals$1([signal, cancelToken && cancelToken.toAbortSignal()], timeout);
  let request;
  const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {
    composedSignal.unsubscribe();
  });
  let requestContentLength;
  try {
    if (onUploadProgress && supportsRequestStream && method !== "get" && method !== "head" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {
      let _request = new Request(url, {
        method: "POST",
        body: data,
        duplex: "half"
      });
      let contentTypeHeader;
      if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get("content-type"))) {
        headers.setContentType(contentTypeHeader);
      }
      if (_request.body) {
        const [onProgress, flush] = progressEventDecorator(
          requestContentLength,
          progressEventReducer(asyncDecorator(onUploadProgress))
        );
        data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);
      }
    }
    if (!utils$1.isString(withCredentials)) {
      withCredentials = withCredentials ? "include" : "omit";
    }
    const isCredentialsSupported = "credentials" in Request.prototype;
    request = new Request(url, {
      ...fetchOptions,
      signal: composedSignal,
      method: method.toUpperCase(),
      headers: headers.normalize().toJSON(),
      body: data,
      duplex: "half",
      credentials: isCredentialsSupported ? withCredentials : void 0
    });
    let response = await fetch(request);
    const isStreamResponse = supportsResponseStream && (responseType === "stream" || responseType === "response");
    if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {
      const options = {};
      ["status", "statusText", "headers"].forEach((prop) => {
        options[prop] = response[prop];
      });
      const responseContentLength = utils$1.toFiniteNumber(response.headers.get("content-length"));
      const [onProgress, flush] = onDownloadProgress && progressEventDecorator(
        responseContentLength,
        progressEventReducer(asyncDecorator(onDownloadProgress), true)
      ) || [];
      response = new Response(
        trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {
          flush && flush();
          unsubscribe && unsubscribe();
        }),
        options
      );
    }
    responseType = responseType || "text";
    let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || "text"](response, config);
    !isStreamResponse && unsubscribe && unsubscribe();
    return await new Promise((resolve, reject) => {
      settle(resolve, reject, {
        data: responseData,
        headers: AxiosHeaders$1.from(response.headers),
        status: response.status,
        statusText: response.statusText,
        config,
        request
      });
    });
  } catch (err) {
    unsubscribe && unsubscribe();
    if (err && err.name === "TypeError" && /fetch/i.test(err.message)) {
      throw Object.assign(
        new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request),
        {
          cause: err.cause || err
        }
      );
    }
    throw AxiosError.from(err, err && err.code, config, request);
  }
});
const knownAdapters = {
  http: httpAdapter,
  xhr: xhrAdapter,
  fetch: fetchAdapter
};
utils$1.forEach(knownAdapters, (fn, value) => {
  if (fn) {
    try {
      Object.defineProperty(fn, "name", { value });
    } catch (e) {
    }
    Object.defineProperty(fn, "adapterName", { value });
  }
});
const renderReason = (reason) => `- ${reason}`;
const isResolvedHandle = (adapter) => utils$1.isFunction(adapter) || adapter === null || adapter === false;
const adapters = {
  getAdapter: (adapters2) => {
    adapters2 = utils$1.isArray(adapters2) ? adapters2 : [adapters2];
    const { length } = adapters2;
    let nameOrAdapter;
    let adapter;
    const rejectedReasons = {};
    for (let i = 0; i < length; i++) {
      nameOrAdapter = adapters2[i];
      let id;
      adapter = nameOrAdapter;
      if (!isResolvedHandle(nameOrAdapter)) {
        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
        if (adapter === void 0) {
          throw new AxiosError(`Unknown adapter '${id}'`);
        }
      }
      if (adapter) {
        break;
      }
      rejectedReasons[id || "#" + i] = adapter;
    }
    if (!adapter) {
      const reasons = Object.entries(rejectedReasons).map(
        ([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build")
      );
      let s = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
      throw new AxiosError(
        `There is no suitable adapter to dispatch the request ` + s,
        "ERR_NOT_SUPPORT"
      );
    }
    return adapter;
  },
  adapters: knownAdapters
};
function throwIfCancellationRequested(config) {
  if (config.cancelToken) {
    config.cancelToken.throwIfRequested();
  }
  if (config.signal && config.signal.aborted) {
    throw new CanceledError(null, config);
  }
}
function dispatchRequest(config) {
  throwIfCancellationRequested(config);
  config.headers = AxiosHeaders$1.from(config.headers);
  config.data = transformData.call(
    config,
    config.transformRequest
  );
  if (["post", "put", "patch"].indexOf(config.method) !== -1) {
    config.headers.setContentType("application/x-www-form-urlencoded", false);
  }
  const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);
  return adapter(config).then(function onAdapterResolution(response) {
    throwIfCancellationRequested(config);
    response.data = transformData.call(
      config,
      config.transformResponse,
      response
    );
    response.headers = AxiosHeaders$1.from(response.headers);
    return response;
  }, function onAdapterRejection(reason) {
    if (!isCancel(reason)) {
      throwIfCancellationRequested(config);
      if (reason && reason.response) {
        reason.response.data = transformData.call(
          config,
          config.transformResponse,
          reason.response
        );
        reason.response.headers = AxiosHeaders$1.from(reason.response.headers);
      }
    }
    return Promise.reject(reason);
  });
}
const VERSION = "1.8.4";
const validators$1 = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach((type, i) => {
  validators$1[type] = function validator2(thing) {
    return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
  };
});
const deprecatedWarnings = {};
validators$1.transitional = function transitional(validator2, version, message) {
  function formatMessage(opt, desc) {
    return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
  }
  return (value, opt, opts) => {
    if (validator2 === false) {
      throw new AxiosError(
        formatMessage(opt, " has been removed" + (version ? " in " + version : "")),
        AxiosError.ERR_DEPRECATED
      );
    }
    if (version && !deprecatedWarnings[opt]) {
      deprecatedWarnings[opt] = true;
      console.warn(
        formatMessage(
          opt,
          " has been deprecated since v" + version + " and will be removed in the near future"
        )
      );
    }
    return validator2 ? validator2(value, opt, opts) : true;
  };
};
validators$1.spelling = function spelling(correctSpelling) {
  return (value, opt) => {
    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);
    return true;
  };
};
function assertOptions(options, schema, allowUnknown) {
  if (typeof options !== "object") {
    throw new AxiosError("options must be an object", AxiosError.ERR_BAD_OPTION_VALUE);
  }
  const keys = Object.keys(options);
  let i = keys.length;
  while (i-- > 0) {
    const opt = keys[i];
    const validator2 = schema[opt];
    if (validator2) {
      const value = options[opt];
      const result = value === void 0 || validator2(value, opt, options);
      if (result !== true) {
        throw new AxiosError("option " + opt + " must be " + result, AxiosError.ERR_BAD_OPTION_VALUE);
      }
      continue;
    }
    if (allowUnknown !== true) {
      throw new AxiosError("Unknown option " + opt, AxiosError.ERR_BAD_OPTION);
    }
  }
}
const validator = {
  assertOptions,
  validators: validators$1
};
const validators = validator.validators;
class Axios {
  constructor(instanceConfig) {
    this.defaults = instanceConfig;
    this.interceptors = {
      request: new InterceptorManager$1(),
      response: new InterceptorManager$1()
    };
  }
  /**
   * Dispatch a request
   *
   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
   * @param {?Object} config
   *
   * @returns {Promise} The Promise to be fulfilled
   */
  async request(configOrUrl, config) {
    try {
      return await this._request(configOrUrl, config);
    } catch (err) {
      if (err instanceof Error) {
        let dummy = {};
        Error.captureStackTrace ? Error.captureStackTrace(dummy) : dummy = new Error();
        const stack = dummy.stack ? dummy.stack.replace(/^.+\n/, "") : "";
        try {
          if (!err.stack) {
            err.stack = stack;
          } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\n.+\n/, ""))) {
            err.stack += "\n" + stack;
          }
        } catch (e) {
        }
      }
      throw err;
    }
  }
  _request(configOrUrl, config) {
    if (typeof configOrUrl === "string") {
      config = config || {};
      config.url = configOrUrl;
    } else {
      config = configOrUrl || {};
    }
    config = mergeConfig(this.defaults, config);
    const { transitional: transitional2, paramsSerializer, headers } = config;
    if (transitional2 !== void 0) {
      validator.assertOptions(transitional2, {
        silentJSONParsing: validators.transitional(validators.boolean),
        forcedJSONParsing: validators.transitional(validators.boolean),
        clarifyTimeoutError: validators.transitional(validators.boolean)
      }, false);
    }
    if (paramsSerializer != null) {
      if (utils$1.isFunction(paramsSerializer)) {
        config.paramsSerializer = {
          serialize: paramsSerializer
        };
      } else {
        validator.assertOptions(paramsSerializer, {
          encode: validators.function,
          serialize: validators.function
        }, true);
      }
    }
    if (config.allowAbsoluteUrls !== void 0)
      ;
    else if (this.defaults.allowAbsoluteUrls !== void 0) {
      config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;
    } else {
      config.allowAbsoluteUrls = true;
    }
    validator.assertOptions(config, {
      baseUrl: validators.spelling("baseURL"),
      withXsrfToken: validators.spelling("withXSRFToken")
    }, true);
    config.method = (config.method || this.defaults.method || "get").toLowerCase();
    let contextHeaders = headers && utils$1.merge(
      headers.common,
      headers[config.method]
    );
    headers && utils$1.forEach(
      ["delete", "get", "head", "post", "put", "patch", "common"],
      (method) => {
        delete headers[method];
      }
    );
    config.headers = AxiosHeaders$1.concat(contextHeaders, headers);
    const requestInterceptorChain = [];
    let synchronousRequestInterceptors = true;
    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
      if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
        return;
      }
      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
    });
    const responseInterceptorChain = [];
    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
    });
    let promise;
    let i = 0;
    let len;
    if (!synchronousRequestInterceptors) {
      const chain = [dispatchRequest.bind(this), void 0];
      chain.unshift.apply(chain, requestInterceptorChain);
      chain.push.apply(chain, responseInterceptorChain);
      len = chain.length;
      promise = Promise.resolve(config);
      while (i < len) {
        promise = promise.then(chain[i++], chain[i++]);
      }
      return promise;
    }
    len = requestInterceptorChain.length;
    let newConfig = config;
    i = 0;
    while (i < len) {
      const onFulfilled = requestInterceptorChain[i++];
      const onRejected = requestInterceptorChain[i++];
      try {
        newConfig = onFulfilled(newConfig);
      } catch (error) {
        onRejected.call(this, error);
        break;
      }
    }
    try {
      promise = dispatchRequest.call(this, newConfig);
    } catch (error) {
      return Promise.reject(error);
    }
    i = 0;
    len = responseInterceptorChain.length;
    while (i < len) {
      promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);
    }
    return promise;
  }
  getUri(config) {
    config = mergeConfig(this.defaults, config);
    const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);
    return buildURL(fullPath, config.params, config.paramsSerializer);
  }
}
utils$1.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
  Axios.prototype[method] = function(url, config) {
    return this.request(mergeConfig(config || {}, {
      method,
      url,
      data: (config || {}).data
    }));
  };
});
utils$1.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
  function generateHTTPMethod(isForm) {
    return function httpMethod(url, data, config) {
      return this.request(mergeConfig(config || {}, {
        method,
        headers: isForm ? {
          "Content-Type": "multipart/form-data"
        } : {},
        url,
        data
      }));
    };
  }
  Axios.prototype[method] = generateHTTPMethod();
  Axios.prototype[method + "Form"] = generateHTTPMethod(true);
});
const Axios$1 = Axios;
class CancelToken {
  constructor(executor) {
    if (typeof executor !== "function") {
      throw new TypeError("executor must be a function.");
    }
    let resolvePromise;
    this.promise = new Promise(function promiseExecutor(resolve) {
      resolvePromise = resolve;
    });
    const token = this;
    this.promise.then((cancel) => {
      if (!token._listeners)
        return;
      let i = token._listeners.length;
      while (i-- > 0) {
        token._listeners[i](cancel);
      }
      token._listeners = null;
    });
    this.promise.then = (onfulfilled) => {
      let _resolve;
      const promise = new Promise((resolve) => {
        token.subscribe(resolve);
        _resolve = resolve;
      }).then(onfulfilled);
      promise.cancel = function reject() {
        token.unsubscribe(_resolve);
      };
      return promise;
    };
    executor(function cancel(message, config, request) {
      if (token.reason) {
        return;
      }
      token.reason = new CanceledError(message, config, request);
      resolvePromise(token.reason);
    });
  }
  /**
   * Throws a `CanceledError` if cancellation has been requested.
   */
  throwIfRequested() {
    if (this.reason) {
      throw this.reason;
    }
  }
  /**
   * Subscribe to the cancel signal
   */
  subscribe(listener) {
    if (this.reason) {
      listener(this.reason);
      return;
    }
    if (this._listeners) {
      this._listeners.push(listener);
    } else {
      this._listeners = [listener];
    }
  }
  /**
   * Unsubscribe from the cancel signal
   */
  unsubscribe(listener) {
    if (!this._listeners) {
      return;
    }
    const index = this._listeners.indexOf(listener);
    if (index !== -1) {
      this._listeners.splice(index, 1);
    }
  }
  toAbortSignal() {
    const controller = new AbortController();
    const abort = (err) => {
      controller.abort(err);
    };
    this.subscribe(abort);
    controller.signal.unsubscribe = () => this.unsubscribe(abort);
    return controller.signal;
  }
  /**
   * Returns an object that contains a new `CancelToken` and a function that, when called,
   * cancels the `CancelToken`.
   */
  static source() {
    let cancel;
    const token = new CancelToken(function executor(c) {
      cancel = c;
    });
    return {
      token,
      cancel
    };
  }
}
const CancelToken$1 = CancelToken;
function spread(callback) {
  return function wrap(arr) {
    return callback.apply(null, arr);
  };
}
function isAxiosError(payload) {
  return utils$1.isObject(payload) && payload.isAxiosError === true;
}
const HttpStatusCode = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(HttpStatusCode).forEach(([key, value]) => {
  HttpStatusCode[value] = key;
});
const HttpStatusCode$1 = HttpStatusCode;
function createInstance(defaultConfig2) {
  const context = new Axios$1(defaultConfig2);
  const instance = bind(Axios$1.prototype.request, context);
  utils$1.extend(instance, Axios$1.prototype, context, { allOwnKeys: true });
  utils$1.extend(instance, context, null, { allOwnKeys: true });
  instance.create = function create(instanceConfig) {
    return createInstance(mergeConfig(defaultConfig2, instanceConfig));
  };
  return instance;
}
const axios = createInstance(defaults$1);
axios.Axios = Axios$1;
axios.CanceledError = CanceledError;
axios.CancelToken = CancelToken$1;
axios.isCancel = isCancel;
axios.VERSION = VERSION;
axios.toFormData = toFormData;
axios.AxiosError = AxiosError;
axios.Cancel = axios.CanceledError;
axios.all = function all(promises) {
  return Promise.all(promises);
};
axios.spread = spread;
axios.isAxiosError = isAxiosError;
axios.mergeConfig = mergeConfig;
axios.AxiosHeaders = AxiosHeaders$1;
axios.formToJSON = (thing) => formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);
axios.getAdapter = adapters.getAdapter;
axios.HttpStatusCode = HttpStatusCode$1;
axios.default = axios;
const axios$1 = axios;
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
var md5$1 = { exports: {} };
var crypt = { exports: {} };
(function() {
  var base64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", crypt$1 = {
    // Bit-wise rotation left
    rotl: function(n, b) {
      return n << b | n >>> 32 - b;
    },
    // Bit-wise rotation right
    rotr: function(n, b) {
      return n << 32 - b | n >>> b;
    },
    // Swap big-endian to little-endian and vice versa
    endian: function(n) {
      if (n.constructor == Number) {
        return crypt$1.rotl(n, 8) & 16711935 | crypt$1.rotl(n, 24) & 4278255360;
      }
      for (var i = 0; i < n.length; i++)
        n[i] = crypt$1.endian(n[i]);
      return n;
    },
    // Generate an array of any length of random bytes
    randomBytes: function(n) {
      for (var bytes = []; n > 0; n--)
        bytes.push(Math.floor(Math.random() * 256));
      return bytes;
    },
    // Convert a byte array to big-endian 32-bit words
    bytesToWords: function(bytes) {
      for (var words = [], i = 0, b = 0; i < bytes.length; i++, b += 8)
        words[b >>> 5] |= bytes[i] << 24 - b % 32;
      return words;
    },
    // Convert big-endian 32-bit words to a byte array
    wordsToBytes: function(words) {
      for (var bytes = [], b = 0; b < words.length * 32; b += 8)
        bytes.push(words[b >>> 5] >>> 24 - b % 32 & 255);
      return bytes;
    },
    // Convert a byte array to a hex string
    bytesToHex: function(bytes) {
      for (var hex = [], i = 0; i < bytes.length; i++) {
        hex.push((bytes[i] >>> 4).toString(16));
        hex.push((bytes[i] & 15).toString(16));
      }
      return hex.join("");
    },
    // Convert a hex string to a byte array
    hexToBytes: function(hex) {
      for (var bytes = [], c = 0; c < hex.length; c += 2)
        bytes.push(parseInt(hex.substr(c, 2), 16));
      return bytes;
    },
    // Convert a byte array to a base-64 string
    bytesToBase64: function(bytes) {
      for (var base64 = [], i = 0; i < bytes.length; i += 3) {
        var triplet = bytes[i] << 16 | bytes[i + 1] << 8 | bytes[i + 2];
        for (var j = 0; j < 4; j++)
          if (i * 8 + j * 6 <= bytes.length * 8)
            base64.push(base64map.charAt(triplet >>> 6 * (3 - j) & 63));
          else
            base64.push("=");
      }
      return base64.join("");
    },
    // Convert a base-64 string to a byte array
    base64ToBytes: function(base64) {
      base64 = base64.replace(/[^A-Z0-9+\/]/ig, "");
      for (var bytes = [], i = 0, imod4 = 0; i < base64.length; imod4 = ++i % 4) {
        if (imod4 == 0)
          continue;
        bytes.push((base64map.indexOf(base64.charAt(i - 1)) & Math.pow(2, -2 * imod4 + 8) - 1) << imod4 * 2 | base64map.indexOf(base64.charAt(i)) >>> 6 - imod4 * 2);
      }
      return bytes;
    }
  };
  crypt.exports = crypt$1;
})();
var cryptExports = crypt.exports;
var charenc = {
  // UTF-8 encoding
  utf8: {
    // Convert a string to a byte array
    stringToBytes: function(str) {
      return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)));
    },
    // Convert a byte array to a string
    bytesToString: function(bytes) {
      return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)));
    }
  },
  // Binary encoding
  bin: {
    // Convert a string to a byte array
    stringToBytes: function(str) {
      for (var bytes = [], i = 0; i < str.length; i++)
        bytes.push(str.charCodeAt(i) & 255);
      return bytes;
    },
    // Convert a byte array to a string
    bytesToString: function(bytes) {
      for (var str = [], i = 0; i < bytes.length; i++)
        str.push(String.fromCharCode(bytes[i]));
      return str.join("");
    }
  }
};
var charenc_1 = charenc;
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
var isBuffer_1 = function(obj) {
  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer);
};
function isBuffer(obj) {
  return !!obj.constructor && typeof obj.constructor.isBuffer === "function" && obj.constructor.isBuffer(obj);
}
function isSlowBuffer(obj) {
  return typeof obj.readFloatLE === "function" && typeof obj.slice === "function" && isBuffer(obj.slice(0, 0));
}
(function() {
  var crypt2 = cryptExports, utf8 = charenc_1.utf8, isBuffer2 = isBuffer_1, bin = charenc_1.bin, md52 = function(message, options) {
    if (message.constructor == String)
      if (options && options.encoding === "binary")
        message = bin.stringToBytes(message);
      else
        message = utf8.stringToBytes(message);
    else if (isBuffer2(message))
      message = Array.prototype.slice.call(message, 0);
    else if (!Array.isArray(message) && message.constructor !== Uint8Array)
      message = message.toString();
    var m = crypt2.bytesToWords(message), l = message.length * 8, a = 1732584193, b = -271733879, c = -1732584194, d = 271733878;
    for (var i = 0; i < m.length; i++) {
      m[i] = (m[i] << 8 | m[i] >>> 24) & 16711935 | (m[i] << 24 | m[i] >>> 8) & 4278255360;
    }
    m[l >>> 5] |= 128 << l % 32;
    m[(l + 64 >>> 9 << 4) + 14] = l;
    var FF = md52._ff, GG = md52._gg, HH = md52._hh, II = md52._ii;
    for (var i = 0; i < m.length; i += 16) {
      var aa = a, bb = b, cc = c, dd = d;
      a = FF(a, b, c, d, m[i + 0], 7, -680876936);
      d = FF(d, a, b, c, m[i + 1], 12, -389564586);
      c = FF(c, d, a, b, m[i + 2], 17, 606105819);
      b = FF(b, c, d, a, m[i + 3], 22, -1044525330);
      a = FF(a, b, c, d, m[i + 4], 7, -176418897);
      d = FF(d, a, b, c, m[i + 5], 12, 1200080426);
      c = FF(c, d, a, b, m[i + 6], 17, -1473231341);
      b = FF(b, c, d, a, m[i + 7], 22, -45705983);
      a = FF(a, b, c, d, m[i + 8], 7, 1770035416);
      d = FF(d, a, b, c, m[i + 9], 12, -1958414417);
      c = FF(c, d, a, b, m[i + 10], 17, -42063);
      b = FF(b, c, d, a, m[i + 11], 22, -1990404162);
      a = FF(a, b, c, d, m[i + 12], 7, 1804603682);
      d = FF(d, a, b, c, m[i + 13], 12, -40341101);
      c = FF(c, d, a, b, m[i + 14], 17, -1502002290);
      b = FF(b, c, d, a, m[i + 15], 22, 1236535329);
      a = GG(a, b, c, d, m[i + 1], 5, -165796510);
      d = GG(d, a, b, c, m[i + 6], 9, -1069501632);
      c = GG(c, d, a, b, m[i + 11], 14, 643717713);
      b = GG(b, c, d, a, m[i + 0], 20, -373897302);
      a = GG(a, b, c, d, m[i + 5], 5, -701558691);
      d = GG(d, a, b, c, m[i + 10], 9, 38016083);
      c = GG(c, d, a, b, m[i + 15], 14, -660478335);
      b = GG(b, c, d, a, m[i + 4], 20, -405537848);
      a = GG(a, b, c, d, m[i + 9], 5, 568446438);
      d = GG(d, a, b, c, m[i + 14], 9, -1019803690);
      c = GG(c, d, a, b, m[i + 3], 14, -187363961);
      b = GG(b, c, d, a, m[i + 8], 20, 1163531501);
      a = GG(a, b, c, d, m[i + 13], 5, -1444681467);
      d = GG(d, a, b, c, m[i + 2], 9, -51403784);
      c = GG(c, d, a, b, m[i + 7], 14, 1735328473);
      b = GG(b, c, d, a, m[i + 12], 20, -1926607734);
      a = HH(a, b, c, d, m[i + 5], 4, -378558);
      d = HH(d, a, b, c, m[i + 8], 11, -2022574463);
      c = HH(c, d, a, b, m[i + 11], 16, 1839030562);
      b = HH(b, c, d, a, m[i + 14], 23, -35309556);
      a = HH(a, b, c, d, m[i + 1], 4, -1530992060);
      d = HH(d, a, b, c, m[i + 4], 11, 1272893353);
      c = HH(c, d, a, b, m[i + 7], 16, -155497632);
      b = HH(b, c, d, a, m[i + 10], 23, -1094730640);
      a = HH(a, b, c, d, m[i + 13], 4, 681279174);
      d = HH(d, a, b, c, m[i + 0], 11, -358537222);
      c = HH(c, d, a, b, m[i + 3], 16, -722521979);
      b = HH(b, c, d, a, m[i + 6], 23, 76029189);
      a = HH(a, b, c, d, m[i + 9], 4, -640364487);
      d = HH(d, a, b, c, m[i + 12], 11, -421815835);
      c = HH(c, d, a, b, m[i + 15], 16, 530742520);
      b = HH(b, c, d, a, m[i + 2], 23, -995338651);
      a = II(a, b, c, d, m[i + 0], 6, -198630844);
      d = II(d, a, b, c, m[i + 7], 10, 1126891415);
      c = II(c, d, a, b, m[i + 14], 15, -1416354905);
      b = II(b, c, d, a, m[i + 5], 21, -57434055);
      a = II(a, b, c, d, m[i + 12], 6, 1700485571);
      d = II(d, a, b, c, m[i + 3], 10, -1894986606);
      c = II(c, d, a, b, m[i + 10], 15, -1051523);
      b = II(b, c, d, a, m[i + 1], 21, -2054922799);
      a = II(a, b, c, d, m[i + 8], 6, 1873313359);
      d = II(d, a, b, c, m[i + 15], 10, -30611744);
      c = II(c, d, a, b, m[i + 6], 15, -1560198380);
      b = II(b, c, d, a, m[i + 13], 21, 1309151649);
      a = II(a, b, c, d, m[i + 4], 6, -145523070);
      d = II(d, a, b, c, m[i + 11], 10, -1120210379);
      c = II(c, d, a, b, m[i + 2], 15, 718787259);
      b = II(b, c, d, a, m[i + 9], 21, -343485551);
      a = a + aa >>> 0;
      b = b + bb >>> 0;
      c = c + cc >>> 0;
      d = d + dd >>> 0;
    }
    return crypt2.endian([a, b, c, d]);
  };
  md52._ff = function(a, b, c, d, x, s, t) {
    var n = a + (b & c | ~b & d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md52._gg = function(a, b, c, d, x, s, t) {
    var n = a + (b & d | c & ~d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md52._hh = function(a, b, c, d, x, s, t) {
    var n = a + (b ^ c ^ d) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md52._ii = function(a, b, c, d, x, s, t) {
    var n = a + (c ^ (b | ~d)) + (x >>> 0) + t;
    return (n << s | n >>> 32 - s) + b;
  };
  md52._blocksize = 16;
  md52._digestsize = 16;
  md5$1.exports = function(message, options) {
    if (message === void 0 || message === null)
      throw new Error("Illegal argument " + message);
    var digestbytes = crypt2.wordsToBytes(md52(message, options));
    return options && options.asBytes ? digestbytes : options && options.asString ? bin.bytesToString(digestbytes) : crypt2.bytesToHex(digestbytes);
  };
})();
var md5Exports = md5$1.exports;
const md5 = /* @__PURE__ */ getDefaultExportFromCjs(md5Exports);
function hashMd5(str) {
  return md5(str);
}
function tipsMsg(msg, time = 2e3) {
  console.log("[CCBar提示]", msg);
  const existingTip = document.getElementById("ccbar-tip");
  if (existingTip) {
    document.body.removeChild(existingTip);
  }
  const tipElement = document.createElement("div");
  tipElement.id = "ccbar-tip";
  tipElement.style.position = "fixed";
  tipElement.style.top = "20px";
  tipElement.style.left = "50%";
  tipElement.style.transform = "translateX(-50%)";
  tipElement.style.padding = "10px 20px";
  tipElement.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
  tipElement.style.color = "#fff";
  tipElement.style.borderRadius = "4px";
  tipElement.style.zIndex = "9999";
  tipElement.style.transition = "opacity 0.3s ease-in-out";
  tipElement.textContent = msg;
  document.body.appendChild(tipElement);
  setTimeout(() => {
    tipElement.style.opacity = "0";
    setTimeout(() => {
      if (document.body.contains(tipElement)) {
        document.body.removeChild(tipElement);
      }
    }, 300);
  }, time);
}
function ccbarDebugger(msg, data, type = "log") {
  const debug = localStorage.getItem("ccbarDebug") === "true";
  if (debug) {
    if (type === "error") {
      console.error(`[CCBar] ${msg}`, data);
    } else {
      console.log(`[CCBar] ${msg}`, data);
    }
  }
}
function debounce(fn, delay) {
  let timer = null;
  return function(...args) {
    const context = this;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
}
function throttle(fn, threshold = 250) {
  let last;
  let deferTimer = null;
  return function(...args) {
    const context = this;
    const now = Date.now();
    if (last && now < last + threshold) {
      if (deferTimer) {
        clearTimeout(deferTimer);
      }
      deferTimer = setTimeout(() => {
        last = now;
        fn.apply(context, args);
      }, threshold);
    } else {
      last = now;
      fn.apply(context, args);
    }
  };
}
function formatDate(date, format) {
  const o = {
    "M+": date.getMonth() + 1,
    // 月份
    "d+": date.getDate(),
    // 日
    "h+": date.getHours(),
    // 小时
    "m+": date.getMinutes(),
    // 分
    "s+": date.getSeconds(),
    // 秒
    "q+": Math.floor((date.getMonth() + 3) / 3),
    // 季度
    "S": date.getMilliseconds()
    // 毫秒
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? String(o[k]) : ("00" + o[k]).substr(String(o[k]).length)
      );
    }
  }
  return format;
}
function replacePhoneNum(phone, displayPhone) {
  if (phone && phone.startsWith("#")) {
    return advReplace(displayPhone, 3, 4);
  } else {
    return displayPhone;
  }
}
function advReplace(text, start, length, placeText = "*") {
  if (!text)
    return "";
  text = String(text);
  if (Math.abs(start) > text.length)
    return text;
  if (start > 0) {
    const prefix = text.substr(0, start);
    const end = start + length;
    const isLongText = end > text.length;
    const suffix = isLongText ? "" : text.substr(end);
    const replaceLength = isLongText ? text.length - start : length;
    return prefix + placeText.repeat(replaceLength) + suffix;
  } else {
    const end = text.substr(start);
    const startLen = text.length + start - length;
    const replaceLen = startLen > 0 ? length : length + startLen;
    const startIndex = startLen > 0 ? startLen : 0;
    const startText = text.substr(0, startIndex);
    return startText + placeText.repeat(replaceLen) + end;
  }
}
const defaultConfig = {
  baseURL: "/api/yc-ccbar-v1",
  wsURL: "/ws",
  timeout: 1e4,
  autoReconnect: true,
  maxReconnectAttempts: 5,
  reconnectInterval: 3e3,
  heartbeatInterval: 3e4,
  autoReady: false,
  pollingInterval: 5e3,
  entId: "",
  loginKey: "",
  productId: "",
  debug: true
};
function loadConfig() {
  try {
    const savedConfig = localStorage.getItem("ccbarConfig");
    if (savedConfig) {
      return { ...defaultConfig, ...JSON.parse(savedConfig) };
    }
    if (typeof window !== "undefined" && window.ccbarConfig) {
      return {
        ...defaultConfig,
        baseURL: window.ccbarConfig.apiBaseUrl || defaultConfig.baseURL,
        wsURL: window.ccbarConfig.wsUrl || defaultConfig.wsURL
      };
    }
  } catch (error) {
    console.error("加载配置失败:", error);
  }
  return { ...defaultConfig };
}
function initializeWithConfig(config) {
  const mergedConfig = { ...defaultConfig, ...config };
  saveConfig(mergedConfig);
  updateGlobalConfig(mergedConfig);
  console.log("[Config] 系统使用的配置:", mergedConfig);
  return mergedConfig;
}
function saveConfig(config) {
  try {
    localStorage.setItem("ccbarConfig", JSON.stringify(config));
    updateGlobalConfig(config);
  } catch (error) {
    console.error("保存配置失败:", error);
  }
}
function updateGlobalConfig(config) {
  if (typeof window !== "undefined") {
    window.ccbarConfig = {
      apiBaseUrl: config.baseURL,
      wsUrl: config.wsURL
    };
  }
}
class RequestService {
  /**
   * 构造函数
   * @param timeout 请求超时时间（毫秒）
   * @param debug 是否开启调试模式
   */
  constructor(timeout = 1e4, debug = false) {
    this.debug = debug;
    const config = loadConfig();
    const baseURL = config.baseURL || "";
    this.axiosInstance = axios$1.create({
      baseURL,
      timeout,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      }
    });
    if (this.debug) {
      ccbarDebugger(`初始化RequestService，baseURL: ${baseURL}`);
    }
    this.axiosInstance.interceptors.request.use(
      (config2) => {
        var _a;
        if (this.debug) {
          ccbarDebugger(`请求: ${(_a = config2.method) == null ? void 0 : _a.toUpperCase()} ${config2.url}`, config2.data || config2.params);
        }
        return config2;
      },
      (error) => {
        if (this.debug) {
          ccbarDebugger("请求错误", error);
        }
        return Promise.reject(error);
      }
    );
    this.axiosInstance.interceptors.response.use(
      (response) => {
        if (this.debug) {
          ccbarDebugger(`响应: ${response.status}`, response.data);
        }
        if (response.data.state == 1) {
          return response.data;
        } else {
          if (response.data.data.resultCode == "403") {
            console.log("检测到403错误码，立即停止轮询和心跳操作");
            sessionStorage.setItem("isLogined", "false");
            const eventEmitter = EventEmitter.getInstance();
            eventEmitter.emit("system:stopPolling", {
              reason: "403 Forbidden - 会话已过期或无效",
              timestamp: (/* @__PURE__ */ new Date()).getTime(),
              stopPolling: true
            });
            eventEmitter.emit("session:403error", {
              message: "会话已过期，需要重新登录",
              timestamp: (/* @__PURE__ */ new Date()).getTime(),
              stopPolling: true
              // 标记需要停止轮询
            });
            return Promise.reject({
              state: false,
              msg: "会话已过期，需要重新登录",
              data: {
                code: "403",
                content: "会话已过期，需要重新登录"
              }
            });
          } else {
            ElMessage({
              message: response.data.data.content,
              type: "error",
              plain: true
            });
          }
          return response.data;
        }
      },
      (error) => {
        var _a;
        if (this.debug) {
          ccbarDebugger("响应错误", error);
        }
        const response = {
          state: false,
          msg: error.message || "请求失败",
          data: {
            code: ((_a = error.response) == null ? void 0 : _a.status) || "error",
            content: error.message || "未知错误"
          }
        };
        return Promise.reject(response);
      }
    );
  }
  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config 其他配置
   * @returns 请求响应
   */
  async get(url, params, config) {
    try {
      return await this.axiosInstance.get(url, { params, ...config });
    } catch (error) {
      return this.handleError(error);
    }
  }
  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 其他配置
   * @returns 请求响应
   */
  async post(url, data, config) {
    try {
      let jsonpConfig = {};
      return await this.axiosInstance.post(url, data, { ...config, ...jsonpConfig });
    } catch (error) {
      return this.handleError(error);
    }
  }
  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 其他配置
   * @returns 请求响应
   */
  async put(url, data, config) {
    try {
      return await this.axiosInstance.put(url, data, config);
    } catch (error) {
      return this.handleError(error);
    }
  }
  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param config 其他配置
   * @returns 请求响应
   */
  async delete(url, config) {
    try {
      return await this.axiosInstance.delete(url, config);
    } catch (error) {
      return this.handleError(error);
    }
  }
  /**
   * 处理请求错误
   * @param error 错误信息
   * @returns 统一的错误响应
   */
  handleError(error) {
    if (error.state === false) {
      return error;
    }
    return {
      state: false,
      msg: error.message || "请求失败",
      data: {
        code: "error",
        content: error.message || "未知错误"
      }
    };
  }
  /**
   * 使用JSONP方式发送请求
   * @param url 请求URL
   * @param data 请求数据
   * @param callbackName 回调函数名
   * @returns Promise<any>
   */
  jsonp(url, data, callbackName) {
    return new Promise((resolve, reject) => {
      try {
        const script = document.createElement("script");
        const fullUrl = this.axiosInstance.defaults.baseURL + url;
        const params = new URLSearchParams();
        for (const key in data) {
          if (data.hasOwnProperty(key)) {
            params.append(key, data[key]);
          }
        }
        params.append("callbackFunc", callbackName);
        const separator = fullUrl.includes("?") ? "&" : "?";
        script.src = `${fullUrl}${separator}${params.toString()}`;
        window[callbackName] = (response) => {
          var _a, _b, _c;
          document.body.removeChild(script);
          delete window[callbackName];
          if (response.state === void 0) {
            resolve({
              state: ((_a = response.data) == null ? void 0 : _a.code) === "succ" || ((_b = response.data) == null ? void 0 : _b.resultCode) === "000",
              msg: ((_c = response.data) == null ? void 0 : _c.content) || "",
              data: response.data
            });
          } else {
            resolve(response);
          }
        };
        script.onerror = (error) => {
          document.body.removeChild(script);
          delete window[callbackName];
          reject({
            state: false,
            msg: "请求失败",
            data: {
              code: "error",
              content: "网络请求失败"
            }
          });
        };
        document.body.appendChild(script);
      } catch (error) {
        reject({
          state: false,
          msg: error instanceof Error ? error.message : String(error),
          data: {
            code: "error",
            content: "未知错误"
          }
        });
      }
    });
  }
}
new RequestService(5e3, true);
class WebSocketService {
  constructor(url, options = {}) {
    this.ws = null;
    this._isConnected = false;
    this.pingInterval = null;
    this.pingTimer = null;
    this.sessionId = "";
    this._isHeartbeatActive = false;
    this.url = url;
    this.reconnectInterval = options.reconnectInterval || 3e3;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectAttempts = 0;
    this.pingInterval = options.pingInterval || null;
    this.eventEmitter = EventEmitter.getInstance();
  }
  /**
   * 获取连接状态
   * @returns 是否已连接
   */
  get isConnected() {
    return this._isConnected;
  }
  /**
   * 获取心跳状态
   * @returns 是否正在心跳
   */
  get isHeartbeatActive() {
    return this._isHeartbeatActive;
  }
  /**
   * 连接WebSocket
   * @param queryString 连接参数，例如 "?sessionId=xxx&agentId=yyy"
   */
  connect(queryString = "") {
    if (this.ws) {
      this.close();
    }
    try {
      if (queryString && queryString.includes("sessionId=")) {
        const sessionIdMatch = queryString.match(/sessionId=([^&]+)/);
        if (sessionIdMatch && sessionIdMatch[1]) {
          this.sessionId = sessionIdMatch[1];
        }
      }
      const fullUrl = `${this.url}${queryString}`;
      this.ws = new WebSocket(fullUrl);
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
    } catch (error) {
      this.eventEmitter.emit("error", error);
      this.reconnect();
    }
  }
  /**
   * 关闭WebSocket连接
   */
  close() {
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      this.ws.close();
      this.ws = null;
      this._isConnected = false;
      if (this.pingTimer) {
        clearInterval(this.pingTimer);
        this.pingTimer = null;
      }
    }
  }
  /**
   * 发送消息
   * @param data 要发送的数据
   */
  send(data) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.eventEmitter.emit("error", new Error("WebSocket未连接"));
      return false;
    }
    try {
      this.ws.send(data);
      return true;
    } catch (error) {
      this.eventEmitter.emit("error", error);
      return false;
    }
  }
  /**
   * 添加事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event, callback) {
    this.eventEmitter.on(event, callback);
  }
  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event, callback) {
    this.eventEmitter.off(event, callback);
  }
  /**
   * 处理WebSocket连接打开事件
   */
  handleOpen(event) {
    this._isConnected = true;
    this.reconnectAttempts = 0;
    this.eventEmitter.emit("open", event);
    if (this.pingInterval && !this.pingTimer) {
      this.pingTimer = setInterval(() => {
        this.ping();
      }, this.pingInterval);
    }
  }
  /**
   * 处理WebSocket接收消息事件
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      this.eventEmitter.emit("message", data);
    } catch (error) {
      this.eventEmitter.emit("message", event.data);
    }
  }
  /**
   * 处理WebSocket连接关闭事件
   */
  handleClose(event) {
    this._isConnected = false;
    this.eventEmitter.emit("close", event);
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    this.reconnect();
  }
  /**
   * 处理WebSocket连接错误事件
   */
  handleError(event) {
    this.eventEmitter.emit("error", event);
  }
  /**
   * 重新连接WebSocket
   */
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.eventEmitter.emit("reconnecting", this.reconnectAttempts);
        this.connect(this.sessionId ? `?sessionId=${this.sessionId}` : "");
      }, this.reconnectInterval);
    } else {
      this.eventEmitter.emit("reconnect_failed");
    }
  }
  /**
   * 发送心跳包
   */
  ping() {
    if (this._isConnected) {
      try {
        const heartbeatData = {
          cmd: "heartbeat",
          sessionId: this.sessionId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        };
        this.send(JSON.stringify(heartbeatData));
      } catch (error) {
        this.eventEmitter.emit("error", error);
      }
    }
  }
  /**
   * 启动心跳
   * @param interval 心跳间隔时间（毫秒），默认30000毫秒（30秒）
   */
  startHeartbeat(interval = 3e4) {
    if (this._isHeartbeatActive) {
      return;
    }
    this._isHeartbeatActive = true;
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }
    this.pingInterval = interval;
    this.pingTimer = setInterval(() => {
      this.ping();
    }, this.pingInterval);
    console.log(`WebSocket心跳已启动，间隔${interval}ms`);
  }
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    this._isHeartbeatActive = false;
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    console.log("WebSocket心跳已停止");
  }
}
var WorkModeType = /* @__PURE__ */ ((WorkModeType2) => {
  WorkModeType2["INBOUND"] = "inbound";
  WorkModeType2["OUTBOUND"] = "outbound";
  WorkModeType2["PDS"] = "pdsbound";
  WorkModeType2["ALL"] = "all";
  return WorkModeType2;
})(WorkModeType || {});
var AppStateType = /* @__PURE__ */ ((AppStateType2) => {
  AppStateType2["READY"] = "ready";
  AppStateType2["NOT_READY"] = "notReady";
  AppStateType2["UNKNOWN"] = "unknown";
  return AppStateType2;
})(AppStateType || {});
var AgentStateType = /* @__PURE__ */ ((AgentStateType2) => {
  AgentStateType2["IDLE"] = "IDLE";
  AgentStateType2["ALERTING"] = "ALERTING";
  AgentStateType2["TALK"] = "TALK";
  AgentStateType2["BUSY"] = "BUSY";
  AgentStateType2["LOGOFF"] = "LOGOFF";
  AgentStateType2["OCCUPY"] = "OCCUPY";
  AgentStateType2["WORKNOTREADY"] = "WORKNOTREADY";
  AgentStateType2["HELD"] = "HELD";
  AgentStateType2["CONFERENCED"] = "CONFERENCED";
  AgentStateType2["CONSULTED"] = "CONSULTED";
  AgentStateType2["MONITORED"] = "MONITORED";
  AgentStateType2["MUTE"] = "MUTE";
  AgentStateType2["UNMUTE"] = "UNMUTE";
  return AgentStateType2;
})(AgentStateType || {});
var FuncMaskType = /* @__PURE__ */ ((FuncMaskType2) => {
  FuncMaskType2["LOGON"] = "logon";
  FuncMaskType2["LOGOFF"] = "logoff";
  FuncMaskType2["MAKECALL"] = "makecall";
  FuncMaskType2["ANSWERCALL"] = "answercall";
  FuncMaskType2["CLEARCALL"] = "clearcall";
  FuncMaskType2["HOLDCALL"] = "holdcall";
  FuncMaskType2["UNHOLDCALL"] = "unholdcall";
  FuncMaskType2["TRANSFERCALL"] = "transfercall";
  FuncMaskType2["CONSULTATIONCALL"] = "consultationcall";
  FuncMaskType2["CONFERENCECALL"] = "conferencecall";
  FuncMaskType2["AGENTREADY"] = "agentready";
  FuncMaskType2["AGENTNOTREADY"] = "agentnotready";
  FuncMaskType2["WORKREADY"] = "workready";
  FuncMaskType2["CANCELCONSULTATION"] = "cancelconsultation";
  FuncMaskType2["COMPLETETRANSFER"] = "completetransfer";
  FuncMaskType2["MUTECALL"] = "mutecall";
  FuncMaskType2["UNMUTECALL"] = "unmutecall";
  return FuncMaskType2;
})(FuncMaskType || {});
var CallEventType = /* @__PURE__ */ ((CallEventType2) => {
  CallEventType2["ALERTING"] = "evtAlertRouted";
  CallEventType2["CONNECTED"] = "evtEstablished";
  CallEventType2["DISCONNECTED"] = "evtReleased";
  CallEventType2["HELD"] = "evtHeld";
  CallEventType2["RETRIEVED"] = "evtRetrieved";
  CallEventType2["TRANSFERRED"] = "evtTransferred";
  CallEventType2["CONSULTED"] = "evtConsulted";
  CallEventType2["TRANSFERRED_COMPLETE"] = "evtTransferComplete";
  CallEventType2["CONSULT_END"] = "evtConsultEnd";
  return CallEventType2;
})(CallEventType || {});
var NotifyEventType = /* @__PURE__ */ ((NotifyEventType2) => {
  NotifyEventType2["LOGIN"] = "respLogin";
  NotifyEventType2["LOGOUT"] = "respLogout";
  NotifyEventType2["MAKECALL"] = "respMakeCall";
  NotifyEventType2["ANSWERCALL"] = "respAnswerCall";
  NotifyEventType2["CLEARCALL"] = "respClearCall";
  NotifyEventType2["READY"] = "respReady";
  NotifyEventType2["NOTREADY"] = "respNotReady";
  NotifyEventType2["WORKREADY"] = "respWorkReady";
  NotifyEventType2["WORKNOTREADY"] = "respWorkNotReady";
  NotifyEventType2["TRANSFERCALL"] = "respTransferCall";
  NotifyEventType2["CONSULTCALL"] = "respConsultCall";
  NotifyEventType2["CONFERENCECALL"] = "respConferenceCall";
  return NotifyEventType2;
})(NotifyEventType || {});
class GlobalStateManager {
  /**
   * 私有构造函数，防止直接实例化
   */
  constructor(config = {}) {
    this.sessionId = localStorage.getItem("sessionId") || "";
    this.agentId = localStorage.getItem("agentId") || "";
    this.phone = localStorage.getItem("phone") || "";
    this.agentState = {
      state: AgentStateType.LOGOFF,
      workMode: WorkModeType.ALL,
      stateDesc: "未登录",
      notifyContent: "",
      resultDesc: null,
      funcMask: {}
    };
    this.callInfo = null;
    this.lastError = null;
    this.stateHistory = [];
    this.eventEmitter = EventEmitter.getInstance();
    this.config = config;
    this.setupErrorHandling();
    this.addStateHistory("初始化", "系统初始化");
    ccbarDebugger("全局状态管理器已初始化");
  }
  /**
   * 获取状态管理器实例
   */
  static getInstance(config) {
    if (!GlobalStateManager.instance) {
      GlobalStateManager.instance = new GlobalStateManager(config);
    } else if (config) {
      GlobalStateManager.instance.updateConfig(config);
    }
    return GlobalStateManager.instance;
  }
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    ccbarDebugger("全局状态配置已更新", this.config);
  }
  /**
   * 设置错误处理机制
   */
  setupErrorHandling() {
    window.addEventListener("error", (event) => {
      const errorMsg = `全局错误: ${event.message} at ${event.filename}:${event.lineno}`;
      this.setLastError(errorMsg);
      ccbarDebugger(errorMsg, event.error, "error");
      this.validateState();
      return false;
    });
    window.addEventListener("unhandledrejection", (event) => {
      const errorMsg = `未处理的Promise拒绝: ${event.reason}`;
      this.setLastError(errorMsg);
      ccbarDebugger(errorMsg, event.reason, "error");
      this.validateState();
      return false;
    });
  }
  /**
   * 获取默认功能掩码
   */
  getDefaultFuncMask() {
    return {
      [FuncMaskType.LOGON]: true,
      [FuncMaskType.LOGOFF]: false,
      [FuncMaskType.MAKECALL]: false,
      [FuncMaskType.ANSWERCALL]: false,
      [FuncMaskType.CLEARCALL]: false,
      [FuncMaskType.HOLDCALL]: false,
      [FuncMaskType.UNHOLDCALL]: false,
      [FuncMaskType.TRANSFERCALL]: false,
      [FuncMaskType.CONSULTATIONCALL]: false,
      [FuncMaskType.CONFERENCECALL]: false,
      [FuncMaskType.AGENTREADY]: false,
      [FuncMaskType.AGENTNOTREADY]: false,
      [FuncMaskType.WORKREADY]: false,
      [FuncMaskType.CANCELCONSULTATION]: false,
      [FuncMaskType.COMPLETETRANSFER]: false
    };
  }
  /**
   * 根据状态获取功能掩码
   */
  getFuncMaskByState(state) {
    const baseMask = this.getDefaultFuncMask();
    switch (state) {
      case AgentStateType.LOGOFF:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: true,
          [FuncMaskType.LOGOFF]: false
        };
      case AgentStateType.IDLE:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.MAKECALL]: true,
          [FuncMaskType.AGENTREADY]: false,
          [FuncMaskType.AGENTNOTREADY]: true
        };
      case AgentStateType.BUSY:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.MAKECALL]: true,
          [FuncMaskType.AGENTREADY]: true,
          [FuncMaskType.AGENTNOTREADY]: false
        };
      case AgentStateType.TALK:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.MAKECALL]: false,
          [FuncMaskType.CLEARCALL]: true,
          [FuncMaskType.HOLDCALL]: true,
          [FuncMaskType.TRANSFERCALL]: true,
          [FuncMaskType.CONSULTATIONCALL]: true,
          [FuncMaskType.MUTECALL]: true,
          [FuncMaskType.UNMUTECALL]: false
        };
      case AgentStateType.HELD:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.UNHOLDCALL]: true,
          [FuncMaskType.CLEARCALL]: true
        };
      case AgentStateType.WORKNOTREADY:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.WORKREADY]: true
        };
      case AgentStateType.CONSULTED:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.CLEARCALL]: true,
          [FuncMaskType.CANCELCONSULTATION]: true,
          [FuncMaskType.COMPLETETRANSFER]: true,
          [FuncMaskType.CONFERENCECALL]: true
        };
      default:
        return baseMask;
    }
  }
  /**
   * 添加状态历史记录
   */
  addStateHistory(state, description) {
    this.stateHistory.push({
      timestamp: Date.now(),
      state,
      description
    });
    if (this.stateHistory.length > 100) {
      this.stateHistory.shift();
    }
  }
  /**
   * 设置最后错误信息
   */
  setLastError(error) {
    this.lastError = error;
    this.eventEmitter.emit("error", { error });
  }
  /**
   * 验证状态一致性
   * 用于错误恢复和状态自我修复
   */
  validateState() {
    try {
      const expectedFuncMask = this.getFuncMaskByState(this.agentState.state);
      this.agentState.funcMask = {
        ...this.agentState.funcMask,
        ...expectedFuncMask
      };
      this.agentState.stateDesc = this.getStateDesc(this.agentState.state);
      this.eventEmitter.emit("state:repaired", {
        previousState: this.getState(),
        currentState: this.getState(),
        timestamp: Date.now()
      });
      this.addStateHistory("状态修复", "自动修复状态一致性");
    } catch (error) {
      ccbarDebugger("状态验证过程中出错", error, "error");
    }
  }
  /**
   * 获取状态描述
   */
  getStateDesc(state) {
    switch (state) {
      case AgentStateType.IDLE:
        return "空闲";
      case AgentStateType.BUSY:
        return "繁忙";
      case AgentStateType.ALERTING:
        return "振铃";
      case AgentStateType.TALK:
        return "通话中";
      case AgentStateType.HELD:
        return "保持";
      case AgentStateType.WORKNOTREADY:
        return "话后处理中";
      case AgentStateType.LOGOFF:
        return "未登录";
      case AgentStateType.CONSULTED:
        return "咨询";
      case AgentStateType.CONFERENCED:
        return "三方";
      case AgentStateType.MONITORED:
        return "监听";
      case AgentStateType.OCCUPY:
        return "预占";
      default:
        return "未知状态";
    }
  }
  /**
   * 设置代理状态
   */
  setState(state, stateDesc, notifyContent) {
    try {
      const oldState = { ...this.agentState };
      this.agentState.state = state;
      this.agentState.stateDesc = stateDesc || this.getStateDesc(state);
      if (notifyContent !== void 0) {
        this.agentState.notifyContent = notifyContent;
      }
      this.agentState.funcMask = this.getFuncMaskByState(state);
      this.addStateHistory(state, this.agentState.stateDesc);
      this.eventEmitter.emit("state:changed", {
        previousState: oldState,
        currentState: this.getState(),
        timestamp: Date.now()
      });
      this.eventEmitter.emit("agent:stateChanged", {
        agentId: this.agentId,
        state: this.agentState.state,
        timestamp: Date.now()
      });
      this.eventEmitter.emit("agentStateSync", this.agentState);
      ccbarDebugger(`状态已更新: ${oldState.state} -> ${state}`);
    } catch (error) {
      ccbarDebugger("设置状态时出错", error, "error");
      this.setLastError(`设置状态时出错: ${error}`);
    }
  }
  /**
   * 更新通话信息
   */
  updateCallInfo(callInfo, updateState = true) {
    try {
      this.callInfo = callInfo;
      if (updateState && callInfo) {
        switch (callInfo.event.createCause) {
          case "alerting":
            this.setState(AgentStateType.ALERTING, "振铃中");
            break;
          case "connected":
            this.setState(AgentStateType.TALK, "通话中");
            break;
          case "held":
            this.setState(AgentStateType.HELD, "通话保持");
            break;
        }
      } else if (updateState && !callInfo) {
        this.setState(AgentStateType.WORKNOTREADY, "话后整理");
      }
      ccbarDebugger("通话信息已更新", callInfo);
    } catch (error) {
      ccbarDebugger("更新通话信息时出错", error, "error");
      this.setLastError(`更新通话信息时出错: ${error}`);
    }
  }
  /**
   * 更新工作模式
   */
  updateWorkMode(workMode) {
    try {
      this.agentState.workMode = workMode;
      this.eventEmitter.emit("workMode:changed", {
        workMode,
        timestamp: Date.now()
      });
      this.eventEmitter.emit("agentStateSync", this.agentState);
      ccbarDebugger(`工作模式已更新: ${workMode}`);
    } catch (error) {
      ccbarDebugger("更新工作模式时出错", error, "error");
      this.setLastError(`更新工作模式时出错: ${error}`);
    }
  }
  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.agentState };
  }
  /**
   * 获取当前状态类型
   */
  getStateType() {
    return this.agentState.state;
  }
  /**
   * 获取当前登录状态
   * 根据状态判断是否登录，LOGOFF表示未登录，其他状态表示已登录
   */
  isLoggedIn() {
    return this.agentState.state !== AgentStateType.LOGOFF;
  }
  /**
   * 获取会话ID
   */
  getSessionId() {
    return this.sessionId || localStorage.getItem("sessionId") || "";
  }
  /**
   * 获取座席ID
   */
  getAgentId() {
    return this.agentId || localStorage.getItem("agentId") || "";
  }
  /**
   * 获取话机号码
   */
  getPhone() {
    return this.phone || localStorage.getItem("phone") || "";
  }
  /**
   * 设置话机号码
   */
  setPhone(phone) {
    this.phone = phone;
    if (phone) {
      localStorage.setItem("phone", phone);
    }
    ccbarDebugger("话机号码已更新", phone);
  }
  /**
   * 获取通话信息
   */
  getCallInfo() {
    return this.callInfo;
  }
  /**
   * 获取最后错误
   */
  getLastError() {
    return this.lastError;
  }
  /**
   * 获取状态历史
   */
  getStateHistory() {
    return [...this.stateHistory];
  }
  /**
   * 获取功能掩码
   */
  getFuncMask() {
    return { ...this.agentState.funcMask };
  }
  /**
   * 检查功能是否可用
   */
  isFuncEnabled(funcName) {
    return !!this.agentState.funcMask[funcName];
  }
  /**
   * 注册状态变更监听器
   */
  onStateChanged(callback) {
    this.eventEmitter.on("state:changed", callback);
  }
  /**
   * 注册代理状态同步监听器
   */
  onAgentStateSync(callback) {
    this.eventEmitter.on("agentStateSync", callback);
  }
  /**
   * 注册错误监听器
   */
  onError(callback) {
    this.eventEmitter.on("error", callback);
  }
  /**
   * 移除状态变更监听器
   */
  offStateChanged(callback) {
    this.eventEmitter.off("state:changed", callback);
  }
  /**
   * 移除代理状态同步监听器
   */
  offAgentStateSync(callback) {
    this.eventEmitter.off("agentStateSync", callback);
  }
  /**
   * 移除错误监听器
   */
  offError(callback) {
    this.eventEmitter.off("error", callback);
  }
  /**
   * 重置状态
   * 用于系统初始化或彻底重置
   */
  resetState() {
    this.sessionId = "";
    this.agentId = "";
    this.phone = "";
    this.callInfo = null;
    this.lastError = null;
    this.agentState = {
      state: AgentStateType.LOGOFF,
      workMode: WorkModeType.ALL,
      stateDesc: "未登录",
      notifyContent: "",
      resultDesc: null,
      funcMask: this.getDefaultFuncMask()
    };
    this.stateHistory = [];
    this.addStateHistory("重置", "状态已重置");
    this.eventEmitter.emit("state:reset", {
      state: this.getState(),
      timestamp: Date.now()
    });
    this.eventEmitter.emit("agentStateSync", this.agentState);
    ccbarDebugger("状态已重置");
  }
}
class EventManager {
  /**
   * 构造函数
   * @param eventEmitter 事件发射器
   */
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
  }
  /**
   * 注册事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on(eventName, callback) {
    ccbarDebugger("EventManager.on", eventName);
    this.eventEmitter.on(eventName, callback);
  }
  /**
   * 移除事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  off(eventName, callback) {
    ccbarDebugger("EventManager.off", eventName);
    this.eventEmitter.off(eventName, callback);
  }
  /**
   * 注册一次性事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  once(eventName, callback) {
    ccbarDebugger("EventManager.once", eventName);
    this.eventEmitter.once(eventName, callback);
  }
  /**
   * 触发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  emit(eventName, data) {
    ccbarDebugger("EventManager.emit", eventName, data);
    this.eventEmitter.emit(eventName, data);
  }
}
class PollingService {
  // 是否处于恢复模式
  constructor(config) {
    this.isLongPolling = false;
    this.clearLongPollingFlag = false;
    this.lastPollingTime = null;
    this.sessionId = null;
    this.agentId = null;
    this.isLogined = false;
    this.failureCount = 0;
    this.maxRetryCount = 5;
    this.baseRetryDelay = 2e3;
    this.maxRetryDelay = 6e4;
    this.networkRecoveryTimeout = null;
    this.isNetworkOffline = false;
    this.retryTimer = null;
    this.isRecoveryMode = false;
    const globalConfig = loadConfig();
    this.baseURL = globalConfig.baseURL || "/api";
    this.url = config.pollingUrl || `/AgentEvent?action=LongPolling`;
    this.pollingTimeout = config.pollingTimeout || 13e3;
    this.eventEmitter = EventEmitter.getInstance();
    this.requestService = new RequestService(
      this.pollingTimeout,
      false
      // 默认不开启调试
    );
    if (config.maxRetryCount !== void 0)
      this.maxRetryCount = config.maxRetryCount;
    if (config.baseRetryDelay !== void 0)
      this.baseRetryDelay = config.baseRetryDelay;
    if (config.maxRetryDelay !== void 0)
      this.maxRetryDelay = config.maxRetryDelay;
    this.setupNetworkListeners();
  }
  /**
   * 设置网络状态监听器
   */
  setupNetworkListeners() {
    window.addEventListener("online", this.handleNetworkOnline.bind(this));
    window.addEventListener("offline", this.handleNetworkOffline.bind(this));
    this.isNetworkOffline = !navigator.onLine;
    if (this.isNetworkOffline) {
      ccbarDebugger("初始化时检测到网络离线", null, "error");
    }
  }
  /**
   * 处理网络恢复在线
   */
  handleNetworkOnline() {
    ccbarDebugger("网络连接已恢复", null, "log");
    this.isNetworkOffline = false;
    this.clearAllTimers();
    this.resetFailureCount();
    if (this.isLogined && !this.clearLongPollingFlag) {
      ElMessage({
        message: "网络已恢复连接",
        type: "success",
        duration: 3e3
      });
      this.eventEmitter.emit("polling:networkRecovered", {
        timestamp: (/* @__PURE__ */ new Date()).getTime()
      });
      setTimeout(() => {
        this.doLongPolling();
      }, 1e3);
    }
  }
  /**
   * 处理网络离线
   */
  handleNetworkOffline() {
    ccbarDebugger("网络连接已断开", null, "error");
    this.isNetworkOffline = true;
    this.clearAllTimers();
    this.isLongPolling = false;
    ElMessage({
      message: "网络连接已断开，请检查网络设置",
      type: "error",
      duration: 5e3
    });
    this.eventEmitter.emit("polling:networkOffline", {
      timestamp: (/* @__PURE__ */ new Date()).getTime()
    });
    this.isRecoveryMode = true;
  }
  /**
   * 清除所有计时器
   */
  clearAllTimers() {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
    if (this.networkRecoveryTimeout) {
      clearTimeout(this.networkRecoveryTimeout);
      this.networkRecoveryTimeout = null;
    }
  }
  static getInstance(config) {
    if (!PollingService.instance) {
      PollingService.instance = new PollingService(config);
    }
    return PollingService.instance;
  }
  /**
   * 设置会话ID
   * @param sessionId 会话ID
   */
  setSessionId(sessionId) {
    this.sessionId = sessionId;
    ccbarDebugger(`设置会话ID: ${sessionId || "null"}`);
  }
  /**
   * 设置座席ID
   * @param agentId 座席ID
   */
  setAgentId(agentId) {
    this.agentId = agentId;
    ccbarDebugger(`设置座席ID: ${agentId || "null"}`);
  }
  /**
   * 设置登录状态
   * @param isLogined 是否已登录
   */
  setLoginState(isLogined) {
    this.isLogined = isLogined;
    if (isLogined) {
      this.resetFailureCount();
    }
  }
  /**
   * 重置失败计数
   */
  resetFailureCount() {
    this.failureCount = 0;
    this.isRecoveryMode = false;
    this.clearAllTimers();
  }
  /**
   * 计算指数退避延迟时间
   * @returns 下次重试的延迟时间(ms)
   */
  calculateBackoffDelay() {
    const delay = Math.min(
      this.baseRetryDelay * Math.pow(2, this.failureCount - 1),
      this.maxRetryDelay
    );
    return delay + Math.floor(Math.random() * 1e3);
  }
  /**
   * 开始长轮询
   */
  startPolling() {
    ccbarDebugger("开始HTTP长轮询");
    this.eventEmitter.emit("polling:start");
    this.clearLongPollingFlag = false;
    this.resetFailureCount();
    if (this.isNetworkOffline) {
      ccbarDebugger("网络当前离线，暂不启动轮询", null, "error");
      ElMessage({
        message: "网络当前离线，请检查网络后重试",
        type: "warning",
        duration: 5e3
      });
      this.scheduleNetworkRecoveryCheck();
      return;
    }
    this.doLongPolling();
  }
  /**
   * 停止长轮询
   */
  stopPolling() {
    ccbarDebugger("停止HTTP长轮询");
    this.clearLongPollingFlag = true;
    this.isLongPolling = false;
    this.resetFailureCount();
    this.clearAllTimers();
    this.eventEmitter.emit("polling:stop");
  }
  /**
   * 检查网络恢复状态并恢复轮询
   * 使用更保守的检测频率，避免频繁检测消耗资源
   */
  scheduleNetworkRecoveryCheck() {
    if (this.networkRecoveryTimeout || navigator.onLine) {
      return;
    }
    ccbarDebugger("启动网络恢复检测模式", null, "log");
    const checkInterval = this.isRecoveryMode ? this.maxRetryDelay : Math.min(this.baseRetryDelay * Math.pow(2, Math.min(this.failureCount, 8)), this.maxRetryDelay);
    this.networkRecoveryTimeout = setTimeout(() => {
      if (navigator.onLine) {
        ccbarDebugger("浏览器报告网络已恢复", null, "log");
        this.handleNetworkOnline();
        return;
      }
      ccbarDebugger(`执行网络恢复检测 (检测间隔: ${checkInterval}ms)...`);
      fetch(window.location.origin + "/favicon.ico", {
        method: "HEAD",
        cache: "no-store",
        // 设置一个短超时，避免长时间等待
        signal: AbortSignal.timeout(3e3)
      }).then(() => {
        ccbarDebugger("网络检测成功，网络已恢复", null, "log");
        this.isNetworkOffline = false;
        this.resetFailureCount();
        if (this.isLogined && !this.clearLongPollingFlag) {
          ElMessage({
            message: "网络已恢复连接",
            type: "success",
            duration: 3e3
          });
          this.doLongPolling();
        }
      }).catch((err) => {
        ccbarDebugger(`网络仍未恢复: ${err.message}`, null, "error");
        this.isNetworkOffline = true;
        if (this.failureCount < 10) {
          this.failureCount++;
        }
        this.networkRecoveryTimeout = null;
        this.scheduleNetworkRecoveryCheck();
      });
    }, checkInterval);
    ccbarDebugger(`已安排网络恢复检测，将在 ${checkInterval}ms 后执行`, null, "log");
  }
  /**
   * 执行长轮询
   * 基于ccbar.js中的longPolling方法实现
   */
  doLongPolling() {
    if (this.isLongPolling || !this.isLogined || this.isNetworkOffline) {
      if (this.isNetworkOffline) {
        ccbarDebugger("网络离线，不执行轮询", null, "error");
        this.scheduleNetworkRecoveryCheck();
      }
      return false;
    }
    if (this.isRecoveryMode) {
      ccbarDebugger("系统处于网络恢复模式，不执行常规轮询", null, "error");
      this.scheduleNetworkRecoveryCheck();
      return false;
    }
    this.isLongPolling = true;
    this.clearLongPollingFlag = false;
    const startTime = (/* @__PURE__ */ new Date()).getTime();
    const data = {
      reload: false,
      cuid: localStorage.getItem("cuid") || ""
    };
    const pollingParams = {
      data
    };
    ccbarDebugger("发送长轮询请求", data);
    this.requestService.post(this.url, pollingParams, {
      timeout: this.pollingTimeout,
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "access-control-allow-headers": "cuid"
      }
    }).then((result) => {
      this.isLongPolling = false;
      this.lastPollingTime = /* @__PURE__ */ new Date();
      this.resetFailureCount();
      ccbarDebugger("长轮询成功", result);
      this.eventEmitter.emit("polling:success", {
        time: (/* @__PURE__ */ new Date()).getTime(),
        delay: (/* @__PURE__ */ new Date()).getTime() - startTime,
        online: true,
        data: result.data
      });
      if (result.data && result.data.events) {
        this.handlePollingEvents(result.data.events);
      }
      this.eventEmitter.emit("polling:stateSync", {
        timestamp: (/* @__PURE__ */ new Date()).getTime(),
        sessionId: this.sessionId,
        agentId: this.agentId
      });
      if (this.isLogined && !this.clearLongPollingFlag) {
        this.doLongPolling();
      }
    }).catch((error) => {
      var _a, _b;
      this.isLongPolling = false;
      this.failureCount++;
      ccbarDebugger(`长轮询失败 (第${this.failureCount}次)`, error, "error");
      if (error && (((_a = error.data) == null ? void 0 : _a.code) === "403" || ((_b = error.data) == null ? void 0 : _b.resultCode) === "403")) {
        ccbarDebugger("检测到403错误，立即停止轮询", error, "error");
        this.clearLongPollingFlag = true;
        this.isLogined = false;
        this.resetFailureCount();
        this.eventEmitter.emit("session:403error", {
          message: "会话已过期，需要重新登录",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          stopPolling: true,
          error
          // 添加错误详情
        });
        this.eventEmitter.emit("polling:forceStop", {
          reason: "403 Forbidden - 会话已过期或无效",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return;
      }
      const isNetworkError = this.isNetworkErrorType(error);
      if (isNetworkError) {
        this.isNetworkOffline = true;
        ccbarDebugger("检测到网络错误，标记为离线状态", error, "error");
      }
      this.eventEmitter.emit("polling:error", {
        time: (/* @__PURE__ */ new Date()).getTime(),
        delay: (/* @__PURE__ */ new Date()).getTime() - startTime,
        online: !this.isNetworkOffline,
        error,
        retryCount: this.failureCount,
        maxRetries: this.maxRetryCount,
        isNetworkError
      });
      const now = /* @__PURE__ */ new Date();
      if (this.lastPollingTime && now.getTime() - this.lastPollingTime.getTime() > 6e4) {
        this.eventEmitter.emit("session:timeout");
        ccbarDebugger("长轮询超时，会话可能已终止", null, "error");
        return;
      }
      if (this.failureCount >= this.maxRetryCount || this.isNetworkOffline) {
        this.isRecoveryMode = true;
        ccbarDebugger(
          this.isNetworkOffline ? "检测到网络离线，切换到网络恢复检测模式" : `已达到最大重试次数(${this.maxRetryCount})，切换到网络恢复检测模式`,
          null,
          "error"
        );
        if (this.failureCount === this.maxRetryCount || isNetworkError) {
          ElMessage({
            message: "网络连接异常，系统将在网络恢复后自动重连",
            type: "warning",
            duration: 5e3
          });
        }
        this.eventEmitter.emit("polling:networkIssue", {
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          failureCount: this.failureCount,
          message: "网络连接异常，已暂停请求",
          isOffline: this.isNetworkOffline
        });
        this.scheduleNetworkRecoveryCheck();
        return;
      }
      if (this.isLogined && !this.clearLongPollingFlag && !this.isRecoveryMode) {
        const retryDelay = this.calculateBackoffDelay();
        ccbarDebugger(`将在 ${retryDelay}ms 后重试轮询 (第${this.failureCount}次)`, null, "error");
        if (this.retryTimer) {
          clearTimeout(this.retryTimer);
        }
        this.retryTimer = setTimeout(() => {
          this.retryTimer = null;
          this.doLongPolling();
        }, retryDelay);
      }
    });
    return true;
  }
  /**
   * 判断错误是否为网络错误
   * @param error 错误对象
   * @returns 是否为网络错误
   */
  isNetworkErrorType(error) {
    return !navigator.onLine || error && (error.message === "Network Error" || error.name === "AbortError" || error.name === "TypeError" && error.message.includes("network") || error.code === "ECONNABORTED" || error.response === void 0 && error.request || (error.statusText === "timeout" || error.statusText === "Network Error"));
  }
  /**
   * 处理轮询返回的事件
   * @param events 事件数组
   */
  handlePollingEvents(events) {
    if (!Array.isArray(events) || events.length === 0)
      return;
    events.forEach((event) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
      ccbarDebugger("处理轮询事件", event);
      if (event.messageId) {
        switch (event.messageId) {
          case "notify":
            console.log("准备发送通知事件，完整事件对象:", JSON.stringify(event));
            ({
              agentId: event.agentId || "unknown",
              resultCode: ((_a = event.cmddata) == null ? void 0 : _a.resultCode) || "info",
              resultDesc: ((_b = event.cmddata) == null ? void 0 : _b.resultDesc) || "系统通知",
              srcMessageId: ((_c = event.cmddata) == null ? void 0 : _c.srcMessageId) || "unknown",
              timestamp: event.timestamp || (/* @__PURE__ */ new Date()).getTime(),
              rawEvent: event
              // 添加原始事件对象以便调试
            });
            if (event.cmddata && event.cmddata.srcMessageId === "respLogin") {
              console.log("准备发送登录响应事件，完整事件对象:", JSON.stringify(event));
              const responseData = {
                agentId: event.agentId || "unknown",
                workno: ((_e = (_d = event.cmddata) == null ? void 0 : _d.result) == null ? void 0 : _e.workno) || "unknown",
                loginTime: ((_g = (_f = event.cmddata) == null ? void 0 : _f.result) == null ? void 0 : _g.loginTime) || (/* @__PURE__ */ new Date()).toISOString(),
                resultCode: ((_h = event.cmddata) == null ? void 0 : _h.resultCode) || "error",
                resultDesc: ((_i = event.cmddata) == null ? void 0 : _i.resultDesc) || "未知响应",
                timestamp: event.timestamp || (/* @__PURE__ */ new Date()).getTime(),
                rawEvent: event
                // 添加原始事件对象以便调试
              };
              this.eventEmitter.emit("agent:loginResponse", responseData);
            } else {
              this.eventEmitter.emit(`notify:${((_j = event.cmddata) == null ? void 0 : _j.srcMessageId) || "unknown"}`, event);
            }
            if (((_k = event.cmddata) == null ? void 0 : _k.resultCode) === "0") {
              if ((_l = event.cmddata) == null ? void 0 : _l.resultDesc) {
                ElMessage({
                  message: (_m = event.cmddata) == null ? void 0 : _m.resultDesc,
                  type: "success",
                  plain: true
                });
              }
            } else {
              ElMessage({
                message: (_n = event.cmddata) == null ? void 0 : _n.resultDesc,
                type: "error",
                plain: true
              });
            }
            break;
          case "agentStateSync":
            this.eventEmitter.emit("agent:stateChanged", {
              agentId: event.agentId,
              state: event.cmddata.state,
              stateDesc: event.cmddata.stateDesc,
              funcMask: event.cmddata.funcMask,
              timestamp: event.timestamp
            });
            break;
          case "callEventSync":
            this.eventEmitter.emit("call:eventSync", {
              agentId: event.agentId,
              // 工号
              callId: event.cmddata.event.callId,
              // 呼叫ID
              callEventId: event.cmddata.callEventId,
              // 呼叫事件ID
              entId: event.cmddata.event.entId,
              // 企业ID
              caller: event.cmddata.event.caller,
              // 主叫
              called: event.cmddata.event.called,
              // 被叫
              custPhone: event.cmddata.event.custPhone
              // 客户电话
            });
            break;
          default:
            this.eventEmitter.emit(`polling:${event.messageId}`, event);
            break;
        }
      }
    });
    this.eventEmitter.emit("polling:events", events);
  }
  /**
   * 添加事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event, callback) {
    this.eventEmitter.on(event, callback);
  }
  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event, callback) {
    this.eventEmitter.off(event, callback);
  }
  /**
   * 一次性事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  once(event, callback) {
    this.eventEmitter.once(event, callback);
  }
}
class ConnectionManager {
  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param wsService WebSocket服务
   * @param eventManager 事件管理器
   * @param config 配置信息
   */
  constructor(requestService, wsService, eventManager, config) {
    this.pollingService = null;
    this.isLogined = false;
    this.sessionId = "";
    this.agentInfo = {
      agentId: "",
      password: "",
      state: AgentStateType.LOGOFF
    };
    this.requestService = requestService;
    this.wsService = wsService;
    this.eventManager = eventManager;
    this.config = config;
    this.initPollingService();
    if (this.wsService) {
      this.initWebSocketEvents();
    }
  }
  /**
   * 初始化轮询服务
   */
  initPollingService() {
    this.pollingService = PollingService.getInstance({
      baseURL: this.config.baseURL || "",
      pollingInterval: this.config.pollingInterval || 1e4
    });
    if (this.pollingService) {
      this.pollingService.on("session:timeout", () => {
        this.eventManager.emit("session:timeout", {
          message: "会话已超时，请重新登录"
        });
      });
      this.eventManager.on("system:stopPolling", (data) => {
        ccbarDebugger(`收到停止轮询指令: ${data.reason || "Unknown reason"}`);
        if (this.pollingService) {
          ccbarDebugger("断开长轮询连接");
          this.pollingService.stopPolling();
          this.pollingService.setLoginState(false);
          this.pollingService.setSessionId(null);
          this.pollingService.setAgentId(null);
          this.eventManager.emit("polling:stopped", {
            reason: data.reason,
            timestamp: data.timestamp || (/* @__PURE__ */ new Date()).getTime()
          });
        }
      });
      this.eventManager.on("polling:forceStop", (data) => {
        ccbarDebugger(`收到强制停止轮询指令: ${data.reason || "403错误"}`);
        if (this.pollingService) {
          this.pollingService.stopPolling();
          this.pollingService.setLoginState(false);
          this.pollingService.setSessionId(null);
          this.pollingService.setAgentId(null);
          ccbarDebugger("轮询服务已被强制停止");
          this.eventManager.emit("polling:stopped", {
            reason: data.reason,
            timestamp: data.timestamp || (/* @__PURE__ */ new Date()).getTime(),
            isForced: true
          });
          if (this.wsService) {
            this.wsService.stopHeartbeat();
          }
        }
      });
      this.pollingService.on("call:incoming", (data) => {
        this.eventManager.emit("call:incoming", data);
      });
      this.pollingService.on("call:established", (data) => {
        this.eventManager.emit("call:established", data);
      });
      this.pollingService.on("call:ended", (data) => {
        this.eventManager.emit("call:ended", data);
      });
      this.pollingService.on("agent:stateChanged", (data) => {
        this.eventManager.emit("agent:stateChanged", data);
      });
    }
  }
  /**
   * 初始化WebSocket事件
   */
  initWebSocketEvents() {
    if (!this.wsService)
      return;
    this.wsService.on("message", (data) => {
      try {
        if (data.event) {
          switch (data.event) {
            case "callOffered":
              this.eventManager.emit("call:incoming", data);
              break;
            case "callEstablished":
              this.eventManager.emit("call:established", data);
              break;
            case "callReleased":
              this.eventManager.emit("call:ended", data);
              break;
            case "agentStateChanged":
              this.eventManager.emit("agent:stateChanged", data);
              break;
            case "heartbeatResponse":
              break;
            default:
              this.eventManager.emit(`ws:${data.event}`, data);
              break;
          }
        }
      } catch (error) {
        ccbarDebugger("WebSocket消息处理错误", error, "error");
      }
    });
    this.wsService.on("open", () => {
      ccbarDebugger("WebSocket连接成功");
      this.eventManager.emit("ws:connected");
      this.startHeartbeat();
    });
    this.wsService.on("close", () => {
      ccbarDebugger("WebSocket连接关闭");
      this.eventManager.emit("ws:disconnected");
      this.startPolling();
    });
    this.wsService.on("error", (error) => {
      ccbarDebugger("WebSocket连接错误", error);
      this.eventManager.emit("ws:error", error);
      this.startPolling();
    });
  }
  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果
   */
  async login(params) {
    var _a, _b, _c, _d;
    try {
      this.agentInfo.agentId = params.username;
      this.agentInfo.password = params.password;
      this.agentInfo.phoneType = params.phoneType || "softphone";
      this.agentInfo.phone = params.phone || "";
      this.agentInfo.credential = params.credential || "";
      const loginParams = {
        data: {
          agentId: params.username,
          token: hashMd5(params.password),
          // 使用MD5加密密码
          phoneNum: params.phone || "",
          readyMode: params.readyMode || "notReady",
          autoAnswer: params.autoAnswer || false,
          entId: this.config.entId || "",
          loginKey: this.config.loginKey || "",
          productId: this.config.productId || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          skillId: params.skillId || "",
          credential: params.credential || ""
        }
      };
      const result = await this.requestService.post("AgentEvent?action=login", loginParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state && (((_a = result.data) == null ? void 0 : _a.code) === "succ" || ((_b = result.data) == null ? void 0 : _b.resultCode) === "000")) {
        this.isLogined = true;
        sessionStorage.setItem("isLogined", this.isLogined.toString());
        this.agentInfo.state = AgentStateType.BUSY;
        if (result.data.result && result.data.result.sessionId) {
          this.sessionId = result.data.result.sessionId;
        }
        localStorage.setItem("sessionId", this.sessionId);
        localStorage.setItem("agentId", result.data.result.agentId);
        localStorage.setItem("cuid", result.data.result.cuid);
        this.startPolling();
        return {
          state: true,
          msg: "登录成功",
          data: {
            code: "succ",
            content: "登录成功",
            result: {
              agentId: result.data.result.agentId,
              state: this.agentInfo.state,
              sessionId: this.sessionId
            }
          }
        };
      } else {
        return {
          state: false,
          msg: ((_c = result.data) == null ? void 0 : _c.content) || "登录失败",
          data: {
            code: "fail",
            content: ((_d = result.data) == null ? void 0 : _d.content) || "登录失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("登录异常", error);
      return {
        state: false,
        msg: error.message || "登录异常",
        data: {
          code: "error",
          content: error.message || "登录异常",
          result: null
        }
      };
    }
  }
  /**
   * 座席登出
   * @returns 登出结果
   */
  async logout() {
    if (sessionStorage.getItem("isLogined") && sessionStorage.getItem("isLogined") === "false") {
      return {
        state: false,
        msg: "座席未登录",
        data: {
          code: "fail",
          content: "座席未登录",
          result: null
        }
      };
    }
    try {
      const logoutParams = {
        data: {
          cmd: "logout",
          agentId: this.agentInfo.agentId,
          sessionId: this.sessionId,
          event: "agentlogout",
          messageId: "cmdLogout",
          cuid: localStorage.getItem("cuid") || "",
          rid: "",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      if (this.pollingService) {
        ccbarDebugger("断开长轮询连接");
        this.pollingService.stopPolling();
        this.pollingService.setLoginState(false);
        this.pollingService.setSessionId(null);
        this.pollingService.setAgentId(null);
      }
      if (this.wsService) {
        ccbarDebugger("断开WebSocket连接");
        this.wsService.close();
        this.wsService.stopHeartbeat();
      }
      const result = await this.requestService.post("AgentEvent?action=event", logoutParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      this.isLogined = false;
      sessionStorage.setItem("isLogined", "false");
      this.sessionId = "";
      this.agentInfo.state = AgentStateType.LOGOFF;
      localStorage.removeItem("sessionId");
      localStorage.removeItem("agentId");
      localStorage.removeItem("cuid");
      return {
        state: true,
        msg: "登出成功",
        data: {
          code: "succ",
          content: "登出成功",
          result: null
        }
      };
    } catch (error) {
      ccbarDebugger("登出异常", error);
      return {
        state: false,
        msg: error.message || "登出异常",
        data: {
          code: "error",
          content: error.message || "登出异常",
          result: null
        }
      };
    }
  }
  async initCCbar() {
    try {
      const initParams = {
        data: {
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=ccbarInit", initParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      return {
        state: true,
        msg: "初始化成功",
        data: {
          code: "succ",
          content: "初始化成功",
          result: result.data
        }
      };
    } catch (error) {
      ccbarDebugger("登出异常", error);
      return {
        state: false,
        msg: error.message || "登出异常",
        data: {
          code: "error",
          content: error.message || "登出异常",
          result: null
        }
      };
    }
  }
  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isLoggedIn() {
    return sessionStorage.getItem("isLogined") === "true";
  }
  /**
   * 检查是否已连接
   * @returns 是否已连接
   */
  isConnected() {
    var _a;
    return ((_a = this.wsService) == null ? void 0 : _a.isConnected) || false;
  }
  /**
   * 获取会话ID
   * @returns 会话ID
   */
  getSessionId() {
    return this.sessionId;
  }
  /**
   * 获取座席信息
   * @returns 座席信息
   */
  getAgentInfo() {
    return {
      agentId: this.agentInfo.agentId,
      state: this.agentInfo.state,
      phoneType: this.agentInfo.phoneType || "softphone",
      phone: this.agentInfo.phone || ""
    };
  }
  /**
   * 设置登录状态
   * @param isLogined 登录状态
   */
  setLoginState(isLogined) {
    this.isLogined = isLogined;
    sessionStorage.setItem("isLogined", isLogined.toString());
    if (this.pollingService) {
      this.pollingService.setLoginState(isLogined);
      if (!isLogined) {
        this.pollingService.stopPolling();
      }
    }
    this.eventManager.emit("connection:state", {
      isLogined,
      timestamp: (/* @__PURE__ */ new Date()).getTime()
    });
  }
  /**
   * 启动WebSocket心跳
   */
  startHeartbeat() {
    if (this.wsService && this.wsService.isConnected) {
      this.wsService.startHeartbeat(3e4);
      ccbarDebugger("WebSocket心跳已启动");
    }
  }
  /**
   * 启动HTTP轮询
   */
  startPolling() {
    if (!this.pollingService || !this.isLogined) {
      ccbarDebugger("无法启动轮询：服务未初始化或用户未登录");
      return;
    }
    ccbarDebugger("开始HTTP长轮询");
    this.pollingService.setSessionId(this.sessionId);
    this.pollingService.setAgentId(this.agentInfo.agentId);
    this.pollingService.setLoginState(this.isLogined);
    this.pollingService.startPolling();
    ccbarDebugger("HTTP长轮询已启动");
  }
}
class StateManager {
  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param config 配置信息
   */
  constructor(requestService, eventManager, config) {
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.config = config;
    this.agentId = localStorage.getItem("agentId") || "";
    this.currentState = AgentStateType.LOGOFF;
  }
  /**
   * 更新会话ID和座席ID
   * @param sessionId 新的会话ID
   * @param agentId 座席ID
   * @deprecated sessionId在状态管理中不再使用，但为保持API兼容性而保留
   */
  updateSessionId(sessionId, agentId) {
    if (agentId) {
      this.agentId = agentId;
    }
    ccbarDebugger("StateManager.updateSessionId被调用，但sessionId在状态功能中未使用");
  }
  /**
   * 设置座席就绪
   * @returns 设置结果
   */
  async setAgentReady() {
    return await this.setState(AgentStateType.IDLE);
  }
  /**
   * 设置座席未就绪
   * @param busyType 忙碌类型
   * @returns 设置结果
   */
  async setAgentNotReady(busyType) {
    return await this.setState(AgentStateType.BUSY, busyType);
  }
  /**
   * 设置话后整理状态
   * @returns 设置结果
   */
  async setWorkNotReady() {
    return await this.setState(AgentStateType.WORKNOTREADY);
  }
  /**
   * 完成话后整理
   * @returns 设置结果
   */
  async setWorkReady() {
    return await this.setState(AgentStateType.IDLE);
  }
  /**
   * 设置工作模式
   * @param mode 工作模式
   * @returns 操作结果
   */
  async setWorkMode(mode) {
    var _a, _b, _c, _d;
    if (![WorkModeType.INBOUND, WorkModeType.OUTBOUND, WorkModeType.PDS, WorkModeType.ALL].includes(mode)) {
      return {
        state: false,
        msg: "不支持的工作模式",
        data: {
          code: "fail",
          content: "不支持的工作模式",
          result: null
        }
      };
    }
    try {
      const workModeParams = {
        cmdJson: JSON.stringify({
          cmd: "setWorkMode",
          agentId: this.agentId,
          workMode: mode,
          event: "setWorkMode",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        })
      };
      const result = await this.requestService.post("/AgentEvent?action=event", workModeParams);
      if (result.state && (((_a = result.data) == null ? void 0 : _a.code) === "succ" || ((_b = result.data) == null ? void 0 : _b.resultCode) === "000")) {
        return {
          state: true,
          msg: "工作模式设置成功",
          data: {
            code: "succ",
            content: "工作模式设置成功",
            result: {
              workMode: mode
            }
          }
        };
      } else {
        return {
          state: false,
          msg: ((_c = result.data) == null ? void 0 : _c.content) || "工作模式设置失败",
          data: {
            code: "fail",
            content: ((_d = result.data) == null ? void 0 : _d.content) || "工作模式设置失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("工作模式设置异常", error);
      return {
        state: false,
        msg: error.message || "工作模式设置异常",
        data: {
          code: "error",
          content: error.message || "工作模式设置异常",
          result: null
        }
      };
    }
  }
  /**
   * 获取功能状态
   * @param func 功能名称
   * @returns 功能是否可用
   */
  getFuncMask(func) {
    return true;
  }
  /**
   * 获取座席状态
   * @returns 座席状态
   */
  getState() {
    return this.currentState;
  }
  /**
   * 设置座席状态
   * @param state 目标状态
   * @param busyType 忙碌类型（可选）
   * @returns 设置结果
   */
  async setState(state, busyType) {
    var _a, _b;
    try {
      let command = "";
      let event = "";
      if (state === AgentStateType.IDLE) {
        command = "cmdReady";
        event = "agentReady";
      } else if (state === AgentStateType.BUSY) {
        command = "cmdNotReady";
        event = "agentNotReady";
      } else if (state === AgentStateType.WORKNOTREADY) {
        command = "cmdWorkNotReady";
        event = "workNotReady";
      } else {
        return {
          state: false,
          msg: "不支持的状态变更类型",
          data: {
            code: "fail",
            content: "不支持的状态变更类型",
            result: null
          }
        };
      }
      const stateParams = {
        data: {
          agentId: this.agentId || localStorage.getItem("agentId"),
          messageId: command,
          event,
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          cuid: localStorage.getItem("cuid")
        }
      };
      //busyType 是忙碌类型，state 是状态
      if (busyType && state === AgentStateType.BUSY) {
        const existingCmdJson = stateParams.cmdJson || "{}";
        stateParams.cmdJson = JSON.stringify({
          ...JSON.parse(existingCmdJson),
          busyType
        });
      }
      const result = await this.requestService.post("/AgentEvent?action=event", stateParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.currentState = state;
        return {
          state: true,
          msg: "状态变更成功",
          data: {
            code: "succ",
            content: "状态变更成功",
            result: {
              state: this.currentState
            }
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "状态变更失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "状态变更失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("状态变更异常", error);
      return {
        state: false,
        msg: error.message || "状态变更异常",
        data: {
          code: "error",
          content: error.message || "状态变更异常",
          result: null
        }
      };
    }
  }
  /**
   * 获取当前状态
   * @returns 当前状态
   */
  getCurrentState() {
    return this.currentState;
  }
  /**
   * 更新当前状态（通常由事件触发）
   * @param state 新状态
   */
  updateCurrentState(state) {
    const oldState = this.currentState;
    this.currentState = state;
    if (oldState !== state) {
      this.eventManager.emit("agent:stateChanged", {
        agentId: this.agentId,
        state: this.currentState,
        timestamp: (/* @__PURE__ */ new Date()).getTime()
      });
    }
  }
}
class CallManager {
  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param sessionId 会话ID
   * @param agentId 座席ID
   */
  constructor(requestService, eventManager, sessionId, agentId) {
    this.currentCallId = null;
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.sessionId = sessionId;
    this.agentId = agentId;
    this.eventManager.on("call:incoming", (data) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });
    this.eventManager.on("call:ended", () => {
      this.currentCallId = null;
    });
  }
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  updateSessionId(sessionId) {
    this.sessionId = sessionId;
  }
  /**
   * 发起呼叫
   * @param phoneNumber 呼叫的电话号码
   * @param displayNumber 外显号码
   * @returns 呼叫结果
   */
  async makeCall(phoneNumber, displayNumber) {
    var _a, _b;
    try {
      const callParams = {
        data: {
          agentId: this.agentId || localStorage.getItem("agentId") || "",
          caller: displayNumber || "",
          called: phoneNumber,
          userData: {},
          callType: 2,
          skillId: "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          cuid: localStorage.getItem("cuid") || ""
        }
      };
      const result = await this.requestService.post("/CallEvent?action=Makecall", callParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        if (result.data.result && result.data.result.callId) {
          this.currentCallId = result.data.result.callId;
        }
        this.eventManager.emit("call:dialing", {
          phoneNumber,
          displayNumber,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "外呼请求已发送",
          data: {
            code: "succ",
            content: "外呼请求已发送",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "外呼失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "外呼失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("外呼异常", error);
      return {
        state: false,
        msg: error.message || "外呼异常",
        data: {
          code: "error",
          content: error.message || "外呼异常",
          result: null
        }
      };
    }
  }
  /**
   * 应答呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 应答结果
   */
  async answerCall(callId) {
    var _a, _b;
    const targetCallId = callId || this.currentCallId;
    if (!this.sessionId) {
      return {
        state: false,
        msg: "会话ID不存在，请先登录",
        data: {
          code: "fail",
          content: "会话ID不存在，请先登录",
          result: null
        }
      };
    }
    if (!targetCallId) {
      return {
        state: false,
        msg: "没有需要应答的呼叫",
        data: {
          code: "fail",
          content: "没有需要应答的呼叫",
          result: null
        }
      };
    }
    try {
      const answerParams = {
        cmdJson: JSON.stringify({
          cmd: "answerCall",
          agentId: this.agentId,
          sessionId: this.sessionId,
          callId: targetCallId,
          event: "answerCall",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        })
      };
      const result = await this.requestService.post("/AgentEvent?action=event", answerParams);
      if (result.state) {
        this.eventManager.emit("call:answered", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "呼叫已应答",
          data: {
            code: "succ",
            content: "呼叫已应答",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "应答失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "应答失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("应答异常", error);
      return {
        state: false,
        msg: error.message || "应答异常",
        data: {
          code: "error",
          content: error.message || "应答异常",
          result: null
        }
      };
    }
  }
  /**
  * 麦克风静音
  * @param callId 呼叫ID，不传则使用当前呼叫
  * @returns 静音结果
  */
  async muteCall(callId) {
    var _a, _b;
    const targetCallId = callId || this.getCurrentCallId();
    try {
      const muteParams = {
        data: {
          messageId: "cmdMuteCall",
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", muteParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("call:muted", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "麦克风已静音",
          data: {
            code: "succ",
            content: "麦克风已静音",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "静音失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "静音失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("静音异常", error);
      return {
        state: false,
        msg: error.message || "静音操作失败",
        data: {
          code: "error",
          content: error.message || "静音操作失败"
        }
      };
    }
  }
  /**
   * 取消麦克风静音
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取消静音结果
   */
  async unmuteCall(callId) {
    var _a, _b;
    const targetCallId = callId || this.getCurrentCallId();
    try {
      const unmuteParams = {
        data: {
          messageId: "cmdRetrieveCall",
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          type: 12
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", unmuteParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("call:unmuted", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "麦克风已取消静音",
          data: {
            code: "succ",
            content: "麦克风已取消静音",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "取消静音失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "取消静音失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("取消静音异常", error);
      return {
        state: false,
        msg: error.message || "取消静音操作失败",
        data: {
          code: "error",
          content: error.message || "取消静音操作失败"
        }
      };
    }
  }
  /**
   * 挂断呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 挂断结果
   */
  async hangupCall(callData) {
    var _a, _b;
    const targetCallId = callData || this.currentCallId;
    const callParams = {
      data: {
        agentId: this.agentId || localStorage.getItem("agentId") || "",
        caller: callData.caller || "114",
        called: callData.called || "",
        callId: callData.callId || "",
        messageId: "cmdClearCall",
        timestamp: (/* @__PURE__ */ new Date()).getTime(),
        cuid: localStorage.getItem("cuid") || ""
      }
    };
    const result = await this.requestService.post("/AgentEvent?action=event", callParams, {
      headers: {
        "Content-Type": "application/json;charset=UTF-8"
      }
    });
    try {
      if (result.state) {
        if (targetCallId === this.currentCallId) {
          this.currentCallId = null;
        }
        this.eventManager.emit("call:hangup", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "呼叫已挂断",
          data: {
            code: "succ",
            content: "呼叫已挂断",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "挂断失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "挂断失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("挂断异常", error);
      return {
        state: false,
        msg: error.message || "挂断异常",
        data: {
          code: "error",
          content: error.message || "挂断异常",
          result: null
        }
      };
    }
  }
  /**
   * 保持呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 保持结果
   */
  async holdCall(callId) {
    var _a, _b;
    const targetCallId = callId || this.currentCallId;
    try {
      const holdParams = {
        data: {
          messageId: "cmdHoldCall",
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", holdParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("call:held", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "呼叫已保持",
          data: {
            code: "succ",
            content: "呼叫已保持",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "保持失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "保持失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("保持异常", error);
      return {
        state: false,
        msg: error.message || "保持异常",
        data: {
          code: "error",
          content: error.message || "保持异常",
          result: null
        }
      };
    }
  }
  /**
   * 取回呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取回结果
   */
  async retrieveCall(callId) {
    var _a, _b;
    const targetCallId = callId || this.currentCallId;
    try {
      const retrieveParams = {
        data: {
          messageId: "cmdRetrieveCall",
          type: 11,
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", retrieveParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("call:retrieved", {
          callId: targetCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "呼叫已取回",
          data: {
            code: "succ",
            content: "呼叫已取回",
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "取回失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "取回失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("取回异常", error);
      return {
        state: false,
        msg: error.message || "取回异常",
        data: {
          code: "error",
          content: error.message || "取回异常",
          result: null
        }
      };
    }
  }
  /**
   * 获取当前呼叫ID
   * @returns 当前呼叫ID，如果没有则返回null
   */
  getCurrentCallId() {
    return this.currentCallId;
  }
  /**
   * 设置当前呼叫ID
   * @param callId 呼叫ID
   */
  setCurrentCallId(callId) {
    this.currentCallId = callId;
  }
  /**
   * 清空当前呼叫ID
   */
  clearCurrentCallId() {
    this.currentCallId = null;
  }
  /**
   * 挂断电话 - CCBarService 接口兼容方法
   * @param callId 呼叫ID，默认使用当前呼叫ID
   * @returns 操作结果
   */
  async clearCall(callData) {
    return this.hangupCall(callData);
  }
  /**
   * 恢复通话 - CCBarService 接口兼容方法
   * @param callId 呼叫ID，默认使用当前呼叫ID
   * @returns 操作结果
   */
  async unholdCall(callId) {
    return this.retrieveCall(callId);
  }
  /**
   * 获取当前呼叫信息 - CCBarService 接口兼容方法
   * @returns 呼叫信息
   */
  getCurrentCall() {
    const callId = this.getCurrentCallId();
    if (!callId)
      return null;
    return {
      callId,
      // 返回基本呼叫信息
      state: "active",
      // 可能的值: 'alerting', 'active', 'held', 'disconnected'
      timestamp: (/* @__PURE__ */ new Date()).getTime(),
      isIncoming: false
    };
  }
}
class TransferManager {
  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param agentId 座席ID
   */
  constructor(requestService, eventManager, agentId) {
    this.currentCallId = null;
    this.isTransferring = false;
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.agentId = agentId;
    this.eventManager.on("call:incoming", (data) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });
    this.eventManager.on("call:ended", () => {
      this.currentCallId = null;
      this.isTransferring = false;
    });
  }
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   * @deprecated sessionId在转接管理中不再使用，但为保持API兼容性而保留
   */
  updateSessionId(sessionId) {
    ccbarDebugger("TransferManager.updateSessionId被调用，但sessionId在转接功能中未使用");
  }
  /**
   * 更新当前呼叫ID
   * @param callId 呼叫ID
   */
  updateCurrentCallId(callId) {
    this.currentCallId = callId;
  }
  /**
   * 发起呼叫转接
   * @param params 转接参数
   * @returns 转接结果
   */
  async transferCall(params) {
    var _a, _b, _c, _d;
    if (!this.currentCallId) {
      return {
        state: false,
        msg: "没有可转接的呼叫",
        data: {
          code: "fail",
          content: "没有可转接的呼叫",
          result: null
        }
      };
    }
    if (this.isTransferring) {
      return {
        state: false,
        msg: "已有转接正在进行中",
        data: {
          code: "fail",
          content: "已有转接正在进行中",
          result: null
        }
      };
    }
    try {
      const transferParams = {
        data: {
          messageId: params.isDirectTransfer ? "cmdBlindTransfer" : "cmdSupervisedTransfer",
          agentId: this.agentId || localStorage.getItem("agentId"),
          callId: this.currentCallId,
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        }
      };
      if (params.targetType === "agent") {
        transferParams.cmdJson = JSON.stringify({
          ...JSON.parse(transferParams.cmdJson),
          targetAgentId: params.agentId,
          agentDN: params.agentPhone
        });
      } else if (params.targetType === "phone") {
        transferParams.cmdJson = JSON.stringify({
          ...JSON.parse(transferParams.cmdJson),
          phoneNumber: params.phoneNumber,
          callerId: params.displayNumber || ""
        });
      }
      const result = await this.requestService.post("/AgentEvent?action=event", transferParams);
      if (result.state && (((_a = result.data) == null ? void 0 : _a.code) === "succ" || ((_b = result.data) == null ? void 0 : _b.resultCode) === "000")) {
        this.isTransferring = true;
        this.eventManager.emit("call:transferring", {
          callId: this.currentCallId,
          targetType: params.targetType,
          targetId: params.targetType === "agent" ? params.agentId : params.phoneNumber,
          isDirectTransfer: params.isDirectTransfer,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "转接请求已发送",
          data: {
            code: "succ",
            content: "转接请求已发送",
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: ((_c = result.data) == null ? void 0 : _c.content) || "转接失败",
          data: {
            code: "fail",
            content: ((_d = result.data) == null ? void 0 : _d.content) || "转接失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("转接异常", error);
      return {
        state: false,
        msg: error.message || "转接异常",
        data: {
          code: "error",
          content: error.message || "转接异常",
          result: null
        }
      };
    }
  }
  /**
   * 完成转接
   * @returns 完成转接结果
   */
  async completeTransfer() {
    var _a, _b, _c, _d;
    if (!this.isTransferring) {
      return {
        state: false,
        msg: "没有正在进行的转接",
        data: {
          code: "fail",
          content: "没有正在进行的转接",
          result: null
        }
      };
    }
    try {
      const completeParams = {
        cmdJson: JSON.stringify({
          cmd: "completeTransfer",
          agentId: this.agentId,
          callId: this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        })
      };
      const result = await this.requestService.post("/AgentEvent?action=event", completeParams);
      if (result.state && (((_a = result.data) == null ? void 0 : _a.code) === "succ" || ((_b = result.data) == null ? void 0 : _b.resultCode) === "000")) {
        this.isTransferring = false;
        this.currentCallId = null;
        this.eventManager.emit("call:transferred", {
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "转接已完成",
          data: {
            code: "succ",
            content: "转接已完成",
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: ((_c = result.data) == null ? void 0 : _c.content) || "完成转接失败",
          data: {
            code: "fail",
            content: ((_d = result.data) == null ? void 0 : _d.content) || "完成转接失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("完成转接异常", error);
      return {
        state: false,
        msg: error.message || "完成转接异常",
        data: {
          code: "error",
          content: error.message || "完成转接异常",
          result: null
        }
      };
    }
  }
  /**
   * 取消转接
   * @returns 取消转接结果
   */
  async cancelTransfer() {
    var _a, _b, _c, _d;
    if (!this.isTransferring) {
      return {
        state: false,
        msg: "没有正在进行的转接",
        data: {
          code: "fail",
          content: "没有正在进行的转接",
          result: null
        }
      };
    }
    try {
      const cancelParams = {
        cmdJson: JSON.stringify({
          cmd: "cancelTransfer",
          agentId: this.agentId,
          callId: this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        })
      };
      const result = await this.requestService.post("/AgentEvent?action=event", cancelParams);
      if (result.state && (((_a = result.data) == null ? void 0 : _a.code) === "succ" || ((_b = result.data) == null ? void 0 : _b.resultCode) === "000")) {
        this.isTransferring = false;
        this.eventManager.emit("call:transfercancelled", {
          callId: this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "转接已取消",
          data: {
            code: "succ",
            content: "转接已取消",
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: ((_c = result.data) == null ? void 0 : _c.content) || "取消转接失败",
          data: {
            code: "fail",
            content: ((_d = result.data) == null ? void 0 : _d.content) || "取消转接失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("取消转接异常", error);
      return {
        state: false,
        msg: error.message || "取消转接异常",
        data: {
          code: "error",
          content: error.message || "取消转接异常",
          result: null
        }
      };
    }
  }
  /**
   * 获取转接状态
   * @returns 是否正在转接中
   */
  isInTransfer() {
    return this.isTransferring;
  }
}
class SupervisorManager {
  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param agentId 班长座席ID
   */
  constructor(requestService, eventManager, agentId) {
    this.currentCallId = null;
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.agentId = agentId;
    this.eventManager.on("call:eventSync", (data) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });
  }
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  updateSessionId(sessionId) {
    ccbarDebugger("SupervisorManager.updateSessionId被调用，但sessionId在班长功能中未使用");
  }
  /**
   * 开始监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 监听结果
   */
  async startMonitor(agentId, callId, options) {
    var _a, _b;
    try {
      const monitorParams = {
        data: {
          messageId: "cmdStartMonitorCall",
          agentId,
          // 被监听的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", monitorParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:monitorStarted", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "监听已开始",
          data: {
            code: "succ",
            content: "监听已开始",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "开始监听失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "开始监听失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("开始监听异常", error);
      return {
        state: false,
        msg: error.message || "开始监听失败",
        data: {
          code: "error",
          content: error.message || "开始监听失败",
          result: null
        }
      };
    }
  }
  /**
   * 结束监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async stopMonitor(agentId, callId, options) {
    var _a, _b;
    try {
      const stopMonitorParams = {
        data: {
          messageId: "cmdStopMonitorCall",
          agentId,
          // 被监听的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", stopMonitorParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:monitorStopped", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "监听已结束",
          data: {
            code: "succ",
            content: "监听已结束",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "结束监听失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "结束监听失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("结束监听异常", error);
      return {
        state: false,
        msg: error.message || "结束监听失败",
        data: {
          code: "error",
          content: error.message || "结束监听失败",
          result: null
        }
      };
    }
  }
  /**
  * 开始强拆
  * @param agentId 座席ID
  * @param callId 通话ID，不传则使用当前通话
  * @param options 附加选项
  * @returns 操作结果
  */
  async ClearForceCall(agentId, callId, options) {
    var _a, _b;
    try {
      const stopMonitorParams = {
        data: {
          messageId: "cmdClearForceCall",
          agentId,
          // 被监听的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", stopMonitorParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:clearForceCall", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强拆已开始",
          data: {
            code: "succ",
            content: "强拆已开始",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "强拆失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "强拆失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("强拆异常", error);
      return {
        state: false,
        msg: error.message || "强拆失败",
        data: {
          code: "error",
          content: error.message || "强拆失败",
          result: null
        }
      };
    }
  }
  /**
   * 开始强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async startIntercept(agentId, callId, options) {
    var _a, _b;
    try {
      const interceptParams = {
        data: {
          messageId: "cmdInterventCall",
          agentId,
          // 被强插的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", interceptParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:forceCallStarted", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强插已开始",
          data: {
            code: "succ",
            content: "强插已开始",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "开始强插失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "开始强插失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("开始强插异常", error);
      return {
        state: false,
        msg: error.message || "开始强插失败",
        data: {
          code: "error",
          content: error.message || "开始强插失败",
          result: null
        }
      };
    }
  }
  /**
   * 结束强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async stopIntercept(agentId, callId, options) {
    var _a, _b;
    try {
      const stopInterceptParams = {
        data: {
          messageId: "cmdStopInterventCall",
          agentId,
          // 被强插的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", stopInterceptParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:forceCallStopped", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强插已结束",
          data: {
            code: "succ",
            content: "强插已结束",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "结束强插失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "结束强插失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("结束强插异常", error);
      return {
        state: false,
        msg: error.message || "结束强插失败",
        data: {
          code: "error",
          content: error.message || "结束强插失败",
          result: null
        }
      };
    }
  }
  /**
   * 强制置忙
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  async forceBusy(agentId, reason, options) {
    var _a, _b;
    try {
      const forceBusyParams = {
        data: {
          messageId: "cmdChangeAgentStatus",
          agentId,
          // 被强制置忙的座席ID
          reason: reason || "主管强制置忙",
          actionType: "forcenotready",
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", forceBusyParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:forceBusy", {
          supervisorId: this.agentId,
          agentId,
          reason,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强制置忙已执行",
          data: {
            code: "succ",
            content: "强制置忙已执行",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "强制置忙失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "强制置忙失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("强制置忙异常", error);
      return {
        state: false,
        msg: error.message || "强制置忙失败",
        data: {
          code: "error",
          content: error.message || "强制置忙失败",
          result: null
        }
      };
    }
  }
  /**
   * 强制置闲
   * @param agentId 座席ID
   * @param options 附加选项
   * @returns 操作结果
   */
  async forceIdle(agentId, options) {
    var _a, _b;
    try {
      const forceIdleParams = {
        data: {
          messageId: "cmdChangeAgentStatus",
          agentId,
          // 被强制置闲的座席ID
          cuid: localStorage.getItem("cuid") || "",
          actionType: "forceready",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", forceIdleParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      console.log("强制置闲请求结果", result, "ccbar");
      if (result.state) {
        this.eventManager.emit("supervisor:forceIdle", {
          supervisorId: this.agentId,
          agentId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强制置闲已执行",
          data: {
            code: "succ",
            content: "强制置闲已执行",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "强制置闲失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "强制置闲失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("强制置闲异常", error);
      return {
        state: false,
        msg: error.message || "强制置闲失败",
        data: {
          code: "error",
          content: error.message || "强制置闲失败",
          result: null
        }
      };
    }
  }
  /**
   * 开始密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async startWhisper(agentId, callId, options) {
    var _a, _b;
    try {
      const whisperParams = {
        data: {
          messageId: "cmdSecretlyTalk",
          agentId,
          // 被密语的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", whisperParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:whisperStarted", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "密语已开始",
          data: {
            code: "succ",
            content: "密语已开始",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "开始密语失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "开始密语失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("开始密语异常", error);
      return {
        state: false,
        msg: error.message || "开始密语失败",
        data: {
          code: "error",
          content: error.message || "开始密语失败",
          result: null
        }
      };
    }
  }
  /**
   * 结束密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async stopWhisper(agentId, callId, options) {
    var _a, _b;
    try {
      const stopWhisperParams = {
        data: {
          messageId: "cmdStopSecretlyTalk",
          agentId,
          // 被密语的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", stopWhisperParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:whisperStopped", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "密语已结束",
          data: {
            code: "succ",
            content: "密语已结束",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "结束密语失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "结束密语失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("结束密语异常", error);
      return {
        state: false,
        msg: error.message || "结束密语失败",
        data: {
          code: "error",
          content: error.message || "结束密语失败",
          result: null
        }
      };
    }
  }
  /**
   * 拦截通话
   * @param agentId 要拦截的座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  async interceptCall(agentId, callId, options) {
    var _a, _b;
    try {
      const interceptParams = {
        data: {
          messageId: "cmdIntercept",
          agentId,
          // 被强制置闲的座席ID
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", interceptParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:callIntercepted", {
          supervisorId: this.agentId,
          agentId,
          callId: callId || this.currentCallId,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "通话已拦截",
          data: {
            code: "succ",
            content: "通话已拦截",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "拦截通话失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "拦截通话失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("拦截通话异常", error);
      return {
        state: false,
        msg: error.message || "拦截通话失败",
        data: {
          code: "error",
          content: error.message || "拦截通话失败",
          result: null
        }
      };
    }
  }
  /**
   * 强制签出
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  async forceLogout(agentId, reason, options) {
    var _a, _b;
    try {
      const forceLogoutParams = {
        data: {
          messageId: "cmdChangeAgentStatus",
          agentId,
          // 被强制签出的座席ID
          reason: reason || "主管强制签出",
          actionType: "forcelogoff",
          cuid: localStorage.getItem("cuid") || "",
          timestamp: (/* @__PURE__ */ new Date()).getTime(),
          ...options || {}
        }
      };
      const result = await this.requestService.post("/AgentEvent?action=event", forceLogoutParams, {
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      });
      if (result.state) {
        this.eventManager.emit("supervisor:forceLogout", {
          supervisorId: this.agentId,
          agentId,
          reason,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: true,
          msg: "强制签出已执行",
          data: {
            code: "succ",
            content: "强制签出已执行",
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: ((_a = result.data) == null ? void 0 : _a.content) || "强制签出失败",
          data: {
            code: "fail",
            content: ((_b = result.data) == null ? void 0 : _b.content) || "强制签出失败",
            result: null
          }
        };
      }
    } catch (error) {
      ccbarDebugger("强制签出异常", error);
      return {
        state: false,
        msg: error.message || "强制签出失败",
        data: {
          code: "error",
          content: error.message || "强制签出失败",
          result: null
        }
      };
    }
  }
}
class CCBarService {
  /**
   * 私有构造函数，防止外部直接实例化
   * @param config 配置参数
   */
  constructor(config) {
    this.config = config;
    this.globalStateManager = GlobalStateManager.getInstance();
    this.initializeRealModules(config);
    this.setupGlobalStateEventListeners();
  }
  /**
   * 初始化真实模块
   * @param config 配置参数
   */
  initializeRealModules(config) {
    console.log("[CCBarService] 初始化模块，配置:", config);
    const defaultConfig2 = {
      baseURL: "http://localhost:8080",
      wsURL: "ws://localhost:8081",
      ...config
    };
    const eventEmitter = EventEmitter.getInstance();
    const requestService = new RequestService(
      defaultConfig2.timeout || 1e4,
      true
    );
    this.eventManager = new EventManager(eventEmitter);
    console.log("[CCBarService] 事件管理器已创建:", this.eventManager);
    const shouldDisableWS = defaultConfig2.serviceType === "polling" || defaultConfig2.disableWebSocket === true || !defaultConfig2.wsURL || defaultConfig2.wsURL === "";
    console.log("[CCBarService] WebSocket禁用检查:", {
      serviceType: defaultConfig2.serviceType,
      disableWebSocket: defaultConfig2.disableWebSocket,
      wsURL: defaultConfig2.wsURL,
      shouldDisableWS
    });
    let wsService = null;
    if (!shouldDisableWS) {
      console.log("[CCBarService] 创建WebSocket服务");
      wsService = new WebSocketService(
        defaultConfig2.wsURL || "",
        {
          reconnectInterval: defaultConfig2.reconnectInterval,
          maxReconnectAttempts: defaultConfig2.maxReconnectAttempts,
          pingInterval: defaultConfig2.heartbeatInterval
        }
      );
    } else {
      console.log("[CCBarService] 跳过WebSocket服务创建 - 使用轮询模式");
    }
    this.connectionManager = new ConnectionManager(
      requestService,
      wsService,
      this.eventManager,
      defaultConfig2
    );
    this.stateManager = new StateManager(
      requestService,
      this.eventManager,
      this.globalStateManager
    );
    if (!this.eventManager) {
      throw new Error("[CCBarService] EventManager未正确初始化");
    }
    this.callManager = new CallManager(
      requestService,
      this.eventManager,
      "",
      ""
    );
    this.transferManager = new TransferManager(
      requestService,
      this.eventManager,
      ""
    );
    this.supervisorManager = new SupervisorManager(
      requestService,
      this.eventManager,
      ""
    );
    console.log("[CCBarService] 所有模块初始化完成");
  }
  /**
   * 设置全局状态管理器的事件监听
   */
  setupGlobalStateEventListeners() {
    this.eventManager.on("agent:stateChanged", (data) => {
      this.globalStateManager.setState(data.state);
    });
    this.eventManager.on("call:incoming", (data) => {
      this.globalStateManager.updateCallInfo(data, true);
    });
    this.eventManager.on("call:established", (data) => {
      this.globalStateManager.updateCallInfo(data, true);
    });
    this.eventManager.on("call:ended", () => {
      this.globalStateManager.updateCallInfo(null, true);
    });
  }
  /**
   * 获取CCBarService单例实例
   * @param config 配置参数
   * @returns CCBarService实例
   */
  static getInstance(config) {
    const initializedConfig = initializeWithConfig(config);
    if (!CCBarService.instance) {
      console.log("[CCBarService] 创建实例，使用配置:", initializedConfig);
      CCBarService.instance = new CCBarService(initializedConfig);
    } else if (config) {
      console.log("[CCBarService] 更新配置:", initializedConfig);
      CCBarService.instance.updateConfig(initializedConfig);
    }
    return CCBarService.instance;
  }
  /**
   * 更新配置
   * @param config 新的配置参数
   */
  updateConfig(config) {
    console.log("[CCBarService] 配置更新前:", this.config);
    this.config = {
      ...this.config,
      ...config
    };
    console.log("[CCBarService] 配置更新后:", this.config);
    if (this.connectionManager) {
      this.eventManager.emit("system:configUpdated", {
        config: this.config,
        timestamp: Date.now()
      });
    }
  }
  /* === 连接管理功能 === */
  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果isLoggedIn
   */
  async login(params) {
    ccbarDebugger("login");
    const loginResult = await this.connectionManager.login(params);
    if (loginResult.state) {
      this.globalStateManager.setPhone(params.phone || "");
    }
    return loginResult;
  }
  /**
   * 座席登出
   * @returns 登出结果
   */
  async logout() {
    ccbarDebugger("logout");
    return this.connectionManager.logout();
  }
  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isLoggedIn() {
    return sessionStorage.getItem("isLogined") === "true" || this.connectionManager.isLoggedIn();
  }
  /**
   * 获取登录状态信息
   * @returns 登录状态详情
   */
  getLoginStatus() {
    const agentInfo = this.connectionManager.getAgentInfo();
    return {
      isLoggedIn: this.connectionManager.isLoggedIn(),
      agentId: agentInfo.agentId || "",
      sessionId: this.connectionManager.getSessionId()
    };
  }
  /* === 状态管理功能 === */
  /**
   * 座席置闲
   * @returns 操作结果
   */
  async agentReady() {
    ccbarDebugger("agentReady");
    return this.stateManager.setAgentReady();
  }
  /**
   * 座席置忙
   * @param busyType 忙碌类型
   * @returns 操作结果
   */
  async agentNotReady(busyType) {
    ccbarDebugger("agentNotReady");
    return this.stateManager.setAgentNotReady(busyType);
  }
  /**
   * 设置话后整理状态
   * @returns 操作结果
   */
  async workNotReady() {
    ccbarDebugger("workNotReady");
    return this.stateManager.setWorkNotReady();
  }
  /**
   * 完成话后整理
   * @returns 操作结果
   */
  async workReady() {
    ccbarDebugger("workReady");
    return this.stateManager.setWorkReady();
  }
  /**
   * 设置工作模式
   * @param mode 工作模式
   * @returns 操作结果
   */
  async setWorkMode(mode) {
    ccbarDebugger("setWorkMode");
    this.globalStateManager.updateWorkMode(mode);
    return this.stateManager.setWorkMode(mode);
  }
  /**
   * 获取功能状态
   * @param func 功能名称
   * @returns 功能是否可用
   */
  getFuncMask(func) {
    return this.globalStateManager.isFuncEnabled(func);
  }
  /**
   * 获取座席状态
   * @returns 座席状态
   */
  getState() {
    return this.globalStateManager.getStateType();
  }
  /**
   * 获取座席信息
   * @returns 座席信息
   */
  getAgentInfo() {
    const agentInfo = this.connectionManager.getAgentInfo();
    const globalState = this.globalStateManager.getState();
    const phone = this.globalStateManager.getPhone();
    return {
      ...agentInfo,
      state: globalState.state,
      stateDesc: globalState.stateDesc,
      funcMask: globalState.funcMask,
      phone: phone || agentInfo.phone || ""
      // 确保包含话机信息
    };
  }
  /**
   * 获取完整的座席状态
   * @returns 完整的座席状态对象
   */
  getAgentState() {
    return this.globalStateManager.getState();
  }
  /* === 呼叫管理功能 === */
  /**
   * 拨打电话
   * @param phoneNumber 电话号码
   * @param displayNumber 外显号码
   * @param userData 用户数据
   * @param callType 呼叫类型
   * @returns 操作结果
   */
  async makeCall(phoneNumber, displayNumber, userData = {}, callType = 2) {
    ccbarDebugger("makeCall", { phoneNumber, displayNumber, userData, callType });
    return this.callManager.makeCall(phoneNumber, displayNumber, userData, callType);
  }
  /**
   * 应答呼叫
   * @returns 操作结果
   */
  async answerCall() {
    ccbarDebugger("answerCall");
    return this.callManager.answerCall();
  }
  /**
   * 挂断呼叫
   * @returns 操作结果
   */
  async clearCall(callData) {
    ccbarDebugger("clearCall");
    return this.callManager.hangupCall(callData);
  }
  /**
   * 保持呼叫
   * @returns 操作结果
   */
  async holdCall() {
    ccbarDebugger("holdCall");
    return this.callManager.holdCall();
  }
  /**
   * 取回呼叫
   * @returns 操作结果
   */
  async unholdCall() {
    ccbarDebugger("unholdCall");
    return this.callManager.retrieveCall();
  }
  /**
   * 静音呼叫
   * @returns 操作结果
   */
  async muteCall() {
    ccbarDebugger("muteCall");
    if (typeof this.callManager.muteCall === "function") {
      return this.callManager.muteCall();
    }
    return {
      state: false,
      msg: "操作不支持",
      data: {
        code: "fail",
        content: "当前呼叫管理器不支持静音操作"
      }
    };
  }
  /**
   * 取消静音呼叫
   * @returns 操作结果
   */
  async unmuteCall() {
    ccbarDebugger("unmuteCall");
    if (typeof this.callManager.unmuteCall === "function") {
      return this.callManager.unmuteCall();
    }
    return {
      state: false,
      msg: "操作不支持",
      data: {
        code: "fail",
        content: "当前呼叫管理器不支持取消静音操作"
      }
    };
  }
  /* === 转接管理功能 === */
  /**
   * 转接通话
   * @param params 转接参数
   * @returns 操作结果
   */
  async transferCall(params) {
    ccbarDebugger("transferCall");
    return this.transferManager.transferCall(params);
  }
  /**
   * 完成转接
   * @returns 操作结果
   */
  async completeTransfer() {
    ccbarDebugger("completeTransfer");
    return this.transferManager.completeTransfer();
  }
  /**
   * 取消转接
   * @returns 操作结果
   */
  async cancelTransfer() {
    ccbarDebugger("cancelTransfer");
    return this.transferManager.cancelTransfer();
  }
  /* === 事件管理功能 === */
  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on(eventName, callback) {
    this.eventManager.on(eventName, callback);
  }
  /**
   * 取消订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  off(eventName, callback) {
    this.eventManager.off(eventName, callback);
  }
  /**
   * 一次性订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  once(eventName, callback) {
    this.eventManager.once(eventName, callback);
  }
  /**
   * 触发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  emit(eventName, data) {
    this.eventManager.emit(eventName, data);
  }
  /**
   * 停止轮询服务
   * 一般在会话超时或出现严重错误时调用
   */
  stopPolling() {
    ccbarDebugger("stopPolling 被调用，尝试停止轮询服务");
    try {
      if (this.connectionManager) {
        this.eventManager.emit("system:stopPolling", {
          reason: "Manually stopped due to error",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        ccbarDebugger("已触发停止轮询事件");
      }
    } catch (error) {
      ccbarDebugger("停止轮询失败", error, "error");
    }
  }
  /**
   * 初始化系统
   * @returns 初始化结果
   */
  async init() {
    ccbarDebugger("init");
    try {
      return this.connectionManager.initCCbar();
    } catch (error) {
      ccbarDebugger("初始化失败", error, "error");
      return {
        state: false,
        msg: error.message || "初始化失败",
        data: {
          code: "-1",
          content: error.message || "初始化失败"
        }
      };
    }
  }
  /**
   * 更新会话信息
   * @param sessionId 会话ID
   * @param agentId 座席ID
   */
  updateSessionInfo(sessionId, agentId) {
    ccbarDebugger("updateSessionInfo", { sessionId, agentId });
    if (this.callManager && typeof this.callManager.updateSessionId === "function") {
      this.callManager.updateSessionId(sessionId);
    }
    if (this.transferManager && typeof this.transferManager.updateSessionId === "function") {
      this.transferManager.updateSessionId(sessionId);
    }
    if (this.supervisorManager && agentId) {
      this.supervisorManager.updateSessionId("");
    }
  }
  /**
   * 获取班长操作管理器
   * @returns 班长操作管理器实例
   */
  get supervisor() {
    return this.supervisorManager;
  }
  /**
   * 调用测试方法
   */
  test(params = {}) {
    console.log("[测试]", params);
    return {
      result: true,
      message: "Test successful",
      data: params
    };
  }
  /**
   * 重置登录状态（不调用服务器接口）
   */
  resetLoginState() {
    console.log("[CCBarService] 重置登录状态");
    try {
      this.globalStateManager.resetState();
      if (this.pollingService) {
        this.pollingService.stopPolling();
        this.pollingService.setLoginState(false);
        this.pollingService.setSessionId(null);
        this.pollingService.setAgentId(null);
      }
      if (this.connectionManager) {
        this.connectionManager.setLoginState(false);
      }
      this.sessionId = "";
      this.isLogined = false;
      localStorage.removeItem("sessionId");
      localStorage.removeItem("agentId");
      localStorage.removeItem("cuid");
      sessionStorage.setItem("isLogined", "false");
      this.eventManager.emit("agent:forceLogout", {
        type: "forceLogout",
        reason: "会话状态已重置",
        timestamp: Date.now()
      });
      console.log("[CCBarService] 登录状态重置完成");
    } catch (error) {
      console.error("[CCBarService] 重置登录状态失败:", error);
    }
  }
}
/*! Element Plus Icons Vue v2.3.1 */
var close_vue_vue_type_script_setup_true_lang_default = /* @__PURE__ */ defineComponent({
  name: "Close",
  __name: "close",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
      })
    ]));
  }
});
var close_default = close_vue_vue_type_script_setup_true_lang_default;
var phone_filled_vue_vue_type_script_setup_true_lang_default = /* @__PURE__ */ defineComponent({
  name: "PhoneFilled",
  __name: "phone-filled",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M199.232 125.568 90.624 379.008a32 32 0 0 0 6.784 35.2l512.384 512.384a32 32 0 0 0 35.2 6.784l253.44-108.608a32 32 0 0 0 10.048-52.032L769.6 633.92a32 32 0 0 0-36.928-5.952l-130.176 65.088-271.488-271.552 65.024-130.176a32 32 0 0 0-5.952-36.928L251.2 115.52a32 32 0 0 0-51.968 10.048z"
      })
    ]));
  }
});
var phone_filled_default = phone_filled_vue_vue_type_script_setup_true_lang_default;
var phone_vue_vue_type_script_setup_true_lang_default = /* @__PURE__ */ defineComponent({
  name: "Phone",
  __name: "phone",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M79.36 432.256 591.744 944.64a32 32 0 0 0 35.2 6.784l253.44-108.544a32 32 0 0 0 9.984-52.032l-153.856-153.92a32 32 0 0 0-36.928-6.016l-69.888 34.944L358.08 394.24l35.008-69.888a32 32 0 0 0-5.952-36.928L233.152 133.568a32 32 0 0 0-52.032 10.048L72.512 397.056a32 32 0 0 0 6.784 35.2zm60.48-29.952 81.536-190.08L325.568 316.48l-24.64 49.216-20.608 41.216 32.576 32.64 271.552 271.552 32.64 32.64 41.216-20.672 49.28-24.576 104.192 104.128-190.08 81.472L139.84 402.304zM512 320v-64a256 256 0 0 1 256 256h-64a192 192 0 0 0-192-192m0-192V64a448 448 0 0 1 448 448h-64a384 384 0 0 0-384-384"
      })
    ]));
  }
});
var phone_default = phone_vue_vue_type_script_setup_true_lang_default;
var question_filled_vue_vue_type_script_setup_true_lang_default = /* @__PURE__ */ defineComponent({
  name: "QuestionFilled",
  __name: "question-filled",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592 0-42.944-14.08-76.736-42.24-101.376-28.16-25.344-65.472-37.312-111.232-37.312zm-12.672 406.208a54.272 54.272 0 0 0-38.72 14.784 49.408 49.408 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.848 54.848 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.968 51.968 0 0 0-15.488-38.016 55.936 55.936 0 0 0-39.424-14.784z"
      })
    ]));
  }
});
var question_filled_default = question_filled_vue_vue_type_script_setup_true_lang_default;
const _sfc_main$4 = defineComponent({
  name: "transfer-call-dialog",
  components: {
    QuestionFilled: question_filled_default
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    currentCall: {
      type: Object,
      default: null
    },
    showDisplayNumber: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue", "transfer"],
  setup(props, { emit }) {
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const activeTab = ref("agent");
    const transferLoading = ref(false);
    const transferForm = reactive({
      agentId: "",
      agentPhone: "",
      phoneNumber: "",
      displayNumber: "",
      isDirectTransfer: true
    });
    const commonAgents = ref([
      { id: 1, name: "技术支持", phone: "8002" },
      { id: 2, name: "售后服务", phone: "8003" },
      { id: 3, name: "投诉处理", phone: "8004" },
      { id: 4, name: "销售顾问", phone: "8005" },
      { id: 5, name: "团队主管", phone: "8006" },
      { id: 6, name: "客户经理", phone: "8007" }
    ]);
    const commonNumbers = ref([
      { id: 1, name: "技术部", phone: "01012345678" },
      { id: 2, name: "销售部", phone: "01012345679" },
      { id: 3, name: "客服部", phone: "01012345680" },
      { id: 4, name: "投诉热线", phone: "01012345681" },
      { id: 5, name: "紧急联系人", phone: "01012345682" },
      { id: 6, name: "前台", phone: "01012345683" }
    ]);
    const selectAgent = (agent) => {
      transferForm.agentId = agent.name;
      transferForm.agentPhone = agent.phone;
    };
    const selectNumber = (number) => {
      transferForm.phoneNumber = number.phone;
    };
    const handleTransfer = async () => {
      transferLoading.value = true;
      try {
        const transferTarget = activeTab.value === "agent" ? { agentId: transferForm.agentId, agentPhone: transferForm.agentPhone } : { phoneNumber: transferForm.phoneNumber, displayNumber: transferForm.displayNumber };
        emit("transfer", {
          ...transferTarget,
          isDirectTransfer: transferForm.isDirectTransfer
        });
        dialogVisible.value = false;
      } finally {
        transferLoading.value = false;
      }
    };
    const handleClosed = () => {
      transferForm.agentId = "";
      transferForm.agentPhone = "";
      transferForm.phoneNumber = "";
      transferForm.displayNumber = "";
      transferForm.isDirectTransfer = true;
      activeTab.value = "agent";
    };
    return {
      dialogVisible,
      activeTab,
      transferForm,
      transferLoading,
      commonAgents,
      commonNumbers,
      selectAgent,
      selectNumber,
      handleTransfer,
      handleClosed,
      showDisplayNumber: props.showDisplayNumber
    };
  }
});
const TransferCallDialog_vue_vue_type_style_index_0_scoped_6a84231d_lang = "";
const _export_sfc = (sfc, props) => {
  const target = sfc.__vccOpts || sfc;
  for (const [key, val] of props) {
    target[key] = val;
  }
  return target;
};
const _hoisted_1$4 = { class: "transfer-container" };
const _hoisted_2$3 = {
  key: 0,
  class: "current-call-info"
};
const _hoisted_3$3 = { class: "info-item" };
const _hoisted_4$3 = { class: "value" };
const _hoisted_5$3 = { class: "common-targets" };
const _hoisted_6$3 = { class: "common-targets" };
const _hoisted_7$3 = { class: "transfer-options" };
const _hoisted_8$3 = { class: "dialog-footer" };
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_el_input = resolveComponent("el-input");
  const _component_el_form_item = resolveComponent("el-form-item");
  const _component_el_form = resolveComponent("el-form");
  const _component_el_button = resolveComponent("el-button");
  const _component_el_col = resolveComponent("el-col");
  const _component_el_row = resolveComponent("el-row");
  const _component_el_tab_pane = resolveComponent("el-tab-pane");
  const _component_el_tabs = resolveComponent("el-tabs");
  const _component_el_checkbox = resolveComponent("el-checkbox");
  const _component_question_filled = resolveComponent("question-filled");
  const _component_el_icon = resolveComponent("el-icon");
  const _component_el_tooltip = resolveComponent("el-tooltip");
  const _component_el_dialog = resolveComponent("el-dialog");
  return openBlock(), createBlock(_component_el_dialog, {
    title: "转接通话",
    modelValue: _ctx.dialogVisible,
    "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => _ctx.dialogVisible = $event),
    width: "400px",
    "close-on-click-modal": false,
    onClosed: _ctx.handleClosed
  }, {
    footer: withCtx(() => [
      createElementVNode("span", _hoisted_8$3, [
        createVNode(_component_el_button, {
          onClick: _cache[6] || (_cache[6] = ($event) => _ctx.dialogVisible = false)
        }, {
          default: withCtx(() => _cache[12] || (_cache[12] = [
            createTextVNode("取消")
          ])),
          _: 1
        }),
        createVNode(_component_el_button, {
          type: "primary",
          onClick: _ctx.handleTransfer,
          loading: _ctx.transferLoading
        }, {
          default: withCtx(() => [
            createTextVNode(toDisplayString(_ctx.transferForm.isDirectTransfer ? "直接转接" : "咨询转接"), 1)
          ]),
          _: 1
        }, 8, ["onClick", "loading"])
      ])
    ]),
    default: withCtx(() => [
      createElementVNode("div", _hoisted_1$4, [
        _ctx.currentCall ? (openBlock(), createElementBlock("div", _hoisted_2$3, [
          createElementVNode("div", _hoisted_3$3, [
            _cache[8] || (_cache[8] = createElementVNode("span", { class: "label" }, "当前通话:", -1)),
            createElementVNode("span", _hoisted_4$3, toDisplayString(_ctx.currentCall.displayCustPhone), 1)
          ])
        ])) : createCommentVNode("", true),
        createVNode(_component_el_tabs, {
          modelValue: _ctx.activeTab,
          "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => _ctx.activeTab = $event),
          class: "transfer-tabs"
        }, {
          default: withCtx(() => [
            createVNode(_component_el_tab_pane, {
              label: "转接坐席",
              name: "agent"
            }, {
              default: withCtx(() => [
                createVNode(_component_el_form, {
                  class: "transfer-form",
                  onSubmit: withModifiers(_ctx.handleTransfer, ["prevent"])
                }, {
                  default: withCtx(() => [
                    createVNode(_component_el_form_item, { label: "坐席ID" }, {
                      default: withCtx(() => [
                        createVNode(_component_el_input, {
                          modelValue: _ctx.transferForm.agentId,
                          "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.transferForm.agentId = $event),
                          placeholder: "请输入坐席ID"
                        }, null, 8, ["modelValue"])
                      ]),
                      _: 1
                    }),
                    createVNode(_component_el_form_item, { label: "坐席分机" }, {
                      default: withCtx(() => [
                        createVNode(_component_el_input, {
                          modelValue: _ctx.transferForm.agentPhone,
                          "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.transferForm.agentPhone = $event),
                          placeholder: "请输入坐席分机"
                        }, null, 8, ["modelValue"])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["onSubmit"]),
                createElementVNode("div", _hoisted_5$3, [
                  _cache[9] || (_cache[9] = createElementVNode("p", { class: "subtitle" }, "常用坐席", -1)),
                  createVNode(_component_el_row, { gutter: 10 }, {
                    default: withCtx(() => [
                      (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.commonAgents, (agent) => {
                        return openBlock(), createBlock(_component_el_col, {
                          span: 8,
                          key: agent.id
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_el_button, {
                              size: "small",
                              onClick: ($event) => _ctx.selectAgent(agent)
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(agent.name) + " (" + toDisplayString(agent.phone) + ") ", 1)
                              ]),
                              _: 2
                            }, 1032, ["onClick"])
                          ]),
                          _: 2
                        }, 1024);
                      }), 128))
                    ]),
                    _: 1
                  })
                ])
              ]),
              _: 1
            }),
            createVNode(_component_el_tab_pane, {
              label: "转接号码",
              name: "number"
            }, {
              default: withCtx(() => [
                createVNode(_component_el_form, {
                  class: "transfer-form",
                  onSubmit: withModifiers(_ctx.handleTransfer, ["prevent"])
                }, {
                  default: withCtx(() => [
                    createVNode(_component_el_form_item, { label: "电话号码" }, {
                      default: withCtx(() => [
                        createVNode(_component_el_input, {
                          modelValue: _ctx.transferForm.phoneNumber,
                          "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => _ctx.transferForm.phoneNumber = $event),
                          placeholder: "请输入转接号码"
                        }, null, 8, ["modelValue"])
                      ]),
                      _: 1
                    }),
                    _ctx.showDisplayNumber ? (openBlock(), createBlock(_component_el_form_item, {
                      key: 0,
                      label: "外显号码"
                    }, {
                      default: withCtx(() => [
                        createVNode(_component_el_input, {
                          modelValue: _ctx.transferForm.displayNumber,
                          "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => _ctx.transferForm.displayNumber = $event),
                          placeholder: "请输入外显号码"
                        }, null, 8, ["modelValue"])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true)
                  ]),
                  _: 1
                }, 8, ["onSubmit"]),
                createElementVNode("div", _hoisted_6$3, [
                  _cache[10] || (_cache[10] = createElementVNode("p", { class: "subtitle" }, "常用号码", -1)),
                  createVNode(_component_el_row, { gutter: 10 }, {
                    default: withCtx(() => [
                      (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.commonNumbers, (number) => {
                        return openBlock(), createBlock(_component_el_col, {
                          span: 8,
                          key: number.id
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_el_button, {
                              size: "small",
                              onClick: ($event) => _ctx.selectNumber(number)
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(number.name), 1)
                              ]),
                              _: 2
                            }, 1032, ["onClick"])
                          ]),
                          _: 2
                        }, 1024);
                      }), 128))
                    ]),
                    _: 1
                  })
                ])
              ]),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["modelValue"]),
        createElementVNode("div", _hoisted_7$3, [
          createVNode(_component_el_checkbox, {
            modelValue: _ctx.transferForm.isDirectTransfer,
            "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => _ctx.transferForm.isDirectTransfer = $event)
          }, {
            default: withCtx(() => _cache[11] || (_cache[11] = [
              createTextVNode("直接转接")
            ])),
            _: 1
          }, 8, ["modelValue"]),
          createVNode(_component_el_tooltip, {
            content: "直接转接: 不需要等待第三方应答，立即转接通话\n咨询转接: 与第三方通话后，再决定是否转接",
            placement: "top"
          }, {
            default: withCtx(() => [
              createVNode(_component_el_icon, { class: "help-icon" }, {
                default: withCtx(() => [
                  createVNode(_component_question_filled)
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ])
      ])
    ]),
    _: 1
  }, 8, ["modelValue", "onClosed"]);
}
const TransferCallDialog = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$1], ["__scopeId", "data-v-6a84231d"]]);
const _sfc_main$3 = defineComponent({
  name: "incoming-call-alert",
  components: {
    PhoneFilled: phone_filled_default,
    Phone: phone_default,
    Close: close_default
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    callInfo: {
      type: Object,
      default: () => ({
        phoneNumber: "",
        customerName: "",
        time: "",
        notes: ""
      })
    }
  },
  emits: ["answer", "reject"],
  setup(props, { emit }) {
    const ringTimer = ref(0);
    const timerInterval = ref(null);
    const alertAudio = ref(null);
    const timerDisplay = computed(() => {
      const minutes = Math.floor(ringTimer.value / 60);
      const seconds = ringTimer.value % 60;
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    });
    const handleAnswer = () => {
      stopTimer();
      emit("answer");
    };
    const handleReject = () => {
      stopTimer();
      emit("reject");
    };
    const startTimer = () => {
      ringTimer.value = 0;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
      timerInterval.value = setInterval(() => {
        ringTimer.value++;
      }, 1e3);
    };
    const stopTimer = () => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
        timerInterval.value = null;
      }
      if (alertAudio.value) {
        alertAudio.value.pause();
        alertAudio.value = null;
      }
    };
    watch(() => props.show, (newValue) => {
      if (newValue) {
        startTimer();
        try {
          alertAudio.value = new Audio();
          alertAudio.value.src = "data:audio/mp3;base64,SUQzAwAAAAAfdlRJVDIAAAAoAAAB//5TAHUAbgBuAHkAIABtAG8AcgBuAGkAbgBnAFQQQUcAAAATAAAA//9SAGkAbgBnAHQAbwBuAGUAVFBFMQAAABUAAAH//5EAaQBuAGcAdABvAG4AZQBz";
          alertAudio.value.loop = true;
          alertAudio.value.play().catch((e) => console.warn("无法播放提示音:", e));
        } catch (e) {
          console.error("音频播放失败", e);
        }
      } else {
        stopTimer();
      }
    });
    onMounted(() => {
      if (props.show) {
        startTimer();
      }
    });
    onUnmounted(() => {
      stopTimer();
    });
    return {
      timerDisplay,
      handleAnswer,
      handleReject
    };
  }
});
const IncomingCallAlert_vue_vue_type_style_index_0_scoped_3d7e27c4_lang = "";
const _hoisted_1$3 = {
  key: 0,
  class: "call-alert-container"
};
const _hoisted_2$2 = { class: "call-alert-header" };
const _hoisted_3$2 = { class: "call-alert-title" };
const _hoisted_4$2 = { class: "call-alert-timer" };
const _hoisted_5$2 = { class: "call-alert-body" };
const _hoisted_6$2 = { class: "call-info" };
const _hoisted_7$2 = { class: "phone-number" };
const _hoisted_8$2 = { class: "value" };
const _hoisted_9$2 = {
  key: 0,
  class: "customer-info"
};
const _hoisted_10$2 = { class: "value" };
const _hoisted_11$2 = { class: "call-time" };
const _hoisted_12$2 = { class: "value" };
const _hoisted_13$1 = {
  key: 1,
  class: "call-notes"
};
const _hoisted_14$1 = { class: "value" };
const _hoisted_15$1 = { class: "call-alert-footer" };
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_phone_filled = resolveComponent("phone-filled");
  const _component_el_icon = resolveComponent("el-icon");
  const _component_phone = resolveComponent("phone");
  const _component_el_button = resolveComponent("el-button");
  const _component_close = resolveComponent("close");
  return _ctx.show ? (openBlock(), createElementBlock("div", _hoisted_1$3, [
    createElementVNode("div", {
      class: normalizeClass(["call-alert-box", { "flashing-border": _ctx.show }])
    }, [
      createElementVNode("div", _hoisted_2$2, [
        createElementVNode("div", _hoisted_3$2, [
          createVNode(_component_el_icon, null, {
            default: withCtx(() => [
              createVNode(_component_phone_filled)
            ]),
            _: 1
          }),
          _cache[0] || (_cache[0] = createElementVNode("span", null, "来电提醒", -1))
        ]),
        createElementVNode("div", _hoisted_4$2, toDisplayString(_ctx.timerDisplay), 1)
      ]),
      createElementVNode("div", _hoisted_5$2, [
        createElementVNode("div", _hoisted_6$2, [
          createElementVNode("div", _hoisted_7$2, [
            _cache[1] || (_cache[1] = createElementVNode("div", { class: "label" }, "来电号码:", -1)),
            createElementVNode("div", _hoisted_8$2, toDisplayString(_ctx.callInfo.phoneNumber), 1)
          ]),
          _ctx.callInfo.customerName ? (openBlock(), createElementBlock("div", _hoisted_9$2, [
            _cache[2] || (_cache[2] = createElementVNode("div", { class: "label" }, "客户姓名:", -1)),
            createElementVNode("div", _hoisted_10$2, toDisplayString(_ctx.callInfo.customerName), 1)
          ])) : createCommentVNode("", true),
          createElementVNode("div", _hoisted_11$2, [
            _cache[3] || (_cache[3] = createElementVNode("div", { class: "label" }, "呼入时间:", -1)),
            createElementVNode("div", _hoisted_12$2, toDisplayString(_ctx.callInfo.time), 1)
          ]),
          _ctx.callInfo.notes ? (openBlock(), createElementBlock("div", _hoisted_13$1, [
            _cache[4] || (_cache[4] = createElementVNode("div", { class: "label" }, "备注信息:", -1)),
            createElementVNode("div", _hoisted_14$1, toDisplayString(_ctx.callInfo.notes), 1)
          ])) : createCommentVNode("", true)
        ])
      ]),
      createElementVNode("div", _hoisted_15$1, [
        createVNode(_component_el_button, {
          type: "primary",
          onClick: _ctx.handleAnswer
        }, {
          default: withCtx(() => [
            createVNode(_component_el_icon, null, {
              default: withCtx(() => [
                createVNode(_component_phone)
              ]),
              _: 1
            }),
            _cache[5] || (_cache[5] = createTextVNode(" 立即接听 "))
          ]),
          _: 1
        }, 8, ["onClick"]),
        createVNode(_component_el_button, { onClick: _ctx.handleReject }, {
          default: withCtx(() => [
            createVNode(_component_el_icon, null, {
              default: withCtx(() => [
                createVNode(_component_close)
              ]),
              _: 1
            }),
            _cache[6] || (_cache[6] = createTextVNode(" 拒接来电 "))
          ]),
          _: 1
        }, 8, ["onClick"])
      ])
    ], 2)
  ])) : createCommentVNode("", true);
}
const IncomingCallAlert = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render], ["__scopeId", "data-v-3d7e27c4"]]);
const _hoisted_1$2 = { class: "ccbar-integrated" };
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "CCBar",
  props: {
    config: {
      type: Object,
      required: true
    },
    showDisplayNumber: {
      type: Boolean,
      default: false
    },
    serviceType: {
      type: String,
      default: "websocket",
      validator: (value) => ["websocket", "polling"].includes(value)
    }
  },
  emits: ["state-changed", "login", "logout", "call-connected", "call-disconnected", "call-rejected", "transfer"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const ccbarService = CCBarService.getInstance(props.config);
    const showLoginDialog = ref(false);
    const transferDialogVisible = ref(false);
    const showIncomingCallAlert = ref(false);
    const incomingCallInfo = ref({
      phoneNumber: "",
      customerName: "",
      time: "",
      notes: ""
    });
    const skills = ref([]);
    const currentCall = ref({
      caller: "",
      called: "",
      custPhone: "",
      displayCustPhone: ""
    });
    const handleInitComplete = (data) => {
      console.log("CCBar: 工具条初始化完成", data.skills);
      if (data && data.skills && Array.isArray(data.skills)) {
        skills.value = data.skills;
      }
    };
    const hideLoginDialog = () => {
      showLoginDialog.value = false;
    };
    const handleLoginSuccess = (data) => {
      hideLoginDialog();
      if (data && !data.phone && ccbarService) {
        const agentInfo = ccbarService.getAgentInfo();
        if (agentInfo && agentInfo.phone) {
          data.phone = agentInfo.phone;
        }
      }
      if (data && data.phone) {
        localStorage.setItem("phone", data.phone);
      }
      emit("login", data);
    };
    const handleStateChange = (data) => {
      emit("state-changed", data);
    };
    const handleCallEvent = (data) => {
      if (data.type === CallEventType.CONNECTED) {
        currentCall.value = {
          caller: data.event.caller,
          called: data.event.called,
          custPhone: data.event.custPhone,
          displayCustPhone: data.event.displayCustPhone
        };
        emit("call-connected", data);
      } else if (data.type === CallEventType.DISCONNECTED) {
        emit("call-disconnected", data);
      }
    };
    const handleTransfer = async (transferInfo) => {
      await ccbarService.transferCall(transferInfo);
      emit("transfer", transferInfo);
    };
    const handleAnswerCall = async () => {
      await ccbarService.answerCall();
      showIncomingCallAlert.value = false;
    };
    const handleRejectCall = async () => {
      showIncomingCallAlert.value = false;
      try {
        await ccbarService.clearCall();
        emit("call-rejected", {
          phoneNumber: incomingCallInfo.value.phoneNumber,
          time: (/* @__PURE__ */ new Date()).toLocaleTimeString()
        });
      } catch (error) {
        console.error("拒接来电失败", error);
      }
    };
    onMounted(() => {
      ccbarService.on(CallEventType.ALERTING, (event, callEvent) => {
        var _a, _b;
        const now = /* @__PURE__ */ new Date();
        const timeStr = now.toLocaleTimeString();
        incomingCallInfo.value = {
          phoneNumber: callEvent.event.displayCustPhone || callEvent.event.custPhone,
          customerName: ((_a = callEvent.event.userData) == null ? void 0 : _a.custName) || "",
          time: timeStr,
          notes: ((_b = callEvent.event.userData) == null ? void 0 : _b.customData) || ""
        };
        showIncomingCallAlert.value = true;
      });
      ccbarService.on(CallEventType.CONNECTED, () => {
        showIncomingCallAlert.value = false;
      });
      ccbarService.on(CallEventType.DISCONNECTED, () => {
        showIncomingCallAlert.value = false;
      });
    });
    watch(() => props.config, (newConfig) => {
      if (newConfig && newConfig.baseURL) {
        CCBarService.getInstance(newConfig);
      }
    }, { deep: true });
    return (_ctx, _cache) => {
      const _component_cc_bar_toolbar = resolveComponent("cc-bar-toolbar");
      const _component_cc_bar_login = resolveComponent("cc-bar-login");
      return openBlock(), createElementBlock("div", _hoisted_1$2, [
        createVNode(_component_cc_bar_toolbar, {
          config: __props.config,
          "show-display-number": __props.showDisplayNumber,
          "service-type": __props.serviceType,
          onShowLogin: _cache[0] || (_cache[0] = ($event) => showLoginDialog.value = true),
          onStateChanged: handleStateChange,
          onCallEvent: handleCallEvent,
          onTransferRequest: _cache[1] || (_cache[1] = ($event) => transferDialogVisible.value = true),
          onInitComplete: handleInitComplete
        }, null, 8, ["config", "show-display-number", "service-type"]),
        createVNode(_component_cc_bar_login, {
          config: __props.config,
          visible: showLoginDialog.value,
          skills: skills.value,
          onClose: hideLoginDialog,
          onLoginSuccess: handleLoginSuccess
        }, null, 8, ["config", "visible", "skills"]),
        createVNode(TransferCallDialog, {
          modelValue: transferDialogVisible.value,
          "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => transferDialogVisible.value = $event),
          "current-call": currentCall.value,
          "show-display-number": __props.showDisplayNumber,
          onTransfer: handleTransfer
        }, null, 8, ["modelValue", "current-call", "show-display-number"]),
        createVNode(IncomingCallAlert, {
          show: showIncomingCallAlert.value,
          callInfo: incomingCallInfo.value,
          onAnswer: handleAnswerCall,
          onReject: handleRejectCall
        }, null, 8, ["show", "callInfo"])
      ]);
    };
  }
});
const CCBar_vue_vue_type_style_index_0_scoped_c1391e1b_lang = "";
const CCBarComponent = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-c1391e1b"]]);
const _hoisted_1$1 = { class: "toolbar-main" };
const _hoisted_2$1 = { class: "toolbar-status" };
const _hoisted_3$1 = { class: "agent-info" };
const _hoisted_4$1 = { class: "agent-state-duration" };
const _hoisted_5$1 = {
  key: 0,
  class: "agent-id"
};
const _hoisted_6$1 = {
  key: 1,
  class: "agent-outbound-number"
};
const _hoisted_7$1 = {
  key: 0,
  class: "state-controls"
};
const _hoisted_8$1 = ["disabled"];
const _hoisted_9$1 = ["disabled"];
const _hoisted_10$1 = {
  key: 0,
  class: "toolbar-call"
};
const _hoisted_11$1 = { class: "call-input" };
const _hoisted_12$1 = ["disabled"];
const _hoisted_13 = { class: "call-controls" };
const _hoisted_14 = ["disabled"];
const _hoisted_15 = ["disabled"];
const _hoisted_16 = ["disabled"];
const _hoisted_17 = ["disabled"];
const _hoisted_18 = ["disabled"];
const _hoisted_19 = {
  key: 1,
  class: "toolbar-login"
};
const _hoisted_20 = {
  key: 2,
  class: "toolbar-logout"
};
const _hoisted_21 = ["disabled"];
const _hoisted_22 = {
  key: 0,
  class: "toolbar-notification"
};
const _hoisted_23 = { class: "event-item" };
const _hoisted_24 = { class: "event-type" };
const _hoisted_25 = { class: "event-content" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CCBarToolbar",
  props: {
    config: {
      type: Object,
      required: true
    }
  },
  emits: [
    "show-login",
    "login-success",
    "logout-success",
    "state-changed",
    "call-event",
    "init-complete"
  ],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const expanded = ref(true);
    ref(false);
    const phoneNumber = ref("");
    const outboundNumber = ref("");
    const latestEvent = ref(
      null
    );
    const stateStartTime = ref(/* @__PURE__ */ new Date());
    const stateDuration = ref(0);
    const stateTimer = ref(null);
    const isInitialized = ref(false);
    const currentCallData = ref(null);
    const globalStateManager = GlobalStateManager.getInstance();
    let ccbarService = CCBarService.getInstance(props.config);
    const eventEmitter = EventEmitter.getInstance();
    const eventManager = new EventManager(eventEmitter);
    const isLoggedInFromState = computed(() => globalStateManager.isLoggedIn());
    const isLoggedIn = ref(false);
    const agentId = computed(() => globalStateManager.getAgentId());
    const agentState = reactive({
      state: AgentStateType.LOGOFF,
      stateDesc: "登出",
      funcMask: {}
    });
    const isMuted = ref(false);
    const canMakeCall = computed(() => {
      var _a;
      return ((_a = agentState.funcMask) == null ? void 0 : _a.makecall) || false;
    });
    const canSetReady = computed(() => {
      var _a;
      return ((_a = agentState.funcMask) == null ? void 0 : _a.agentready) || false;
    });
    const canSetNotReady = computed(
      () => {
        var _a;
        return ((_a = agentState.funcMask) == null ? void 0 : _a.agentnotready) || false;
      }
    );
    const canHangupCall = computed(() => {
      var _a;
      const fromFuncMask = ((_a = agentState.funcMask) == null ? void 0 : _a.clearcall) || false;
      if (agentState.state === AgentStateType.HELD || isMuted.value || agentState.state === AgentStateType.TALK) {
        return true;
      }
      return fromFuncMask;
    });
    const canHoldCall = computed(() => {
      var _a;
      if (isMuted.value || agentState.state === AgentStateType.HELD) {
        return false;
      }
      return ((_a = agentState.funcMask) == null ? void 0 : _a.holdcall) || false;
    });
    const canRetrieveCall = computed(() => {
      var _a;
      if (agentState.state === AgentStateType.HELD) {
        console.log("HELD状态，强制启用恢复按钮");
        return true;
      }
      return ((_a = agentState.funcMask) == null ? void 0 : _a.unholdcall) || false;
    });
    const canMuteCall = computed(() => {
      var _a;
      if (agentState.state === AgentStateType.HELD) {
        return false;
      }
      if (agentState.state === AgentStateType.TALK) {
        return true;
      }
      return ((_a = agentState.funcMask) == null ? void 0 : _a.mutecall) || false;
    });
    const canUnmuteCall = computed(() => {
      var _a;
      if (agentState.state === AgentStateType.MUTE) {
        return true;
      }
      return ((_a = agentState.funcMask) == null ? void 0 : _a.unMutecall) || false;
    });
    const canLogout = computed(() => {
      var _a;
      return ((_a = agentState.funcMask) == null ? void 0 : _a.logoff) || false;
    });
    watch(isLoggedInFromState, (newValue) => {
      console.log(`isLoggedInFromState变化: ${newValue}`);
      isLoggedIn.value = newValue;
    });
    const setupLoginStateListener = () => {
      isLoggedIn.value;
      watch(isLoggedIn, (newValue, oldValue) => {
        if (newValue !== oldValue) {
          stateStartTime.value = /* @__PURE__ */ new Date();
          stateDuration.value = 0;
          console.log("登录状态变更，重置状态计时器");
        }
      });
      globalStateManager.onStateChanged((event) => {
        console.log("接收到全局状态变化，检查登录状态");
        setTimeout(() => {
          ensureLoginStateSync();
        }, 0);
      });
    };
    const ensureLoginStateSync = () => {
      const currentLoginState = globalStateManager.isLoggedIn();
      if (currentLoginState !== isLoggedIn.value) {
        console.log(
          `强制同步登录状态: 全局=${currentLoginState}, 本地=${isLoggedIn.value}`
        );
        isLoggedIn.value = currentLoginState;
        const currentState = globalStateManager.getState();
        if (currentState) {
          globalStateManager.setState(currentState.state, currentState.stateDesc);
        }
        setTimeout(() => {
          console.log("登录状态同步完成");
          emit("state-changed", agentState);
        }, 100);
        setTimeout(() => {
          forceUpdateCounter.value += 1;
        }, 10);
      }
    };
    const forceUpdateCounter = ref(0);
    let skills = ref([]);
    const ccbarInit = async () => {
      try {
        if (isInitialized.value) {
          return { state: true, msg: "话务条已初始化" };
        }
        addEvent("系统", "初始化话务条...");
        const result = await ccbarService.init();
        if (result && result.state) {
          isInitialized.value = true;
          console.log("话务条初始化成功", result);
          addEvent("系统", "话务条初始化成功");
          skills.value = result.data.result.groups;
          emit("init-complete", {
            success: true,
            data: result,
            skills: skills.value,
            timestamp: (/* @__PURE__ */ new Date()).getTime()
          });
          return {
            state: true,
            msg: "初始化成功",
            data: result
          };
        } else {
          console.error("话务条初始化失败", result);
          addEvent("错误", `话务条初始化失败: ${(result == null ? void 0 : result.msg) || "未知错误"}`);
          emit("init-complete", {
            success: false,
            error: (result == null ? void 0 : result.msg) || "初始化失败",
            timestamp: (/* @__PURE__ */ new Date()).getTime()
          });
          return {
            state: false,
            msg: (result == null ? void 0 : result.msg) || "初始化失败",
            data: result
          };
        }
      } catch (error) {
        console.error("话务条初始化出错", error);
        addEvent("错误", `话务条初始化出错: ${error.message || "未知错误"}`);
        emit("init-complete", {
          success: false,
          error: error.message || "未知错误",
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        });
        return {
          state: false,
          msg: error.message || "初始化出错",
          data: null
        };
      }
    };
    onMounted(() => {
      console.log("CCBarToolbar组件已挂载, 配置:", props.config);
      if (!props.config || !props.config.baseURL) {
        console.warn("未提供有效的CCBar配置，请检查config属性是否正确传递");
        addEvent("错误", "未提供有效的CCBar配置，请检查config属性");
        return;
      }
      try {
        console.log("正在初始化CCBar服务，配置:", JSON.stringify(props.config));
        ccbarService = CCBarService.getInstance(props.config);
        isLoggedIn.value = globalStateManager.isLoggedIn();
        startStateTimer();
        addEvent("系统", "CCBar工具条已初始化");
      } catch (error) {
        console.error("初始化CCBar服务失败:", error);
        addEvent("错误", `初始化失败: ${error}`);
      }
      setupEventHandlers();
      setupLoginStateListener();
      startStateTimer();
      if (props.config && props.config.outboundNumber) {
        outboundNumber.value = props.config.outboundNumber;
        console.log("从配置中获取外显号码:", outboundNumber.value);
      }
      ensureLoginStateSync();
      console.log("CCBar工具栏已挂载，初始登录状态:", isLoggedIn.value);
      ccbarInit();
      isMuted.value = false;
      watch(
        isMuted,
        (newValue) => {
          console.log("静音状态变化:", newValue);
          forceUpdateCounter.value += 1;
          if (newValue) {
            console.log("进入静音状态，检查按钮显示逻辑");
          } else {
            console.log("退出静音状态，检查按钮显示逻辑");
          }
        },
        { immediate: true }
      );
      watch(
        () => agentState.state,
        (newState) => {
          console.log("座席状态变化:", newState);
          if (newState !== AgentStateType.TALK && isMuted.value) {
            console.log("非通话状态，重置静音状态");
            isMuted.value = false;
          }
          forceUpdateCounter.value += 1;
        }
      );
      eventEmitter.on("call:muted", (data) => {
        console.log("检测到call:muted事件", data);
        isMuted.value = true;
        forceUpdateCounter.value += 1;
      });
      eventEmitter.on("call:unmuted", (data) => {
        console.log("检测到call:unmuted事件", data);
        isMuted.value = false;
        forceUpdateCounter.value += 1;
      });
      console.log("组件挂载时的静音状态:", isMuted.value);
    });
    onBeforeUnmount(() => {
      if (stateTimer.value) {
        clearInterval(stateTimer.value);
        stateTimer.value = null;
      }
    });
    const getStateClass = () => {
      if (!isLoggedIn.value || agentState.state === AgentStateType.LOGOFF) {
        return "state-offline";
      }
      switch (agentState.state) {
        case AgentStateType.IDLE:
          return "state-ready";
        case AgentStateType.BUSY:
          return "state-busy";
        case AgentStateType.TALK:
          return "state-talking";
        case AgentStateType.ALERTING:
          return "state-ringing";
        case AgentStateType.HELD:
          return "state-held";
        case AgentStateType.WORKNOTREADY:
          return "state-acw";
        default:
          return "state-offline";
      }
    };
    const addEvent = (type, content, id = "") => {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      const eventContent = id ? `[ID:${id}] ${content}` : content;
      latestEvent.value = { time, type, content: eventContent };
      emit("call-event", { time, type, content: eventContent });
    };
    const setupEventHandlers = () => {
      eventManager.on("agent:loginResponse", (events, data) => {
        var _a, _b, _c, _d, _e, _f;
        console.log("CCBarToolbar收到登录响应", events, data);
        const responseData = data || (events && events.target ? events : null);
        if (!responseData) {
          console.error("登录响应数据为空", events, data);
          addEvent("登录", "登录响应数据无效", "LOGIN_INVALID_RESPONSE");
          return;
        }
        if (responseData.rawEvent) {
          console.log("登录响应原始事件:", responseData);
        }
        if (responseData.resultCode === "0" || responseData.resultCode === 0) {
          console.log(
            `登录成功，工号: ${responseData.agentId}，时间: ${responseData.loginTime}`
          );
          if (responseData.outboundNumber || ((_c = (_b = (_a = responseData.rawEvent) == null ? void 0 : _a.cmddata) == null ? void 0 : _b.result) == null ? void 0 : _c.outboundNumber)) {
            outboundNumber.value = responseData.outboundNumber || ((_f = (_e = (_d = responseData.rawEvent) == null ? void 0 : _d.cmddata) == null ? void 0 : _e.result) == null ? void 0 : _f.outboundNumber) || "";
            console.log("设置外显号码:", outboundNumber.value);
          }
          setTimeout(() => {
            isLoggedIn.value = true;
            agentState.state = AgentStateType.BUSY;
            setDefaultFuncMask(agentState.state, agentState.stateDesc);
            agentState.funcMask.logoff = true;
            agentState.funcMask.agentready = true;
            agentState.funcMask.agentnotready = false;
            forceUpdateCounter.value += 1;
            console.log(
              "登录成功后更新状态:",
              isLoggedIn.value,
              "功能掩码:",
              agentState.funcMask
            );
            emit("state-changed", {
              ...agentState,
              state: agentState.state,
              stateDesc: agentState.stateDesc
            });
          }, 100);
          localStorage.setItem("agentId", responseData.agentId);
          if (responseData.phone) {
            localStorage.setItem("phone", responseData.phone);
          }
          addEvent(
            "登录",
            `登录成功，工号: ${responseData.agentId || "未知"}`,
            "LOGIN_SUCCESS"
          );
          emit("login-success", {
            agentId: responseData.agentId,
            phone: responseData.phone || "",
            state: agentState.state,
            stateDesc: agentState.stateDesc,
            funcMask: agentState.funcMask
          });
        } else {
          console.error(
            `登录失败: ${responseData.resultDesc || "未知原因"}, 代码: ${responseData.resultCode || "未知"}`
          );
          addEvent(
            "登录",
            `登录失败: ${responseData.resultDesc || "未知原因"}`,
            "LOGIN_FAILED"
          );
        }
      });
      eventManager.on("agent:stateChanged", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到座席状态变更事件:", responseData);
        if (!responseData) {
          addEvent("状态", "座席状态数据无效", "STATE_INVALID_DATA");
          return;
        }
        const stateData = responseData.cmddata || responseData;
        if (stateData) {
          if (stateData.state && stateData.state !== agentState.state) {
            stateStartTime.value = /* @__PURE__ */ new Date();
            stateDuration.value = 0;
          }
          if (stateData.state) {
            agentState.state = stateData.state;
            agentState.stateDesc = stateData.stateDesc || getStateDescription(stateData.state);
            addEvent(
              "状态变更",
              `座席状态: ${agentState.stateDesc} (${stateData.state})`,
              "STATE_CHANGED"
            );
            if (agentState.state === AgentStateType.HELD) {
              console.log("检测到HELD状态，确保恢复和挂断按钮可用");
              agentState.funcMask.unholdcall = true;
              agentState.funcMask.clearcall = true;
            }
            if (agentState.state === AgentStateType.TALK && !currentCallData.value) {
              console.log("检测到TALK状态，但没有通话数据，创建空的通话数据");
              currentCallData.value = {
                callId: "",
                caller: "114",
                called: "",
                agentId: agentId.value || localStorage.getItem("agentId") || ""
              };
            }
          }
          if (stateData.funcMask) {
            agentState.funcMask = { ...stateData.funcMask };
            if (agentState.state === AgentStateType.HELD) {
              agentState.funcMask.unholdcall = true;
              agentState.funcMask.clearcall = true;
            }
            if (agentState.state === AgentStateType.TALK) {
              agentState.funcMask.clearcall = true;
            }
          } else {
            setDefaultFuncMask(agentState.state, agentState.stateDesc);
          }
          if (isLoggedIn.value) {
            agentState.funcMask.logoff = true;
          }
          emit("state-changed", agentState);
        }
      });
      eventManager.on("agentStateSync", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到座席状态同步事件:", responseData);
        if (!responseData) {
          console.error("座席状态同步数据为空", events, data);
          addEvent("状态", "座席状态同步数据无效", "STATE_SYNC_INVALID");
          return;
        }
        const stateData = responseData.cmddata || responseData;
        if (stateData) {
          if (stateData.state && stateData.state !== agentState.state) {
            stateStartTime.value = /* @__PURE__ */ new Date();
            stateDuration.value = 0;
            console.log("座席状态已同步，重置计时器:", stateData.state);
          }
          if (stateData.state) {
            agentState.state = stateData.state;
            agentState.stateDesc = stateData.stateDesc || getStateDescription(stateData.state);
            addEvent(
              "状态同步",
              `座席状态更新: ${agentState.stateDesc} (${stateData.state})`,
              "STATE_SYNC_UPDATED"
            );
          }
          if (stateData.funcMask) {
            agentState.funcMask = { ...stateData.funcMask };
          } else {
            setDefaultFuncMask(agentState.state, agentState.stateDesc);
          }
          if (isLoggedIn.value) {
            agentState.funcMask.logoff = true;
          }
          if (agentState.state === AgentStateType.HELD) {
            console.log("状态同步检测到HELD状态，确保恢复和挂断按钮可用");
            agentState.funcMask.unholdcall = true;
            agentState.funcMask.clearcall = true;
          } else if (agentState.state === AgentStateType.IDLE || agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休" || agentState.stateDesc === "空闲") {
            if (agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休") {
              agentState.funcMask.agentready = true;
              agentState.funcMask.agentnotready = false;
            } else if (agentState.state === AgentStateType.IDLE && agentState.stateDesc === "空闲") {
              agentState.funcMask.agentready = true;
              agentState.funcMask.agentnotready = true;
            }
          }
          emit("state-changed", agentState);
        }
      });
      eventManager.on("call:eventSync", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到呼叫事件:", responseData);
        if (!responseData) {
          console.error("呼叫事件数据为空", events, data);
          addEvent("呼叫", "呼叫事件数据无效");
          return;
        }
        const { agentId: agentId2, callId, callEventId, entId, caller, called, custPhone } = responseData;
        currentCallData.value = responseData;
        console.log("已保存当前通话数据:", currentCallData.value);
        if (callEventId === "evtAltering") {
          ElMessage({
            message: `振铃中：${custPhone}`,
            type: "info",
            plain: true
          });
        } else if (callEventId === "evtConnected") {
          ElMessage({
            message: `正在通话`,
            type: "info",
            plain: true
          });
        }
        currentCallData.value = responseData;
        addEvent(
          "呼叫",
          `呼叫事件: ${callId}, 事件ID: ${callEventId}, 企业ID: ${entId}, 主叫: ${caller}, 被叫: ${called}`
        );
      });
      eventManager.on("polling:stateSync", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到轮询状态同步事件:", responseData);
        if (!responseData) {
          console.error("轮询状态同步数据为空", events, data);
          return;
        }
        ensureLoginStateSync();
        const globalState = globalStateManager.getState();
        if (globalState) {
          if (globalState.state && globalState.state !== agentState.state) {
            console.log(
              `检测到座席状态不一致，全局状态: ${globalState.state}, UI状态: ${agentState.state}`
            );
            agentState.state = globalState.state;
            agentState.stateDesc = globalState.stateDesc || getStateDescription(globalState.state);
            if (globalState.funcMask) {
              agentState.funcMask = { ...globalState.funcMask };
            } else {
              setDefaultFuncMask(agentState.state, agentState.stateDesc);
            }
            if (isLoggedIn.value) {
              agentState.funcMask.logoff = true;
            }
            if (agentState.state === AgentStateType.HELD) {
              console.log("在轮询同步中检测到HELD状态，确保恢复和挂断按钮可用");
              agentState.funcMask.unholdcall = true;
              agentState.funcMask.clearcall = true;
            }
            if (agentState.state === AgentStateType.TALK) {
              console.log("在轮询同步中检测到TALK状态，确保挂断按钮可用");
              agentState.funcMask.clearcall = true;
              if (!currentCallData.value) {
                console.log("TALK状态但没有通话数据，创建默认通话数据");
                currentCallData.value = {
                  callId: "",
                  caller: "114",
                  called: "",
                  agentId: agentId.value || localStorage.getItem("agentId") || ""
                };
              }
            }
            if (agentState.state === AgentStateType.IDLE || agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休" || agentState.stateDesc === "空闲") {
              if (agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休") {
                agentState.funcMask.agentready = true;
                agentState.funcMask.agentnotready = false;
              } else if (agentState.state === AgentStateType.IDLE && agentState.stateDesc === "空闲") {
                agentState.funcMask.agentready = true;
                agentState.funcMask.agentnotready = true;
              }
            }
            stateStartTime.value = /* @__PURE__ */ new Date();
            stateDuration.value = 0;
            addEvent("状态同步", `座席状态已同步: ${agentState.stateDesc}`);
          } else {
            if (isLoggedIn.value) {
              agentState.funcMask.logoff = true;
            }
            if (agentState.state === AgentStateType.HELD) {
              console.log("在轮询同步中检查HELD状态按钮可用性");
              if (!agentState.funcMask.unholdcall) {
                console.log("修复HELD状态下恢复按钮不可用的问题");
                agentState.funcMask.unholdcall = true;
              }
              if (!agentState.funcMask.clearcall) {
                console.log("修复HELD状态下挂断按钮不可用的问题");
                agentState.funcMask.clearcall = true;
              }
            }
            if (agentState.state === AgentStateType.TALK) {
              console.log("在轮询同步中检查TALK状态挂断按钮可用性");
              if (!agentState.funcMask.clearcall) {
                console.log("修复TALK状态下挂断按钮不可用的问题");
                agentState.funcMask.clearcall = true;
              }
              if (!currentCallData.value) {
                console.log("TALK状态但无通话数据，创建默认通话数据");
                currentCallData.value = {
                  callId: "",
                  caller: "114",
                  called: "",
                  agentId: agentId.value || localStorage.getItem("agentId") || ""
                };
              }
            }
            if (agentState.state === AgentStateType.IDLE || agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休" || agentState.stateDesc === "空闲") {
              if (agentState.state === AgentStateType.BUSY && agentState.stateDesc === "小休") {
                agentState.funcMask.agentready = true;
                agentState.funcMask.agentnotready = false;
              } else if (agentState.state === AgentStateType.IDLE && agentState.stateDesc === "空闲") {
                agentState.funcMask.agentready = true;
                agentState.funcMask.agentnotready = true;
              }
            }
          }
        }
      });
      eventManager.on("call:muted", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到静音事件:", responseData);
        isMuted.value = true;
        console.log("设置isMuted=true，麦克风已静音");
        addEvent("呼叫", "麦克风已静音");
        forceUpdateCounter.value += 1;
      });
      eventManager.on("call:unmuted", (events, data) => {
        const responseData = data || (events && events.target ? events : null);
        console.log("收到取消静音事件:", responseData);
        isMuted.value = false;
        console.log("设置isMuted=false，麦克风已取消静音");
        addEvent("呼叫", "麦克风已取消静音");
        forceUpdateCounter.value += 1;
      });
      eventManager.on("session:403error", (data) => {
        console.log("收到403错误事件:", data);
        if (isLoggedIn.value) {
          addEvent("系统", "检测到会话已过期，正在重置状态", "SESSION_EXPIRED");
          try {
            ccbarService.resetLoginState();
            isLoggedIn.value = false;
            sessionStorage.setItem("isLogined", "false");
            agentState.state = AgentStateType.LOGOFF;
            agentState.stateDesc = "登出";
            agentState.funcMask = {
              logon: true,
              // 仅允许登录操作
              logoff: false,
              makecall: false,
              clearcall: false,
              holdcall: false,
              unholdcall: false,
              transfercall: false,
              conference: false,
              agentready: false,
              agentnotready: false
            };
            outboundNumber.value = "";
            addEvent("系统", "状态已重置为登出状态", "STATE_RESET");
            forceUpdateCounter.value += 1;
          } catch (error) {
            console.error("重置状态失败:", error);
            addEvent("错误", `重置状态失败: ${error}`, "RESET_ERROR");
          }
        } else {
          console.log("当前未登录，忽略403错误事件");
        }
      });
      eventManager.on("polling:stopped", (data) => {
        console.log("收到轮询已停止事件:", data);
        addEvent("系统", "轮询服务已停止", "POLLING_STOPPED");
      });
      eventManager.on("agent:talk", (events, data) => {
        console.log("收到通话状态事件:", data);
        isMuted.value = false;
        forceUpdateCounter.value += 1;
      });
      eventManager.on("agent:held", (events, data) => {
        console.log("收到保持状态事件:", data);
        isMuted.value = false;
        forceUpdateCounter.value += 1;
      });
    };
    const getStateDescription = (state) => {
      const stateMap = {
        IDLE: "空闲",
        READY: "就绪",
        BUSY: "忙碌",
        TALK: "通话中",
        ALERTING: "振铃中",
        HELD: "保持中",
        OFFLINE: "离线",
        ACW: "话后处理"
      };
      return stateMap[state] || state;
    };
    const setAgentReady = async () => {
      if (!canSetReady.value) {
        addEvent("错误", "当前状态不允许设置为空闲", "SET_READY_NOT_ALLOWED");
        return;
      }
      try {
        const result = await ccbarService.agentReady();
        if (result.state) {
          addEvent("状态", "座席已置闲", "SET_READY_SUCCESS");
        } else {
          addEvent(
            "错误",
            `置闲失败: ${result.msg || "未知错误"}`,
            "SET_READY_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `置闲出错: ${error.message || "未知错误"}`,
          "SET_READY_ERROR"
        );
      }
    };
    const setAgentNotReady = async () => {
      if (!canSetNotReady.value) {
        addEvent("错误", "当前状态不允许设置为繁忙", "SET_NOTREADY_NOT_ALLOWED");
        return;
      }
      try {
        const result = await ccbarService.agentNotReady();
        console.log(result, 123456);
        if (result.state) {
          addEvent("状态", "座席已置忙", "SET_NOTREADY_SUCCESS");
        } else {
          addEvent(
            "错误",
            `置忙失败: ${result.msg || "未知错误"}`,
            "SET_NOTREADY_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `置忙出错: ${error.message || "未知错误"}`,
          "SET_NOTREADY_ERROR"
        );
      }
    };
    const setAgentState = async (state) => {
      let reasonCode;
      let reasonDesc;
      switch (state) {
        case "break":
          reasonCode = "1";
          reasonDesc = "休息中";
          break;
        case "meeting":
          reasonCode = "2";
          reasonDesc = "会议中";
          break;
        case "training":
          reasonCode = "3";
          reasonDesc = "培训中";
          break;
        default:
          reasonCode = "0";
          reasonDesc = "其他原因";
      }
      try {
        const result = await ccbarService.agentNotReady(reasonCode);
        console.log("设置状态变更结果:", result);
        if (result.state) {
          addEvent(
            "状态变更",
            `座席状态变更为: ${reasonDesc}`,
            `SET_STATE_${state.toUpperCase()}`
          );
        } else {
          addEvent(
            "错误",
            `状态变更失败: ${result.msg || "未知错误"}`,
            `SET_STATE_${state.toUpperCase()}_FAILED`
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `状态变更出错: ${error.message || "未知错误"}`,
          `SET_STATE_${state.toUpperCase()}_ERROR`
        );
      }
    };
    const makeCall = async () => {
      if (!phoneNumber.value) {
        addEvent("警告", "请输入电话号码", "MAKE_CALL_NO_NUMBER");
        return;
      }
      if (!canMakeCall.value) {
        addEvent("错误", "当前状态不允许拨打电话", "MAKE_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", `开始拨号: ${phoneNumber.value}`, "MAKE_CALL_START");
        const result = await ccbarService.makeCall(phoneNumber.value);
        ElMessage({
          message: `正在呼叫: ${phoneNumber.value}`,
          type: "info",
          plain: true
        });
        if (result.state) {
          addEvent("呼叫", `正在呼叫: ${phoneNumber.value}`, "MAKE_CALL_DIALING");
        } else {
          addEvent(
            "错误",
            `拨号失败: ${result.msg || "未知错误"}`,
            "MAKE_CALL_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `拨号失败: ${error.message || "未知错误"}`,
          "MAKE_CALL_ERROR"
        );
      }
    };
    const hangupCall = async () => {
      if (!canHangupCall.value) {
        addEvent("错误", "当前状态不允许挂断电话", "HANGUP_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", "挂断电话", "HANGUP_CALL_START");
        const callDataToUse = currentCallData.value || {
          // 创建一个默认的通话数据对象
          callId: "",
          // 空的callId
          caller: "114",
          // 默认主叫
          called: "",
          // 默认被叫
          agentId: agentId.value || localStorage.getItem("agentId") || ""
        };
        console.log("使用以下数据进行挂断:", callDataToUse);
        const result = await ccbarService.clearCall(callDataToUse);
        if (result.state) {
          addEvent("呼叫", "通话已结束", "HANGUP_CALL_SUCCESS");
          currentCallData.value = null;
          console.log("手动挂断通话，清除通话数据");
        } else {
          addEvent(
            "错误",
            `挂断失败: ${result.msg || "未知错误"}`,
            "HANGUP_CALL_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `挂断失败: ${error.message || "未知错误"}`,
          "HANGUP_CALL_ERROR"
        );
      }
    };
    const holdCall = async () => {
      if (!canHoldCall.value) {
        addEvent("错误", "当前状态不允许保持电话", "HOLD_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", "保持通话", "HOLD_CALL_START");
        if (currentCallData.value) {
          console.log("使用保存的通话数据进行保持:", currentCallData.value);
        }
        const result = await ccbarService.holdCall();
        if (result.state) {
          addEvent("呼叫", "通话已保持", "HOLD_CALL_SUCCESS");
        } else {
          addEvent(
            "错误",
            `保持失败: ${result.msg || "未知错误"}`,
            "HOLD_CALL_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `保持失败: ${error.message || "未知错误"}`,
          "HOLD_CALL_ERROR"
        );
      }
    };
    const retrieveCall = async () => {
      console.log(
        "尝试恢复通话，当前状态:",
        agentState.state,
        "恢复按钮可用:",
        canRetrieveCall.value
      );
      console.log("功能掩码:", JSON.stringify(agentState.funcMask));
      if (!canRetrieveCall.value) {
        addEvent("错误", "当前状态不允许恢复电话", "RETRIEVE_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", "恢复通话", "RETRIEVE_CALL_START");
        if (currentCallData.value) {
          console.log("使用保存的通话数据进行恢复:", currentCallData.value);
        }
        const result = await ccbarService.unholdCall();
        if (result.state) {
          addEvent("呼叫", "通话已恢复", "RETRIEVE_CALL_SUCCESS");
        } else {
          addEvent(
            "错误",
            `恢复失败: ${result.msg || "未知错误"}`,
            "RETRIEVE_CALL_FAILED"
          );
        }
      } catch (error) {
        addEvent(
          "错误",
          `恢复失败: ${error.message || "未知错误"}`,
          "RETRIEVE_CALL_ERROR"
        );
      }
    };
    const muteCall = async () => {
      console.log("尝试静音通话，当前静音状态:", isMuted.value);
      if (!canMuteCall.value) {
        console.log("当前状态不允许静音，canMuteCall:", canMuteCall.value);
        addEvent("错误", "当前状态不允许静音", "MUTE_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", "静音通话", "MUTE_CALL_START");
        const result = await ccbarService.muteCall();
        console.log("静音通话请求结果:", result);
        if (result.state) {
          isMuted.value = true;
          console.log("静音成功，设置isMuted=true");
          addEvent("呼叫", "麦克风已静音", "MUTE_CALL_SUCCESS");
          eventEmitter.emit("call:muted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
          forceUpdateCounter.value += 1;
          setTimeout(() => {
            if (!isMuted.value) {
              console.warn("静音状态未正确保持，再次强制设置");
              isMuted.value = true;
              forceUpdateCounter.value += 1;
            }
          }, 100);
        } else {
          addEvent(
            "错误",
            `静音失败: ${result.msg || "未知错误"}`,
            "MUTE_CALL_FAILED"
          );
        }
      } catch (error) {
        console.error("静音操作异常:", error);
        addEvent(
          "错误",
          `静音失败: ${error.message || "未知错误"}`,
          "MUTE_CALL_ERROR"
        );
      }
    };
    const unmuteCall = async () => {
      console.log("尝试取消静音通话，当前静音状态:", isMuted.value);
      if (!canUnmuteCall.value) {
        console.log("当前状态不允许取消静音，canUnmuteCall:", canUnmuteCall.value);
        addEvent("错误", "当前状态不允许取消静音", "UNMUTE_CALL_NOT_ALLOWED");
        return;
      }
      try {
        addEvent("呼叫", "取消静音通话", "UNMUTE_CALL_START");
        const result = await ccbarService.unmuteCall();
        console.log("取消静音通话请求结果:", result);
        if (result.state) {
          isMuted.value = false;
          console.log("取消静音成功，设置isMuted=false");
          addEvent("呼叫", "麦克风已取消静音", "UNMUTE_CALL_SUCCESS");
          eventEmitter.emit("call:unmuted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
          forceUpdateCounter.value += 1;
          setTimeout(() => {
            if (isMuted.value) {
              console.warn("非静音状态未正确保持，再次强制设置");
              isMuted.value = false;
              forceUpdateCounter.value += 1;
            }
          }, 100);
        } else {
          addEvent(
            "错误",
            `取消静音失败: ${result.msg || "未知错误"}`,
            "UNMUTE_CALL_FAILED"
          );
        }
      } catch (error) {
        console.error("取消静音操作异常:", error);
        addEvent(
          "错误",
          `取消静音失败: ${error.message || "未知错误"}`,
          "UNMUTE_CALL_ERROR"
        );
      }
    };
    const testMuteToggle = async () => {
      console.log("测试静音状态切换");
      if (isMuted.value) {
        console.log("当前是静音状态，尝试取消静音");
        isMuted.value = false;
        eventEmitter.emit("call:unmuted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
      } else {
        console.log("当前是非静音状态，尝试静音");
        isMuted.value = true;
        eventEmitter.emit("call:muted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
      }
      forceUpdateCounter.value += 1;
      addEvent(
        "测试",
        `测试切换静音状态: ${isMuted.value ? "已静音" : "已取消静音"}`
      );
      return {
        newState: isMuted.value,
        message: `静音状态已切换为: ${isMuted.value ? "已静音" : "已取消静音"}`
      };
    };
    const onLogoutButtonClick = () => {
      handleLogout(false);
    };
    const handleLogout = async (isAutoLogout = false) => {
      if (!isAutoLogout && !canLogout.value) {
        addEvent("错误", "当前状态不允许登出");
        return;
      }
      try {
        if (isAutoLogout) {
          console.log("执行自动签出操作");
        }
        const result = await ccbarService.logout();
        if (result.state) {
          stateStartTime.value = /* @__PURE__ */ new Date();
          stateDuration.value = 0;
          isLoggedIn.value = false;
          agentState.state = AgentStateType.LOGOFF;
          agentState.stateDesc = "登出";
          agentState.funcMask = {
            logon: true,
            // 仅允许登录操作
            logoff: false,
            makecall: false,
            clearcall: false,
            holdcall: false,
            unholdcall: false,
            transfercall: false,
            conference: false,
            agentready: false,
            agentnotready: false
          };
          outboundNumber.value = "";
          globalStateManager.resetState();
          forceUpdateCounter.value += 1;
          console.log(
            "登出成功后更新状态:",
            isLoggedIn.value,
            "座席状态:",
            agentState.state
          );
          if (isAutoLogout) {
            addEvent("系统", "会话已过期，系统自动签出");
          } else {
            addEvent("登出", "签出成功");
          }
          emit("logout-success");
        } else {
          addEvent("错误", `签出失败: ${result.msg || "未知错误"}`);
        }
      } catch (error) {
        addEvent("错误", `签出失败: ${error.message || "未知错误"}`);
      }
    };
    const startStateTimer = () => {
      if (stateTimer.value) {
        clearInterval(stateTimer.value);
      }
      stateTimer.value = window.setInterval(() => {
        const now = /* @__PURE__ */ new Date();
        stateDuration.value = Math.floor(
          (now.getTime() - stateStartTime.value.getTime()) / 1e3
        );
      }, 1e3);
    };
    const stateDurationText = computed(() => {
      const hours = Math.floor(stateDuration.value / 3600);
      const minutes = Math.floor(stateDuration.value % 3600 / 60);
      const seconds = stateDuration.value % 60;
      if (hours > 0) {
        return `${hours}小时${minutes}分${seconds}秒`;
      } else if (minutes > 0) {
        return `${minutes}分${seconds}秒`;
      } else {
        return `${seconds}秒`;
      }
    });
    const setDefaultFuncMask = (state, stateDesc) => {
      const defaultMask = {
        logon: false,
        logoff: false,
        makecall: false,
        clearcall: false,
        holdcall: false,
        unholdcall: false,
        transfercall: false,
        conference: false,
        agentready: false,
        agentnotready: false
      };
      switch (state) {
        case AgentStateType.LOGOFF:
          defaultMask.logon = true;
          break;
        case AgentStateType.IDLE:
          defaultMask.logoff = true;
          defaultMask.makecall = true;
          defaultMask.agentnotready = true;
          break;
        case AgentStateType.BUSY:
          defaultMask.logoff = true;
          defaultMask.agentready = true;
          defaultMask.makecall = true;
          break;
        case AgentStateType.TALK:
          defaultMask.clearcall = true;
          defaultMask.holdcall = true;
          defaultMask.transfercall = true;
          defaultMask.logoff = true;
          break;
        case AgentStateType.HELD:
          defaultMask.unholdcall = true;
          defaultMask.clearcall = true;
          defaultMask.logoff = true;
          console.log("在保持状态下设置默认功能掩码: 恢复和挂断按钮已启用");
          break;
        default:
          defaultMask.logoff = true;
      }
      agentState.funcMask = { ...defaultMask };
      console.log("已设置默认功能掩码:", state, stateDesc, agentState.funcMask);
    };
    const getCurrentCallData = () => {
      return currentCallData.value;
    };
    const toggleMuteState = () => {
      isMuted.value = !isMuted.value;
      console.log("手动切换静音状态:", isMuted.value);
      if (isMuted.value) {
        eventEmitter.emit("call:muted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
        addEvent("测试", "手动切换为静音状态");
      } else {
        eventEmitter.emit("call:unmuted", { timestamp: (/* @__PURE__ */ new Date()).getTime() });
        addEvent("测试", "手动切换为非静音状态");
      }
      forceUpdateCounter.value += 1;
    };
    __expose({
      setAgentReady,
      setAgentNotReady,
      setAgentState,
      makeCall,
      hangupCall,
      holdCall,
      retrieveCall,
      muteCall,
      unmuteCall,
      handleLogout,
      ccbarInit,
      getCurrentCallData,
      toggleMuteState,
      // 新增的测试方法
      testMuteToggle
      // 新增测试方法
    });
    watch(
      () => agentState.state,
      (newState, oldState) => {
        if (newState === AgentStateType.HELD) {
          console.log(
            "检测到状态变为HELD，当前功能掩码:",
            JSON.stringify(agentState.funcMask)
          );
          setTimeout(() => {
            if (!agentState.funcMask.unholdcall) {
              console.log("修复HELD状态下恢复按钮不可用问题");
              agentState.funcMask.unholdcall = true;
            }
            if (!agentState.funcMask.clearcall) {
              console.log("修复HELD状态下挂断按钮不可用问题");
              agentState.funcMask.clearcall = true;
            }
          }, 100);
        }
      },
      { immediate: true }
    );
    watch(
      () => canRetrieveCall.value,
      (isEnabled) => {
        if (agentState.state === AgentStateType.HELD && !isEnabled) {
          console.warn("HELD状态下恢复按钮被禁用，强制启用");
          agentState.funcMask.unholdcall = true;
        }
      }
    );
    watch([() => agentState.state, () => isMuted.value], ([state, muted]) => {
      console.log(`当前状态组合: 状态=${state}, 静音=${muted}`);
      console.log("可用按钮情况:", {
        canHangup: canHangupCall.value,
        canHold: canHoldCall.value,
        canRetrieve: canRetrieveCall.value,
        canMute: canMuteCall.value,
        canUnmute: canUnmuteCall.value
      });
    });
    watch(
      [() => isMuted.value, () => agentState.state],
      () => {
        console.log("=== 按钮状态调试信息 ===");
        console.log(`当前静音状态: ${isMuted.value ? "已静音" : "未静音"}`);
        console.log(`当前代理状态: ${agentState.state}`);
        console.log(`挂断按钮可用: ${canHangupCall.value}`);
        console.log(`保持按钮可用: ${canHoldCall.value}`);
        console.log(`恢复按钮可用: ${canRetrieveCall.value}`);
        console.log(`静音按钮可用: ${canMuteCall.value}`);
        console.log(`取消静音按钮可用: ${canUnmuteCall.value}`);
        console.log(
          `静音按钮显示: ${!isMuted.value && agentState.state !== AgentStateType.HELD}`
        );
        console.log(`取消静音按钮显示: ${isMuted.value}`);
        console.log("========================");
      },
      { immediate: true }
    );
    const logButtonStates = () => {
      console.log("=== 按钮状态调试信息 ===");
      console.log(`当前静音状态: ${isMuted.value ? "已静音" : "未静音"}`);
      console.log(`当前代理状态: ${agentState.state}`);
      console.log(`挂断按钮可用: ${canHangupCall.value}`);
      console.log(`保持按钮可用: ${canHoldCall.value}`);
      console.log(`恢复按钮可用: ${canRetrieveCall.value}`);
      console.log(`静音按钮可用: ${canMuteCall.value}`);
      console.log(`取消静音按钮可用: ${canUnmuteCall.value}`);
      console.log(
        `静音按钮显示: ${!isMuted.value && agentState.state !== AgentStateType.HELD}`
      );
      console.log(`取消静音按钮显示: ${isMuted.value}`);
      console.log("========================");
    };
    watch(
      [() => isMuted.value, () => agentState.state],
      () => {
        logButtonStates();
      },
      { immediate: true }
    );
    watch(
      () => agentState.state,
      (newState, oldState) => {
        console.log(`代理状态变更: ${oldState} -> ${newState}`);
        if (oldState === AgentStateType.TALK && newState !== AgentStateType.TALK) {
          console.log("通话状态结束，重置静音状态为false");
          isMuted.value = false;
        }
        if (newState === AgentStateType.HELD && isMuted.value) {
          console.log("进入保持状态，重置静音状态为false");
          isMuted.value = false;
        }
        forceUpdateCounter.value += 1;
      },
      { immediate: true }
    );
    onMounted(() => {
      eventEmitter.on("agent:state", (event) => {
        console.log("代理状态改变事件:", event);
        if (event.state) {
          console.log(`事件通知代理状态变更为 ${event.state}`);
          if (event.state !== AgentStateType.TALK && isMuted.value) {
            console.log("通话结束，重置静音状态");
            isMuted.value = false;
            forceUpdateCounter.value += 1;
          }
        }
      });
      eventEmitter.on("call:muted", (data) => {
        console.log("检测到call:muted事件", data);
        isMuted.value = true;
        forceUpdateCounter.value += 1;
        logButtonStates();
        nextTick(() => {
          console.log("静音状态设置后，DOM已更新");
          logButtonStates();
        });
      });
      eventEmitter.on("call:unmuted", (data) => {
        console.log("检测到call:unmuted事件", data);
        isMuted.value = false;
        forceUpdateCounter.value += 1;
        logButtonStates();
        nextTick(() => {
          console.log("取消静音状态设置后，DOM已更新");
          logButtonStates();
        });
      });
      console.log("组件挂载时的静音状态:", isMuted.value);
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["ccbar-toolbar", { "is-expanded": expanded.value }]),
        key: forceUpdateCounter.value
      }, [
        createElementVNode("div", _hoisted_1$1, [
          createElementVNode("div", _hoisted_2$1, [
            createElementVNode("div", _hoisted_3$1, [
              createElementVNode("div", {
                class: normalizeClass(["agent-state", getStateClass()])
              }, [
                _cache[2] || (_cache[2] = createElementVNode("i", { class: "state-icon" }, null, -1)),
                createElementVNode("span", null, toDisplayString(!isLoggedIn.value ? "登出" : agentState.stateDesc || "未知状态"), 1)
              ], 2),
              createElementVNode("div", _hoisted_4$1, [
                createElementVNode("span", null, "持续: " + toDisplayString(stateDurationText.value), 1)
              ]),
              isLoggedIn.value ? (openBlock(), createElementBlock("div", _hoisted_5$1, [
                createElementVNode("span", null, "工号: " + toDisplayString(agentId.value), 1)
              ])) : createCommentVNode("", true),
              isLoggedIn.value && outboundNumber.value ? (openBlock(), createElementBlock("div", _hoisted_6$1, [
                createElementVNode("span", null, "外显号码: " + toDisplayString(outboundNumber.value), 1)
              ])) : createCommentVNode("", true)
            ]),
            isLoggedIn.value ? (openBlock(), createElementBlock("div", _hoisted_7$1, [
              createElementVNode("button", {
                class: "btn btn-ready",
                disabled: !canSetReady.value,
                onClick: setAgentReady
              }, " 置闲 ", 8, _hoisted_8$1),
              createElementVNode("button", {
                class: "btn btn-not-ready",
                disabled: !canSetNotReady.value,
                onClick: setAgentNotReady
              }, " 置忙 ", 8, _hoisted_9$1)
            ])) : createCommentVNode("", true)
          ]),
          isLoggedIn.value ? (openBlock(), createElementBlock("div", _hoisted_10$1, [
            createElementVNode("div", _hoisted_11$1, [
              withDirectives(createElementVNode("input", {
                type: "text",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => phoneNumber.value = $event),
                placeholder: "请输入电话号码"
              }, null, 512), [
                [vModelText, phoneNumber.value]
              ]),
              createElementVNode("button", {
                class: "btn btn-call",
                disabled: !phoneNumber.value || !canMakeCall.value,
                onClick: makeCall
              }, " 拨打 ", 8, _hoisted_12$1)
            ]),
            createElementVNode("div", _hoisted_13, [
              createElementVNode("button", {
                class: "btn btn-hangup",
                disabled: !canHangupCall.value,
                onClick: hangupCall
              }, " 挂断 ", 8, _hoisted_14),
              agentState.state !== unref(AgentStateType).HELD && !isMuted.value ? (openBlock(), createElementBlock("button", {
                key: 0,
                class: "btn btn-hold",
                disabled: !canHoldCall.value,
                onClick: holdCall
              }, " 保持 ", 8, _hoisted_15)) : createCommentVNode("", true),
              agentState.state === unref(AgentStateType).HELD ? (openBlock(), createElementBlock("button", {
                key: 1,
                class: "btn btn-retrieve",
                disabled: !canRetrieveCall.value,
                onClick: retrieveCall
              }, " 恢复 ", 8, _hoisted_16)) : createCommentVNode("", true),
              agentState.state !== unref(AgentStateType).MUTE ? (openBlock(), createElementBlock("button", {
                key: 2,
                class: "btn btn-mute",
                disabled: !canMuteCall.value,
                onClick: muteCall
              }, " 静音 ", 8, _hoisted_17)) : createCommentVNode("", true),
              agentState.state === unref(AgentStateType).MUTE ? (openBlock(), createElementBlock("button", {
                key: 3,
                class: "btn btn-unmute",
                disabled: !canUnmuteCall.value,
                onClick: unmuteCall
              }, " 取消静音 ", 8, _hoisted_18)) : createCommentVNode("", true)
            ])
          ])) : createCommentVNode("", true),
          !isLoggedIn.value ? (openBlock(), createElementBlock("div", _hoisted_19, [
            createElementVNode("button", {
              class: "btn btn-login",
              onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("show-login"))
            }, "签入")
          ])) : (openBlock(), createElementBlock("div", _hoisted_20, [
            createElementVNode("button", {
              class: "btn btn-logout",
              disabled: !canLogout.value,
              onClick: onLogoutButtonClick
            }, " 签出 ", 8, _hoisted_21)
          ]))
        ]),
        expanded.value && latestEvent.value ? (openBlock(), createElementBlock("div", _hoisted_22, [
          createElementVNode("div", _hoisted_23, [
            createElementVNode("span", _hoisted_24, toDisplayString(latestEvent.value.type), 1),
            createElementVNode("span", _hoisted_25, toDisplayString(latestEvent.value.content), 1)
          ])
        ])) : createCommentVNode("", true)
      ], 2);
    };
  }
});
const CCBarToolbar_vue_vue_type_style_index_0_scoped_f62b190b_lang = "";
const CCBarToolbarComponent = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-f62b190b"]]);
const _hoisted_1 = { class: "login-dialog" };
const _hoisted_2 = { class: "login-header" };
const _hoisted_3 = { class: "login-body" };
const _hoisted_4 = { class: "login-form" };
const _hoisted_5 = { class: "form-group" };
const _hoisted_6 = { class: "form-group skills-group" };
const _hoisted_7 = { class: "skills-container" };
const _hoisted_8 = { class: "skill-label" };
const _hoisted_9 = ["value"];
const _hoisted_10 = { class: "login-footer" };
const _hoisted_11 = {
  key: 0,
  class: "error-message"
};
const _hoisted_12 = ["disabled"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "CCBarLogin",
  props: {
    config: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    skills: {
      type: Array,
      required: true
    }
  },
  emits: ["close", "login-success"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const ccbarService = CCBarService.getInstance(props.config);
    const loading = ref(false);
    const errorMessage = ref("");
    ref(false);
    const loginForm = reactive({
      agentId: "",
      password: "",
      phone: "55552013",
      // 默认话机号
      entId: "",
      loginKey: "",
      productId: "",
      workMode: "preview",
      readyMode: "notReady",
      autoAnswer: false,
      force: false,
      selectedSkills: []
      // 技能组ID数组
    });
    onMounted(() => {
      if (props.config) {
        loginForm.entId = props.config.entId || "";
        loginForm.loginKey = props.config.loginKey || "";
        loginForm.productId = props.config.productId || "";
      }
    });
    const handleLogin = async () => {
      try {
        loading.value = true;
        errorMessage.value = "";
        const loginParams = {
          username: loginForm.agentId,
          password: loginForm.password,
          phone: loginForm.phone,
          phoneType: "softphone",
          loginType: 0,
          readyMode: loginForm.readyMode,
          workMode: loginForm.workMode,
          autoAnswer: loginForm.autoAnswer,
          force: loginForm.force,
          skillId: loginForm.selectedSkills.join(";")
        };
        const result = await ccbarService.login(loginParams);
        if (result.state) {
          localStorage.setItem("phone", loginForm.phone);
          emit("login-success", {
            agentId: loginForm.agentId || localStorage.getItem("agentId") || "",
            phone: loginForm.phone || localStorage.getItem("phone") || ""
          });
          emit("close");
        } else {
          errorMessage.value = `登录失败: ${result.msg || "未知错误"}`;
        }
      } catch (error) {
        errorMessage.value = `登录异常: ${error.message || "未知错误"}`;
        console.error("登录异常:", error);
      } finally {
        loading.value = false;
      }
    };
    const getSkillValue = (skill) => {
      return typeof skill === "object" ? skill.SKILL_GROUP_ID || skill.value || "" : String(skill);
    };
    const getSkillName = (skill) => {
      return typeof skill === "object" ? skill.SKILL_GROUP_NAME || skill.label || "" : String(skill);
    };
    return (_ctx, _cache) => {
      return __props.visible ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: "login-overlay",
        onClick: _cache[3] || (_cache[3] = withModifiers(($event) => _ctx.$emit("close"), ["self"]))
      }, [
        createElementVNode("div", _hoisted_1, [
          createElementVNode("div", _hoisted_2, [
            _cache[4] || (_cache[4] = createElementVNode("h3", null, "话务员登录", -1)),
            createElementVNode("button", {
              class: "close-btn",
              onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("close"))
            }, "×")
          ]),
          createElementVNode("div", _hoisted_3, [
            createElementVNode("div", _hoisted_4, [
              createElementVNode("div", _hoisted_5, [
                _cache[5] || (_cache[5] = createElementVNode("label", null, "话机号码", -1)),
                withDirectives(createElementVNode("input", {
                  type: "text",
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => loginForm.phone = $event),
                  placeholder: "请输入话机号"
                }, null, 512), [
                  [vModelText, loginForm.phone]
                ])
              ]),
              createElementVNode("div", _hoisted_6, [
                _cache[6] || (_cache[6] = createElementVNode("label", null, "技能组选择", -1)),
                createElementVNode("div", _hoisted_7, [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(props.skills, (skill, index) => {
                    return openBlock(), createElementBlock("div", {
                      key: index,
                      class: "skill-item"
                    }, [
                      createElementVNode("label", _hoisted_8, [
                        withDirectives(createElementVNode("input", {
                          type: "checkbox",
                          value: getSkillValue(skill),
                          "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => loginForm.selectedSkills = $event)
                        }, null, 8, _hoisted_9), [
                          [vModelCheckbox, loginForm.selectedSkills]
                        ]),
                        createElementVNode("span", null, toDisplayString(getSkillName(skill)), 1)
                      ])
                    ]);
                  }), 128))
                ])
              ])
            ])
          ]),
          createElementVNode("div", _hoisted_10, [
            errorMessage.value ? (openBlock(), createElementBlock("div", _hoisted_11, toDisplayString(errorMessage.value), 1)) : createCommentVNode("", true),
            createElementVNode("button", {
              class: "login-btn",
              onClick: handleLogin,
              disabled: loading.value
            }, toDisplayString(loading.value ? "登录中..." : "登录"), 9, _hoisted_12)
          ])
        ])
      ])) : createCommentVNode("", true);
    };
  }
});
const CCBarLogin_vue_vue_type_style_index_0_scoped_55a2e1f8_lang = "";
const CCBarLoginComponent = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-55a2e1f8"]]);
const CCBar = {
  install(app, options) {
    app.component("CCBar", CCBarComponent);
    app.component("CCBarToolbar", CCBarToolbarComponent);
    app.component("CCBarLogin", CCBarLoginComponent);
    app.component("cc-bar", CCBarComponent);
    app.component("cc-bar-toolbar", CCBarToolbarComponent);
    app.component("cc-bar-login", CCBarLoginComponent);
    if (options && options.baseURL) {
      const service = CCBarService.getInstance(options);
      this.service = service;
    }
  },
  name: "CCBar",
  version: "1.0.0",
  CCBarComponent,
  CCBarToolbar: CCBarToolbarComponent,
  CCBarLogin: CCBarLoginComponent,
  // 添加getInstance方法，直接代理到CCBarService.getInstance
  getInstance: (config) => {
    return CCBarService.getInstance(config);
  },
  // 添加常用方法，全部代理到service实例
  login: (params) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用login前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.login(params);
  },
  logout: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用logout前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.logout();
  },
  agentReady: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用agentReady前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.agentReady();
  },
  agentNotReady: (busyType) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用agentNotReady前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.agentNotReady(busyType);
  },
  makeCall: (phoneNumber, displayNumber, userData = {}, callType = 2) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用makeCall前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.makeCall(phoneNumber, displayNumber, userData, callType);
  },
  answerCall: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用answerCall前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.answerCall();
  },
  clearCall: (callData) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用clearCall前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.clearCall(callData);
  },
  holdCall: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用holdCall前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.holdCall();
  },
  unholdCall: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用unholdCall前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.unholdCall();
  },
  getState: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用getState前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.getState();
  },
  getAgentInfo: () => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用getAgentInfo前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    return CCBar.service.getAgentInfo();
  },
  on: (eventName, callback) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用on前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    CCBar.service.on(eventName, callback);
  },
  off: (eventName, callback) => {
    if (!CCBar.service) {
      console.error("[CCBar] 在调用off前必须先初始化service实例");
      throw new Error("CCBar service not initialized");
    }
    CCBar.service.off(eventName, callback);
  }
};
if (typeof window !== "undefined") {
  window.CCBar = CCBar;
}
export {
  AgentStateType,
  AppStateType,
  CCBar,
  CCBarComponent,
  CCBarLoginComponent as CCBarLogin,
  CCBarService,
  CCBarToolbarComponent as CCBarToolbar,
  CallEventType,
  FuncMaskType,
  NotifyEventType,
  WorkModeType,
  ccbarDebugger,
  debounce,
  CCBar as default,
  formatDate,
  replacePhoneNum,
  throttle,
  tipsMsg
};
//# sourceMappingURL=ccbar.es.js.map
