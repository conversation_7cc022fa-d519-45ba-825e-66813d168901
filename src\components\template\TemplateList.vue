<template>
  <div class="content-box">
    <div class="filter-container">
      <div class="filter-item">
        <span>时间</span>
        <el-date-picker
          v-model="filterParams.startTime"
          type="datetime"
          clearable
          placeholder="开始时间"
          format="YYYY-MM-DD HH:mm:ss"
        ></el-date-picker>
        <span class="filter-item-separator">-</span>
        <el-date-picker
          v-model="filterParams.endTime"
          type="datetime"
          clearable
          placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
        ></el-date-picker>
      </div>
      <div class="filter-item">
        <span>模板类型</span>
        <el-select
          v-model="filterParams.templateType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in templateTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="filter-item">
        <span>模板名称</span>
        <el-input
          v-model="filterParams.templateName"
          clearable
          placeholder="请输入"
        ></el-input>
      </div>
      <div class="filter-item">
        <span>创建人</span>
        <el-input
          v-model="filterParams.createBy"
          clearable
          placeholder="请输入"
        ></el-input>
      </div>
      <div class="filter-buttons">
        <div class="filter-button" @click="handleSearch"></div>
        <div class="filter-button" @click="handleReset"></div>
      </div>
    </div>
    <div class="history-record-info">
      <div class="history-record-list" v-if="templateList?.length">
        <div
          v-for="(item, index) in templateList"
          :key="index"
          class="history-record-item"
        >
          <div
            class="item-left"
            :class="
              item.TEMPLATE_TYPE == 1
                ? 'sms'
                : item.TEMPLATE_TYPE == 2
                ? 'call'
                : 'assistant'
            "
          ></div>
          <div class="item-right">
            <div class="phone-number">
              <span class="fontStyle">{{ item.TEMPLATE_NAME }}</span>
              <div
                class="alarm-type"
                :class="
                  item.TEMPLATE_TYPE == 1
                    ? 'type-sms'
                    : item.TEMPLATE_TYPE == 2
                    ? 'type-call'
                    : 'type-assistant'
                "
              ></div>
            </div>

            <div class="call-info">
              <div class="time">
                <span class="label">创建人：</span>
                <span class="value">{{ item.CREATED_BY || "--" }}</span>
              </div>
              <div class="time">
                <span class="label">创建时间：</span>
                <span class="value number">{{
                  item.CREATED_TIME || "--"
                }}</span>
              </div>
              <div class="time">
                <span class="label">修改人：</span>
                <span class="value">{{ item.UPDATED_BY || "--" }}</span>
              </div>
              <div class="time">
                <span class="label">修改时间：</span>
                <span class="value number">{{
                  item.UPDATED_TIME || "--"
                }}</span>
              </div>
            </div>

            <div class="btnBox">
              <div class="btn delete" @click="handleDelete(item)">删除</div>
              <div class="btn" @click="handleTransfer(item)">详情</div>
              <div class="btn" @click="handleDeal(item)">修改</div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <img src="../../assets/image/empty.png" alt="" class="empty-icon" />
        <div class="empty-text">暂无数据</div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        :current-page="Number(filterParams.pageIndex)"
        :page-size="Number(filterParams.pageSize)"
        :page-sizes="[10, 15, 30, 50]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="templateTotal"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteTemplate } from "../../api/index";
const props = defineProps({
  templateList: {
    type: Array,
    default: () => [],
  },
  templateTotal: {
    type: Number,
    default: 0,
  },
  templateParams: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits([
  "params-change",
  "edit-template",
  "view-template-detail",
]);

// 过滤参数
const filterParams = reactive({
  pageType: "1",
  pageIndex: "1",
  pageSize: "10",
  startTime: "",
  endTime: "",
  templateType: "",
  templateName: "",
  createBy: "",
});

// 监听传入的参数变化
watch(
  () => props.templateParams,
  (newVal) => {
    if (newVal) {
      Object.assign(filterParams, newVal);
    }
  },
  { deep: true, immediate: true }
);

// 模板类型选项
const templateTypeOptions = ref([
  { value: "1", label: "短信通知模板" },
  { value: "2", label: "外呼通知模板" },
  { value: "0", label: "助手消息模板" },
]);

// 处理查询
const handleSearch = () => {
  filterParams.pageIndex = "1";
  emit("params-change", { ...filterParams });
};

// 处理重置
const handleReset = () => {
  filterParams.startTime = "";
  filterParams.endTime = "";
  filterParams.templateType = "";
  filterParams.templateName = "";
  filterParams.createBy = "";
  filterParams.pageIndex = "1";

  emit("params-change", { ...filterParams });
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  filterParams.pageSize = String(size);
  emit("params-change", { ...filterParams });
};

// 处理当前页变化
const handleCurrentChange = (page) => {
  filterParams.pageIndex = String(page);
  emit("params-change", { ...filterParams });
};

// 处理编辑
const handleDeal = (row) => {
  console.log("修改模板", row);
  emit("edit-template", {
    id: row.ID,
    templateType: row.TEMPLATE_TYPE,
  });
};

// 处理详情
const handleTransfer = (row) => {
  console.log("查看模板详情", row);
  emit("view-template-detail", {
    id: row.ID,
    templateType: row.TEMPLATE_TYPE,
  });
};

// 处理删除
const handleDelete = (row) => {
  console.log("删除模板", row);
  ElMessageBox.confirm(`是否确认删除模板"${row.TEMPLATE_NAME}"?`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteTemplate({ templateId: row.ID, templateType: row.TEMPLATE_TYPE })
        .then((res) => {
          if (res.data.state == 1) {
            ElMessage.success("删除成功");
            // 通知父组件刷新列表
            emit("params-change", { ...filterParams });
          } else {
            ElMessage.error(res.data.msg || "删除失败");
          }
        })
        .catch((error) => {
          ElMessage.error(error.message || "删除失败");
        });
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

onMounted(() => {
  // 初始化时如果有模板参数，赋值到过滤条件
  if (props.templateParams) {
    Object.assign(filterParams, props.templateParams);
  }
});
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  .filter-container {
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    .filter-item {
      display: flex;
      align-items: center;
      color: #fff;
      .filter-item-separator {
        margin-left: 4px;
      }
      span {
        margin-right: 4px;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
      }

      :deep(.el-input) {
        width: 140px !important;
      }

      :deep(.el-select) {
        width: 140px !important;
      }

      :deep(.el-date-editor.el-input),
      :deep(.el-date-editor--datetime) {
        width: 160px !important;
      }

      :deep(.el-input__wrapper) {
        width: 100%;
      }

      :deep(.el-input__inner::placeholder) {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .filter-buttons {
      display: flex;
      gap: 8px;

      .filter-button {
        width: 80px;
        height: 32px;

        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        &:active {
          transform: scale(0.9);
        }
        &:nth-child(1) {
          background: url("../../assets/image/search.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        &:nth-child(2) {
          background: url("../../assets/image/reset.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
      }
    }
  }

  .history-record-info {
    width: 1212px;
    height: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    .history-record-list {
      margin-top: 16px;
      flex: 1;

      .history-record-item {
        background: rgba(0, 128, 255, 0.1);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;

        .item-left {
          margin-right: 16px;
          width: 64px;
          height: 64px;
        }
        .assistant {
          background: url("../../assets/image/zsicon.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        .sms {
          background: url("../../assets/image/dxicon.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        .call {
          background: url("../../assets/image/callicon.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        .item-right {
          flex: 1;

          .phone-number {
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 8px;
            display: flex;
            gap: 12px;
            align-items: center;
            span {
              font-size: 24px;
            }
          }

          .call-info {
            display: flex;
            gap: 24px;

            .time {
              .label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
              }
              .value {
                font-size: 14px;
                font-weight: normal;
                text-align: center;
                letter-spacing: normal;
                color: #ffffff;
              }
            }
          }
        }

        .alarm-type {
          width: 100px;
          height: 26px;
        }
        .type-sms {
          background: url("../../assets/image/dxtztag.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        .type-call {
          background: url("../../assets/image/whtztag.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
        .type-assistant {
          background: url("../../assets/image/zsxxtag.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
      }
      .active {
        background: rgba(255, 220, 0, 0.2) !important;
        box-sizing: border-box;
        /* 辅助色/湘叶 */
        border: 1px solid #ffdc00 !important;
        box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72) !important;
        position: relative;
      }
    }

    .empty-state {
      margin-top: 20vh;
      text-align: center;

      .empty-icon {
        width: 308px;
        height: 252px;
      }

      .empty-text {
        font-size: 14px;
        color: #fff;
      }
    }

    .btnBox {
      display: flex;
      gap: 8px;
      position: absolute;
      bottom: 16px;
      right: 16px;
      .btn {
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        padding: 5px 16px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);

        &:hover {
          background: rgba(0, 128, 255, 0.2);
        }
      }
      .delete {
        background: rgba(220, 0, 85, 0.2);
        color: #dc0055;
        &:hover {
          background: rgba(220, 0, 85, 0.3) !important;
        }
      }
    }
  }
}

.pagination {
  position: relative;
  bottom: 0;
  margin-bottom: 8px;
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
  :deep(.el-pagination) {
    .el-input__inner {
      color: #fff;
    }
    .is-active {
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 128, 255, 0.3) 2%,
        rgba(0, 128, 255, 0.2) 100%
      ) !important;
      background-color: none !important;
      box-sizing: border-box;
      border: 1px solid #0080ff;
      box-shadow: inset 0px 0px 12px 0px #0080ff;
    }
    .el-pagination__total {
      color: #fff;
    }
    .el-pagination__jump {
      color: #fff;
    }

    .el-select {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      height: 32px;
      color: rgba(0, 255, 255, 1);
    }
    .el-select__selected-item {
      color: rgba(0, 255, 255, 1);
    }

    .el-pager {
      gap: 8px;
      margin-left: 8px;
      margin-right: 8px;
    }
    .number,
    .more {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
    .btn-prev,
    .btn-next {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
  }
}
</style>
<style lang="scss">
.el-date-picker__header,
.el-date-picker__time-header,
.el-date-table-cell,
.el-picker-panel__body-wrapper,
.el-picker-panel__footer {
  background: rgba(12, 33, 75, 1) !important;
}
.el-picker-panel__content {
  th {
    background: rgba(12, 33, 75, 1) !important;
  }
}

.el-picker-panel__footer {
  border-top: 1px solid rgba(0, 128, 255, 0.2) !important;
}
.el-date-picker__time-header {
  border-bottom: 1px solid rgba(0, 128, 255, 0.2) !important;
}

.current {
  .el-date-table-cell {
    .el-date-table-cell__text {
      color: #00ffff !important;
    }
  }
}

/* 确保清除按钮不影响宽度 */
.el-input__suffix {
  flex-shrink: 0;
}

.el-input__suffix-inner {
  display: inline-flex;
}

.el-input__clear {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

/* 下拉菜单样式 */
.el-select-dropdown {
  background-color: rgba(12, 33, 75, 1) !important;
  border: 1px solid rgba(0, 128, 255, 0.3) !important;

  .el-select-dropdown__item {
    color: #ffffff !important;

    &.hover,
    &:hover {
      background-color: rgba(0, 128, 255, 0.2) !important;
    }

    &.selected {
      background-color: rgba(0, 255, 255, 0.1) !important;
      color: var(--el-color-primary) !important;
    }
  }
}

/* 输入框样式 */
.el-input__wrapper {
  background-color: rgba(0, 128, 255, 0.1) !important;
  box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.3) inset !important;

  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
  }
}

/* 按钮样式 */
.el-button {
  &.el-button--primary {
    background-color: rgba(0, 128, 255, 0.8) !important;
    border-color: rgba(0, 128, 255, 0.9) !important;

    &:hover,
    &:focus {
      background-color: rgba(0, 128, 255, 1) !important;
      border-color: rgba(0, 128, 255, 1) !important;
    }
  }

  &:not(.el-button--primary) {
    background-color: rgba(0, 128, 255, 0.1) !important;
    border-color: rgba(0, 128, 255, 0.3) !important;
    color: #ffffff !important;

    &:hover,
    &:focus {
      background-color: rgba(0, 128, 255, 0.2) !important;
      border-color: rgba(0, 128, 255, 0.4) !important;
    }
  }
}

/* 日期选择器样式 */
.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: transparent;
  color: #ffc400 !important;
  border-radius: 4px;
  background: rgba(255, 220, 0, 0.2);
  box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
}

.el-date-table td.today .el-date-table-cell__text {
  color: var(--el-color-primary) !important;
  font-weight: bold;
}
</style>
