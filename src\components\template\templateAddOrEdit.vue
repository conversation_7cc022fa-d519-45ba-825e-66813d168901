<template>
  <div class="template-add-or-edit">
    <div class="template-type-box">
      <div class="template-type-box-title">
        <img src="../../assets/image/titleLeft.png" alt="" />
        <div class="fontStyle">模板类型</div>
      </div>
      <div class="template-type-box-content">
        <div
          class="template-type-box-content-item"
          v-for="item in templateTypeList"
          :key="item.value"
          :class="{ active: templateType === item.value }"
          @click="handleTemplateType(item.value)"
        >
          <img
            class="activeRT"
            src="../../assets/image/active-right-top.png"
            alt=""
            v-if="templateType === item.value"
          />
          <div class="template-type-box-content-item-title">
            {{ item.label }}
          </div>
          <div class="template-type-box-content-item-icon">
            <img
              v-if="templateType === item.value"
              :src="item.activeIcon"
              alt=""
            />
            <img v-else :src="item.icon" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="template-content-box">
      <div class="template-content-box-title">
        <img src="../../assets/image/titleLeft.png" alt="" />
        <div class="fontStyle">模板内容</div>
      </div>
      <div class="template-content-box-content">
        <div class="fromBox">
          <div class="fromItem">
            <div class="fromItem-title">模板名称</div>
            <div class="fromItem-input">
              <el-input
                type="text"
                placeholder="请输入"
                v-model="templateName"
              />
            </div>
          </div>
          <div class="fromItem" v-if="templateType === '0'">
            <div class="fromItem-title">模板主题</div>
            <div class="fromItem-input">
              <el-input
                type="text"
                placeholder="请输入"
                v-model="templateSubject"
              />
            </div>
          </div>
          <div class="fromItem" v-if="templateType === '1'">
            <div class="fromItem-title">模板id</div>
            <div class="fromItem-input">
              <el-input
                type="text"
                placeholder="请输入"
                v-model="smsTemplateId"
              />
            </div>
          </div>
          <div class="fromItem">
            <div class="fromItem-title">模板内容</div>
            <div class="fromItem-input">
              <el-input
                type="textarea"
                placeholder="请输入"
                show-word-limit
                :maxlength="templateType == 0 ? 50 : templateType == 1 ? 100 : 200"
                :rows="4"
                v-model="detail"
              />
            </div>
            <div class="fromItem" v-if="templateType != '0'">
              <div class="fromItem-add" @click="addParamDialogVisible = true">
                <div class="fontStyle">添加参数</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="template-message-box" v-if="templateType === '0'">
      <div class="template-message-box-title">
        <img src="../../assets/image/titleLeft.png" alt="" />
        <div class="fontStyle">消息类型</div>
      </div>
      <div class="template-message-box-content">
        <div
          class="template-message-box-content-item"
          v-for="item in msgTypeList"
          :key="item.value"
          :class="{ active: msgType === item.value }"
          @click="handlemsgType(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="template-footer">
      <div class="template-footer-close" @click="emit('close')">
        <img src="../../assets/image/closeBtn.png" alt="" />
      </div>
      <div class="template-footer-sumbit" @click="handleConfirm">
        <div class="fontStyle">确定</div>
      </div>
    </div>

    <addParamDialog
      ref="addParamDialogRef"
      :visible="addParamDialogVisible"
      @close="addParamDialogVisible = false"
      @addParam="handleAddParam"
    />
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from "vue";
import callInfo from "../../assets/image/call-icon.png";
import smsInfo from "../../assets/image/sms-icon.png";
import assistantInfo from "../../assets/image/assistant-icon.png";
import callInfoActive from "../../assets/image/call-icon-active.png";
import smsInfoActive from "../../assets/image/sms-icon-active.png";
import assistantInfoActive from "../../assets/image/assistant-icon-active.png";
import addParamDialog from "./addParamDialog.vue";
import { addTemplate, getTemplateDetail } from "../../api/index";
import { ElMessage } from "element-plus";

const props = defineProps({
  templateId: {
    type: String,
    default: "",
  },
  templateType: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["close", "confirm"]);
console.log(emit, "emit");
const addParamDialogRef = ref(null);
const addParamDialogVisible = ref(false);

const templateType = ref("0");
const msgType = ref("1");

const templateName = ref("");
const templateSubject = ref("");
const smsTemplateId = ref("");
const detail = ref("");
const isEdit = ref(false); // 判断是新增还是修改

const templateTypeList = ref([
  {
    label: "助手消息模板",
    value: "0",
    icon: assistantInfo,
    activeIcon: assistantInfoActive,
  },
  {
    label: "短信通知模板",
    value: "1",
    icon: smsInfo,
    activeIcon: smsInfoActive,
  },
  {
    label: "外呼通知模板",
    value: "2",
    icon: callInfo,
    activeIcon: callInfoActive,
  },
]);

const msgTypeList = ref([
  {
    label: "公告",
    value: "6",
  },
  {
    label: "话后超时",
    value: "0",
  },
  {
    label: "超长通话",
    value: "1",
  },
  {
    label: "语速过快",
    value: "2",
  },
  {
    label: "求助",
    value: "3",
  },
  {
    label: "转派",
    value: "5",
  },
  {
    label: "静默",
    value: "7",
  },
  {
    label: "抢话",
    value: "8",
  },
  {
    label: "坐席违规词",
    value: "9",
  },
  {
    label: "市民敏感词",
    value: "10",
  },
  {
    label: "其他",
    value: "4",
  },
]);
// 监听传入的模板类型
watch(
  () => props.templateType,
  (newVal) => {
    if (newVal) {
      templateType.value = newVal;
    }
  },
  { immediate: true }
);

const handleAddParam = (param) => {
  console.log(param, "param");
  if (param && param.paramName) {
    if (param.type === 0) {
      detail.value += `{${param.paramName}}`;
    } else {
      detail.value += `[${param.paramName}]`;
    }
  }
};

const handleTemplateType = (value) => {
  if(value!==templateType.value){
    detail.value = "";
  }
  templateType.value = value;
};

const handlemsgType = (value) => {
  msgType.value = value;
};

// 获取模板详情
const getTemplateDetailData = async (id) => {
  try {
    const res = await getTemplateDetail({
      templateId: id,
      templateType: props.templateType,
    });
    console.log(res, "res");
    const templateData = res.data.data;
    // 如果没有传入模板类型或者需要覆盖传入的模板类型，则使用接口返回的模板类型
    if (
      !props.templateType ||
      templateType.value !== templateData.TEMPLATE_TYPE
    ) {
      templateType.value = templateData.TEMPLATE_TYPE || "0";
    }
    templateName.value = templateData.TEMPLATE_NAME || "";
    templateSubject.value = templateData.TEMPLATE_SUBJECT || "";
    smsTemplateId.value = templateData.SMS_TEMPLATE_ID || "";
    detail.value = templateData.DETAIL || "";
    msgType.value = templateData.MESSAGE_TYPE || "1";
    isEdit.value = true;
  } catch (error) {
    ElMessage.error(error.message || "获取模板详情失败");
  }
};

// 监听模板ID变化
watch(
  () => props.templateId,
  (newVal) => {
    if (newVal) {
      getTemplateDetailData(newVal);
    }
  },
  { immediate: true }
);

const handleConfirm = async () => {
  if (!templateName.value) {
    ElMessage.error("请输入模板名称");
    return;
  }
  if (templateType.value === "0" && !templateSubject.value) {
    ElMessage.error("请输入模板主题");
    return;
  }
  if (templateType.value === "1" && !smsTemplateId.value) {
    ElMessage.error("请输入模板id");
    return;
  }
  if (!detail.value) {
    ElMessage.error("请输入模板内容");
    return;
  }
  if (!msgType.value) {
    ElMessage.error("请选择消息类型");
    return;
  }

  const data = {
    templateName: templateName.value,
    templateSubject: templateSubject.value,
    smsTemplateId: smsTemplateId.value,
    detail: detail.value,
    templateType: templateType.value,
    msgType: msgType.value,
  };

  // 如果是编辑模式，添加模板ID
  if (props.templateId) {
    data.id = props.templateId;
  }

  try {
    const res = await addTemplate(data);
    console.log(res, "res");
    if (res.data.state == 1) {
      ElMessage.success(isEdit.value ? "模板修改成功" : "模板添加成功");
      // 通知父组件操作成功，需要刷新列表
      emit("confirm", { success: true, isEdit: isEdit.value });
      emit("close");
    } else {
      ElMessage.error(res.data.msg);
    }
  } catch (error) {
    ElMessage.error(
      error.message || (isEdit.value ? "模板修改失败" : "模板添加失败")
    );
  }
};
</script>

<style lang="scss" scoped>
.template-add-or-edit {
  width: 652px;
  height: 100%;

  .template-type-box {
    margin-bottom: 24px;

    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }
    &-content {
      display: flex;
      justify-content: space-between;
      &-item {
        width: 206px;
        height: 82px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.2);
        box-sizing: border-box;
        border: 1px solid rgba(0, 255, 255, 0.2);
        line-height: 82px;
        padding-left: 16px;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        color: #ffffff;
        position: relative;
        cursor: pointer;
        &-icon {
          width: 80px;
          height: 80px;
          position: absolute;
          right: 0;
          bottom: 0;
          user-select: none;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .active {
        border-radius: 4px;
        background: rgba(255, 220, 0, 0.2);
        box-sizing: border-box;
        box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
        border: 1px solid #fff197;
        color: #ffdc00;
      }
    }

    .activeRT {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .template-content-box {
    margin-bottom: 24px;
    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }
    &-content {
      display: flex;
      justify-content: space-between;
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      padding: 16px;
      .fromBox {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 24px;
        .fromItem {
          width: 100%;
          .fromItem-title {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            letter-spacing: normal;
            color: #ffffff;
            margin-bottom: 10px;
          }
          .fromItem-input {
            width: 100%;
          }
          .fromItem-add {
            margin-top: 10px;
            display: inline-block;
            border-radius: 4px;
            background: linear-gradient(
              180deg,
              rgba(0, 255, 255, 0.3) 2%,
              rgba(0, 255, 255, 0.2) 100%
            );
            box-sizing: border-box;
            border: 1px solid #00ffff;
            box-shadow: inset 0px 0px 12px 0px #00ffff;
            padding: 5px 24px;
            font-size: 14px;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
            &:active {
              transform: scale(0.98);
            }
          }
        }
      }
    }
  }
  .template-message-box {
    &-title {
      display: flex;
      align-items: center;
      margin-left: -6px;
      margin-bottom: 10px;
      .fontStyle {
        font-size: 18px;
        font-weight: bold;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }

    &-content {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      &-item {
        width: 151px;
        height: 48px;
        box-sizing: border-box;
        cursor: pointer;
        font-family: Alibaba PuHuiTi 3;
        font-size: 16px;
        font-weight: bold;
        line-height: 48px;
        letter-spacing: normal;
        color: #ffffff;
        padding-left: 50px;
        &.active {
          color: #ffdc00;
        }
        &:nth-child(1) {
          background: url("../../assets/image/type-gg.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-gg-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(2) {
          background: url("../../assets/image/type-hhcs.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-hhcs-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(3) {
          background: url("../../assets/image/type-ccth.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ccth-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(4) {
          background: url("../../assets/image/type-ysgk.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ysgk-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(5) {
          background: url("../../assets/image/type-jm.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-jm-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(6) {
          background: url("../../assets/image/type-qh.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-qh-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(7) {
          background: url("../../assets/image/type-wgc.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-wgc-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(8) {
          background: url("../../assets/image/type-qt.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-qt-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(9) {
          background: url("../../assets/image/type-gg.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-gg-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(10) {
          background: url("../../assets/image/type-wgc.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-wgc-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
        &:nth-child(11) {
          background: url("../../assets/image/type-ccth.png") no-repeat center
            center;
          background-size: 100% 100%;
          &.active {
            background: url("../../assets/image/type-ccth-active.png") no-repeat
              center center;
            background-size: 100% 100%;
          }
        }
      }
    }
  }

  .template-footer {
    // height: 72px;
    position: absolute;
    bottom: 16px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    &-close {
      width: 112px;
      height: 40px;
      cursor: pointer;
      margin-right: 16px;
      &:hover {
        opacity: 0.8;
      }
      &:active {
        transform: scale(0.98);
      }
      img {
        width: 100%;
      }
    }
    &-sumbit {
      width: 112px;
      height: 40px;
      //   padding: 8px 40px;
      text-align: center;
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 255, 255, 0.3) 2%,
        rgba(0, 255, 255, 0.2) 100%
      );
      box-sizing: border-box;
      border: 1px solid #00ffff;
      box-shadow: inset 0px 0px 12px 0px #00ffff;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
      &:active {
        transform: scale(0.98);
      }
      .fontStyle {
        font-size: 16px;
        line-height: 40px;
        font-weight: normal;
        color: #fff;
      }
    }
  }
}
:deep(.el-textarea__inner) {
  background: rgba(0, 128, 255, 0.2);
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}
:deep(.el-input__count) {
  background: transparent;
}
:deep(.el-input__inner) {
  color: #fff;
}
:deep(.el-textarea__inner) {
  color: #fff;
}
</style>
