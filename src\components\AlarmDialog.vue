<template>
  <div class="dialog-mask" @click.stop="handleClose" v-if="visible"></div>
  <div class="dialog-content" v-if="visible">
    <div class="dialog-content-top">
      <div class="close-btn" @click="handleClose">
        <img src="../assets/image/close.png" alt="关闭" />
        <span class="fontStyle">关闭</span>
      </div>
      <BaseTitle>{{
        dealObj.type === "deal" ? "告警处理" : "转派求助"
      }}</BaseTitle>
    </div>
    <div class="dialog-content-middle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef">
        <el-form-item
          label="告警类型"
          prop="alarmType"
          v-if="dealObj.type === 'deal'"
        >
          <el-select
            v-model="ruleForm.alarmType"
            @change="handleTypeChange"
            disabled
            placeholder="请选择告警类型"
          >
            <el-option
              v-for="(label, value) in types"
              :key="value"
              :label="label"
              :value="Number(value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="处理方式"
          prop="sendAgent"
          v-if="dealObj.type === 'deal'"
        >
          <div class="radioBox">
            <div
              class="radioItem"
              :class="{ active: ruleForm.sendAgent === 0 }"
              @click="handleRadioChange(0)"
            >
              <div class="fontStyle">仅处理</div>
            </div>
            <div
              class="radioItem"
              :class="{ active: ruleForm.sendAgent === 1 }"
              @click="handleRadioChange(1)"
            >
              <div class="fontStyle">处理并通知</div>
            </div>
          </div>
        </el-form-item>

        <template v-if="ruleForm.sendAgent === 1 && dealObj.type === 'deal'">
          <el-form-item label="消息模板" prop="msgTemp">
            <el-select
              v-model="ruleForm.msgTemp"
              placeholder="请选择"
              @change="handleTempChange"
            >
              <el-option
                v-for="(key, value) in tempList"
                :key="key"
                :label="key"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>

        <template v-if="dealObj.type === 'transfer'">
          <el-form-item label="转派人员" prop="agentId">
            <el-select
              v-model="ruleForm.agentId"
              placeholder="请选择"
              @change="handleAgentChange"
            >
              <el-option
                v-for="(item, index) in userList"
                :key="index"
                :label="item.AGENT_NAME"
                :value="item.USER_ID"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="dialog-content-bottom">
      <div class="closeBtn" @click="handleClose"></div>
      <!-- 告警处理 -->
      <div
        class="submitBtn"
        @click="handleSubmit"
        v-if="dealObj.type === 'deal'"
      ></div>
      <!-- 转派求助 -->
      <div
        class="submitBtn"
        @click="handleTransfer"
        v-if="dealObj.type === 'transfer'"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from "vue";
import {
  getWarnTemp,
  queryUserAcc,
  handleAlarm,
  transferWarn,
} from "../api/index";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  dealObj: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits([
  "update:visible",
  "update:ruleForm",
  "close",
  "submit",
  "refresh-backlog", // 添加刷新待办列表的事件
]);

const handleSubmit = async () => {
  // emit("submit", { ...ruleForm.value, type: dealObj.value.type });
  const res = await handleAlarm({
    msgId: props.dealObj.MSG_ID, //消息id（和告警类型不能全为空）
    agentId: props.dealObj.AGENT_ID, //坐席id
    alarmType: ruleForm.value.alarmType.toString(), //告警类型（必填）
    hfCode:
      props.dealObj.ROOM_LOCATION ||
      localStorage.getItem("currentHfCode") ||
      "", //话房编码
    sendAgent: ruleForm.value.sendAgent.toString(), //是否通知坐席1通知 0不通知
    msgTemp: ruleForm.value.msgTemp, //通知模版 sendAgent为1是必填
  });
  console.log(res, "res");
  if (res.data.result == 1) {
    ElMessage.success(res.data.desc);
    emit("close");
    emit("refresh-backlog"); // 触发刷新待办列表的事件
  } else {
    ElMessage.error(res.data.desc);
  }
};

const handleTransfer = async () => {
  // emit("submit", { ...ruleForm.value, type: dealObj.value.type });
  const res = await transferWarn({
    msgId: props.dealObj.MSG_ID, //消息id（和告警类型不能全为空）
    hfCode: props.dealObj.ROOM_LOCATION, //话房编码
    agentId: ruleForm.value.agentId, //转派人
    agentNo: props.dealObj.AGENT_ID,
  });
  if (res.data.result == "000") {
    ElMessage.success(res.data.desc);
    emit("close");
    emit("refresh-backlog"); // 触发刷新待办列表的事件
  } else {
    ElMessage.error(res.data.desc);
  }
};
const rules = {
  alarmType: [{ required: true, message: "请选择告警类型", trigger: "change" }],
  sendAgent: [{ required: true, message: "请选择处理方式", trigger: "change" }],
  msgTemp: [{ required: true, message: "请选择消息模板", trigger: "change" }],
  agentId: [{ required: true, message: "请选择转派人", trigger: "change" }],
  msgType: [{ required: true, message: "请选择告警类型", trigger: "change" }],
};

const userList = ref([]);
// 告警类型
const types = {
  1: "超长通话",
  2: "话后超时",
  3: "静音",
  4: "语速过快",
  5: "抢话",
  6: "坐席违规词",
  7: "市民敏感词",
  8: "求助",
};
// 1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民敏感词 8.求助

const tempList = ref({});
const getWarnTempList = async (value) => {
  const res = await getWarnTemp(value);
  console.log(res);
  tempList.value = res.data.data;
};

const ruleForm = ref({
  alarmType: 1,
  sendAgent: 0,
  msgTemp: "",
  agentId: "",
});

const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

const handleTypeChange = (value) => {
  ruleForm.value.alarmType = value;
  getWarnTempList(value);
};

const queryUserAccFn = async () => {
  const res = await queryUserAcc(props.dealObj.MSG_ID);
  userList.value = res.data.data;
  console.log(userList.value, "userList.value");
};
const handleRadioChange = (value) => {
  ruleForm.value.sendAgent = value;

  getWarnTempList(ruleForm.value.alarmType);
};

// 监听 dealObj 变化
watch(
  () => props.dealObj,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      if (newVal.type === "transfer") {
        queryUserAccFn();
      } else if (newVal.type === "deal") {
        getWarnTempList(ruleForm.value.alarmType);
        console.log(newVal, "newVal.alarmType");
        ruleForm.value.alarmType = Number(newVal.ALARM_TYPE);
      }
    }
  },
  { deep: true }
);

// 监听对话框显示状态，当显示时清空消息模板选择
watch(
  () => props.visible,
  (newVal) => {
    if (newVal === true) {
      ruleForm.value.msgTemp = "";
    }
  }
);
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.radioBox {
  display: flex;
  align-items: center;
  gap: 10px;
  .radioItem {
    border-radius: 4px;
    background: rgba(0, 128, 255, 0.2);
    padding: 0px 16px;
    box-sizing: border-box;
    cursor: pointer;
    span {
      font-size: 14px;
    }
  }
  .active {
    border-radius: 4px;
    background: rgba(255, 220, 0, 0.2);
    box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
    .fontStyle {
      letter-spacing: normal;
      background: #ffdc00;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
}
:deep(.el-form) {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.close-btn {
  font-size: 24px;
  cursor: pointer;
  padding: 0 10px;
  position: absolute;
  left: 16px;
  top: 18px;
  display: flex;
  align-items: center;
  gap: 4px;
  span {
    font-size: 18px;
  }
  img {
    width: 24px;
    height: 24px;
  }
}

:deep(.is-disabled) {
  background: rgba(0, 128, 255, 0.2);
}

:deep(.el-select__selected-item) {
  /* 文字/正文 */
  /* 样式描述：正文文本/表单文本/常规组件文本/次级按钮文本 */
  font-family: Alibaba PuHuiTi 3;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.dialog-content {
  width: 700px;
  height: 368px;
  color: #fff;
  position: fixed;
  background: url("../assets/image/dialogBg.png") no-repeat center center;
  background-size: 100% 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;

  .dialog-content-top {
    padding-top: 12px;
    width: 700px;
    justify-content: center;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }

  .dialog-content-middle {
    justify-content: center;
    align-items: center;
    padding: 24px;
    box-sizing: border-box;
  }

  .dialog-content-bottom {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    position: absolute;
    bottom: 24px;
    right: 24px;
    .closeBtn {
      width: 112px;
      height: 40px;
      background: url("../assets/image/closeBtn.png") no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
    .submitBtn {
      width: 112px;
      height: 40px;
      background: url("../assets/image/submitBtn.png") no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 添加对话框淡入淡出动画 */
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: all 0.3s ease-out;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

:deep(.el-dialog) {
  margin-top: 15vh !important;
}

:deep(.el-select) {
  width: 100%;
}
</style>
