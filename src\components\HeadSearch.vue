<template>
  <div class="head">
    <div class="head-title">
      <img
        src="../assets/image/logout.png"
        v-if="isLoggedIn"
        alt=""
        @click="handleSignInOut"
      />
      <img
        src="../assets/image/login.png"
        v-else
        alt=""
        @click="handleSignInOut"
      />
      <el-tag
        type="success"
        class="login-status"
        v-if="isLoggedIn && ccbarState"
      >
        <span>状态：</span>{{ ccbarStateMap[ccbarState] }}
      </el-tag>

      <div
        class="btn add-btn"
        @click="handleSetReady"
        v-if="isLoggedIn && ccbarState == 'BUSY'"
      >
        置闲
      </div>
      <div
        class="btn add-btn"
        @click="handleSetNotReady"
        v-if="isLoggedIn && ccbarState == 'IDLE'"
      >
        置忙
      </div>
      <div
        class="btn clear-btn"
        v-if="isLoggedIn && ccbarState == 'TALK'"
        @click="handleClearCall"
      >
        挂断
      </div>
      <span class="login-label" v-if="isLoggedIn && loginDuration">
        状态持续时间:<span class="login-time">{{ loginDuration }}</span>
      </span>
    </div>
    <div class="form">
      <el-form :model="localForm" ref="form" :inline="true">
        <el-form-item label="关键字">
          <el-input
            v-model="localForm.agentName"
            placeholder="请输入关键字"
            clearable
            @change="handleNameChange"
            class="keyword-input"
          ></el-input>
        </el-form-item>
        <el-form-item style="margin-right: 0">
          <div class="search-btn btn" @click="handleSearch">
            <img src="../assets/image/header/search-icon.png" alt="查找人员" />
            <span>查找人员</span>
          </div>
          <div class="search-btn btn" @click="handleBatch">
            <img src="../assets/image/header/batch-icon.png" alt="批量处理" />
            <span>批量处理</span>
          </div>
          <div
            class="search-btn btn message-btn"
            @mouseleave="showMessageMenu = false"
            @mouseover="showMessageMenu = true"
          >
            <img src="../assets/image/header/message-icon.png" alt="新建消息" />
            <span>新建消息</span>
            <div class="menu-wrapper" v-show="showMessageMenu">
              <div
                class="menu-transition"
                @mouseover="showMessageMenu = true"
              ></div>
              <downBg class="menu-content" :isTop="true">
                <div class="down-menu" @mouseover="showMessageMenu = true">
                  <div
                    class="down-menu-item hover-trigger"
                    @click="handleCreateMessage(0)"
                  >
                    <img src="../assets/image/left/zsxxmx.png" alt="助手消息" />
                    <span class="fontStyle">助手消息</span>
                  </div>
                  <div
                    class="down-menu-item hover-trigger"
                    @click="handleCreateMessage(1)"
                  >
                    <img src="../assets/image/left/dxxfmx.png" alt="短信消息" />
                    <span class="fontStyle">短信消息</span>
                  </div>
                  <div
                    class="down-menu-item hover-trigger"
                    @click="handleCreateMessage(2)"
                  >
                    <img src="../assets/image/left/whtzmx.png" alt="外呼消息" />
                    <span class="fontStyle">外呼消息</span>
                  </div>
                </div>
              </downBg>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!-- 登录弹窗 -->
    <LoginDialog
      v-model:visible="showLoginDialog"
      :skill-groups="skillGroups"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import downBg from "./downBg.vue";
import LoginDialog from "./LoginDialog.vue";
import {
  computed,
  defineEmits,
  defineProps,
  ref,
  onMounted,
  onUnmounted,
} from "vue";
import { useCCBar } from "../composables/useCCBar";
import { ElMessage } from "element-plus";

const { ccbarService, ccbarData } = useCCBar();
const showLoginDialog = ref(false);
const loginStartTime = ref(null);
const loginDuration = ref("");
const durationTimer = ref(null);
const ccbarState = ref("BUSY");
const ccbarStateMap = {
  IDLE: "空闲",
  BUSY: "离席",
  TALK: "通话",
  MONITORED: "监听",
  INTERVENTED: "强插",
  SECRETLYTALK: "密语",
};
// 添加强制更新计数器
const forceUpdate = ref(0);

// 直接使用全局的技能组数据
const skillGroups = computed(() => ccbarData.value.skillGroups);

// 格式化时长显示
const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
  return `${minutes.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

// 更新登录时长
const updateLoginDuration = () => {
  if (loginStartTime.value) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - loginStartTime.value) / 1000);
    loginDuration.value = formatDuration(diffInSeconds);
  }
};

// 开始计时
const startDurationTimer = () => {
  if (durationTimer.value) {
    clearInterval(durationTimer.value);
  }
  durationTimer.value = setInterval(updateLoginDuration, 1000);
};

// 停止计时
const stopDurationTimer = () => {
  if (durationTimer.value) {
    clearInterval(durationTimer.value);
    durationTimer.value = null;
  }
  loginDuration.value = "";
};

const handleSetReady = async () => {
  //置闲或置忙按钮，点击置闲变成置忙，点击置忙变成置闲
  try {
    const result = await ccbarService.agentReady();
    if (result.state) {
      ccbarState.value = "空闲"; // 更新状态为离席
    } else {
      ElMessage.error(`置闲失败: ${result.msg || "未知错误"}`);
    }
  } catch (error) {
    console.error("置闲异常:", error);
    ElMessage.error(`置闲失败: ${error.message || "网络异常"}`);
  }
};

const handleSetNotReady = async () => {
  try {
    // 可以根据需要设置不同的忙碌类型
    // 1: 休息, 2: 会议, 3: 培训, 0: 其他
    const busyType = "1"; // 默认为休息

    const result = await ccbarService.agentNotReady(busyType);
    if (!result.state) {
      ElMessage.error(`置忙失败: ${result.msg || "未知错误"}`);
    }
  } catch (error) {
    console.error("置忙异常:", error);
    ElMessage.error(`置忙失败: ${error.message || "网络异常"}`);
  }
};
const handleClearCall = async () => {
  await ccbarService.clearCall({});
};
// 监听403会话过期事件
ccbarService.on("session:403error", () => {
  isLoggedIn.value = false;
});
// 从SDK获取登录状态 - 添加强制更新依赖
const isLoggedIn = ref(false);

// 监听登录状态变化
const setupEventListeners = () => {
  if (ccbarService?.eventManager) {
    // 监听登录成功事件
    ccbarService.eventManager.on("agent:loginResponse", (event, data) => {
      console.log("收到登录响应事件:", event, data);
      const responseData = data || event;

      if (responseData?.resultCode === "0") {
        console.log("登录成功，强制更新状态");
        forceUpdate.value++;

        // 设置登录时间
        if (responseData.loginTime) {
          loginStartTime.value = new Date(responseData.loginTime);
        } else {
          loginStartTime.value = new Date();
        }
        localStorage.setItem("loginTime", loginStartTime.value.toISOString());
        startDurationTimer();
        updateLoginDuration();
      }
    });

    // 监听状态变化事件
    ccbarService.eventManager.on("agent:stateChanged", (e, data) => {
      console.log("收到状态变化事件", e, data);

      ccbarState.value = data.state;
      isLoggedIn.value = ccbarService.isLoggedIn();

      // 添加这两行：重置状态持续时间
      loginStartTime.value = new Date();
      loginDuration.value = "";
    });

    // 监听登出事件
    ccbarService.eventManager.on("agent:forceLogout", () => {
      isLoggedIn.value = ccbarService.isLoggedIn();
      loginStartTime.value = null;
      stopDurationTimer();
      localStorage.removeItem("loginTime");
    });
  }
};

// 处理签入/签出点击
const handleSignInOut = () => {
  if (isLoggedIn.value) {
    // 执行签出
    console.log("执行签出", isLoggedIn.value);
    handleSignOut();
  } else {
    // 显示签入对话框
    showLoginDialog.value = true;
  }
};

// 处理签出
const handleSignOut = async () => {
  try {
    const result = await ccbarService.logout();
    console.log("执行签出", ccbarService.logout);
    if (result.state) {
      loginStartTime.value = null;
      stopDurationTimer();
      localStorage.removeItem("loginTime");
      isLoggedIn.value = ccbarService.isLoggedIn();
    } else {
      console.error("签出失败:", result.msg);
    }
  } catch (error) {
    console.error("签出异常:", error);
  }
};

// 组件挂载时设置事件监听
onMounted(() => {
  setupEventListeners();

  // 如果SDK显示已登录，恢复登录时间
  const savedLoginTime = localStorage.getItem("loginTime");
  if (savedLoginTime && isLoggedIn.value) {
    loginStartTime.value = new Date(savedLoginTime);
    startDurationTimer();
    updateLoginDuration();
  }

  console.log("HeadSearch组件挂载，当前登录状态:", isLoggedIn.value);
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopDurationTimer();
});

// ====================================================================
const props = defineProps({
  form: Object,
  statusList: Object,
});

console.log("ccbarService:", ccbarService);

const emit = defineEmits(["search", "batch", "update:form", "create-message"]);

const localForm = computed({
  get: () => props.form,
  set: (value) => emit("update:form", value),
});

const showMessageMenu = ref(false);

const handleSearch = () => {
  emit("search");
};

const handleBatch = () => {
  emit("batch");
};

const handleNameChange = (value) => {
  const newForm = { ...props.form, agentName: value };
  emit("update:form", newForm);
};

const handleCreateMessage = (type) => {
  console.log("创建消息类型:", type);
  emit("create-message", type);
  // 关闭下拉菜单
  showMessageMenu.value = false;
};

const handleLoginSuccess = (result) => {
  console.log("登录成功回调:", result);

  // 强制更新状态
  forceUpdate.value++;

  // 设置登录时间
  if (result.loginTime) {
    loginStartTime.value = new Date(result.loginTime);
  } else {
    loginStartTime.value = new Date();
  }

  localStorage.setItem("loginTime", loginStartTime.value.toISOString());
  startDurationTimer();
  updateLoginDuration();

  // 延迟检查状态更新
  setTimeout(() => {
    console.log("登录成功后状态检查:", isLoggedIn.value);
    forceUpdate.value++;
  }, 100);
};
</script>

<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  border-radius: 4px;
  background: linear-gradient(
    180deg,
    rgba(0, 85, 255, 0.04) 0%,
    rgba(0, 85, 255, 0.08) 98%
  );
  padding: 0px 24px;
  margin-bottom: 16px;
}

.head-title {
  font-family: Alibaba PuHuiTi 3;
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
  display: flex;
  align-items: center;
  color: #fff;
  gap: 8px;
  .sign-out {
    color: #ff6b6b;
  }

  img {
    width: 80px;
    height: 32px;

    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
  .login-label {
    margin: 0 8px;
    font-size: 14px;
    color: #fff;
    font-weight: normal;
  }
  .login-time {
    font-size: 18px;

    color: #00ffff;
    font-weight: normal;
    font-family: zcoolqingkehuangyouti !important;
  }
  .login-status {
    // margin: 0 8px;
    // margin-left: 8px;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    font-weight: normal;
    span {
      color: #00ffff;
    }
  }
  .add-btn {
    // margin-right: 8px;
  }
  .clear-btn {
    margin-left: 8px;
    color: #f42d2d;
    background: rgba(244, 45, 45, 0.1);

    &:hover {
      background-image: none;

      background: rgba(244, 45, 45, 0.2);
    }
  }
  .IDLE-btn {
    background: rgba(138, 202, 45, 0.1);
    background: rgb(138, 202, 45);
  }
}

.condition-select {
  width: 160px;
  height: 32px;
}

.keyword-input {
  width: 240px;
  height: 32px;
}

:deep(.el-input__inner) {
  color: #fff;
  background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
  color: #fff;
}

:deep(.el-select__input::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
  color: #00ffff;
}

:deep(.el-select) {
  background-color: transparent;
}

:deep(.el-tag) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}

:deep(.el-form-item__label) {
  color: #fff;
}

.btn {
  height: 32px;
  /* 自动布局 */
  display: flex;
  align-items: center;
  padding: 5px 16px;
  border-radius: 4px;
  background: rgba(0, 255, 255, 0.1);
  box-sizing: border-box;
  /* 文字/正文 */
  /* 样式描述：正文文本/表单文本/常规组件文本/次级按钮文本 */
  font-family: Alibaba PuHuiTi 3;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  /* 主色/卓越青 */
  color: #00ffff;
  cursor: pointer;
  position: relative;
  &:nth-child(2) {
    margin: 0 16px;
  }
  &:hover {
    background: url(../assets/image/header/hoverBtn.png) no-repeat center center;
    background-size: 100% 100%;
  }
  img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.message-btn {
  position: relative;

  .menu-wrapper {
    position: absolute;
    top: 120%;
    left: -70%;
    z-index: 999;
  }

  .menu-transition {
    height: 10px;
    margin-top: -10px;
  }

  .menu-content {
    position: relative;
  }
}

.down-menu {
  font-size: 14px;
  border-radius: 4px;
  min-width: 10px;

  &-item {
    line-height: 40px;
    padding: 0 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #fff;
    white-space: nowrap;

    &.hover-trigger:hover {
      background: rgba(0, 128, 255, 0.2);
    }

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
}

:deep(.downBg) {
  position: absolute;
  z-index: 999;
}
</style>
