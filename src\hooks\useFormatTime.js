import { ref } from "vue";

/**
 * 将秒数转换为 HH:mm:ss 格式的时间
 * @param {number} seconds - 需要转换的秒数
 * @returns {string} - 格式化后的时间字符串
 */
export const useFormatTime = (seconds) => {
  const formatTime = (seconds) => {
    if (!seconds) return "00:00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const padZero = (num) => String(num).padStart(2, "0");

    return `${padZero(hours)}:${padZero(minutes)}:${padZero(remainingSeconds)}`;
  };

  const formattedTime = ref(formatTime(seconds));

  return {
    formattedTime,
  };
};

export const usePhoneMask = (phone) => {
  if (!phone) return "";
  return phone.toString().replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
};
