<template>
  <div class="help-record">
    <div class="help-record-top">
      <div class="tableHeader">
        <div class="headItem">求助人</div>
        <div class="headItem">求助类型</div>
        <div class="headItem" style="flex: 1.5">求助时间</div>
        <div class="headItem">处理状态</div>
        <div class="headItem">处理人</div>
      </div>
      <div class="tableBody">
        <template v-if="helpRecordData && helpRecordData.length > 0">
          <div
            class="tableItem"
            v-for="(item, index) in helpRecordData"
            :key="item.id"
            :class="index % 2 === 1 ? 'zebra' : ''"
          >
            <div class="itemContent">{{ item.NAME || "--" }}</div>
            <div class="itemContent">{{ item.REMIND_TYPE || "--" }}</div>
            <div class="itemContent" style="flex: 1.5">
              {{ item.REMIND_TIME || "--" }}
            </div>
            <div class="itemContent">
              <img
                v-if="item.IS_READ == 1"
                src="../assets/image/ycl.png"
                alt=""
              />
              <img
                v-if="item.IS_READ == 0"
                src="../assets/image/wcl.png"
                alt=""
              />
            </div>
            <div class="itemContent">
              {{ item.DISTRIBUTE_AGENT_NAME || "--" }}
            </div>

          </div>
        </template>
        <div v-else class="empty-state">
          <div class="empty-image"></div>
          暂无数据
        </div>
      </div>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page="pageIndex"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const emit = defineEmits(["update:pageIndex", "update:pageSize", "refresh"]);

const props = defineProps({
  helpRecordData: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  pageIndex: {
    type: Number,
    default: 1,
  },
});
console.log(props.helpRecordData);
const handleSizeChange = (val) => {
  emit("update:pageSize", val);
  emit("update:pageIndex", 1);
  emit("refresh");
};

const handleCurrentChange = (val) => {
  emit("update:pageIndex", val);
  emit("refresh");
};

</script>

<style lang="scss" scoped>
.help-record {
  width: 652px;
  height: 95%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .help-record-top {
    flex: none;
    overflow-y: auto;
    .tableHeader {
      width: 100%;
      height: 46px;
      background-color: rgba(0, 128, 255, 0.2);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      box-sizing: border-box;
      .headItem {
        flex: 1;
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .tableBody {
      //   min-height: 200px;
      height: 834px;
      overflow-y: auto;
      margin-bottom: 16px;
      .tableItem {
        &.zebra {
          background-color: rgba(0, 128, 255, 0.2);
        }
        .itemContent {
          flex: 1;
          align-items: center;
          display: flex;
          img {
            width: 58px;
            height: 24px;
          }
        }
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        box-sizing: border-box;

        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        /* 主色/白色-文字用色 */
        /* 样式描述：文字主要用色 */
        color: #ffffff;
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-top: 20vh;
        .empty-image {
          width: 308px;
          height: 252px;
          background: url("../assets/image/empty.png") no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }

  .pagination {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    :deep(.el-pagination) {
      .is-active {
        border-radius: 4px;
        background: linear-gradient(
          180deg,
          rgba(0, 128, 255, 0.3) 2%,
          rgba(0, 128, 255, 0.2) 100%
        );
        box-sizing: border-box;
        border: 1px solid #0080ff;
        box-shadow: inset 0px 0px 12px 0px #0080ff;
      }
      .el-select {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        height: 32px;
        color: rgba(0, 255, 255, 1);
      }
      .el-select__selected-item {
        color: rgba(0, 255, 255, 1);
      }

      .el-pager {
        gap: 8px;
        margin-left: 8px;
        margin-right: 8px;
      }
      .number,
      .more {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        width: 32px;
        height: 32px;
        color: #fff;
      }
      .btn-prev,
      .btn-next {
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        width: 32px;
        height: 32px;
        color: #fff;
      }
    }
  }
}
</style>
<style>
button:disabled {
  color: rgba(0, 255, 255, 0.2) !important;
}
.el-pagination button.is-disabled {
  color: rgba(0, 255, 255, 0.2) !important;
}
</style>
