<template>
  <div :class="[side === 'left' ? 'nine-square-left' : 'nine-square-right']">
    <div class="top">
      <div class="grid-item"></div>
      <div class="grid-item">
        <div class="nine-square-content" style="visibility: hidden">
          <slot></slot>
        </div>
      </div>
      <div class="grid-item"></div>
    </div>
    <div class="middle">
      <div class="grid-item"></div>
      <div class="grid-item">
        <div class="nine-square-content">
          <slot></slot>
        </div>
      </div>
      <div class="grid-item"></div>
    </div>
    <div class="bottom">
      <div class="grid-item"></div>
      <div class="grid-item">
        <div class="nine-square-content" style="visibility: hidden">
          <slot></slot>
        </div>
      </div>
      <div class="grid-item"></div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  side: {
    type: String,
    default: "right", // 默认显示右侧气泡
    validator: (value) => ["left", "right"].includes(value),
  },
});
console.log(props.side);
</script>

<style lang="scss" scoped>
.nine-square-common {
  display: flex;
  flex-direction: column;
  margin-top: 0px;
  .top,
  .middle,
  .bottom {
    display: flex;
  }
  .grid-item {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    box-sizing: border-box;
  }
}

.nine-square-right {
  @extend .nine-square-common;
  float: right;
  .nine-square-content {
    margin-top: -6px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .top {
    .grid-item {
      height: 18px;
      &:nth-child(1) {
        /* rt1.png */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/rt1.png");
      }
      &:nth-child(2) {
        /* rt2.png */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/rt2.png");
      }
      &:nth-child(3) {
        /* rt3.png */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/rt3.png");
      }
    }
  }
  .middle {
    .grid-item {
      min-height: 10px;
      &:nth-child(1) {
        /* rt4.png */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/rt4.png");
      }
      &:nth-child(2) {
        /* rt5.png */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/rt5.png");
      }
      &:nth-child(3) {
        /* rt6.png - 仅占据最右侧的窄列 */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/rt6.png");
      }
    }
  }
  .bottom {
    .grid-item {
      height: 4px;
      &:nth-child(1) {
        /* rt7.png */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/rt7.png");
      }
      &:nth-child(2) {
        /* rt8.png */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/rt8.png");
      }
      &:nth-child(3) {
        /* rt9.png - 仅占据最右侧的窄列 */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/rt9.png");
      }
    }
  }
}

.nine-square-left {
  @extend .nine-square-common;
  .nine-square-content {
    margin-top: -6px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .top {
    .grid-item {
      height: 18px;
      &:nth-child(1) {
        /* rt3.png - 现在在左侧 */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/lt1.png");
      }
      &:nth-child(2) {
        /* rt2.png - 中间 */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/lt2.png");
      }
      &:nth-child(3) {
        /* rt1.png - 现在在右侧 */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/lt3.png");
      }
    }
  }
  .middle {
    .grid-item {
      min-height: 10px;
      &:nth-child(1) {
        /* rt6.png - 现在在左侧 */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/lt4.png");
      }
      &:nth-child(2) {
        /* rt5.png - 中间 */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/lt5.png");
      }
      &:nth-child(3) {
        /* rt4.png - 现在在右侧 */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/lt6.png");
      }
    }
  }
  .bottom {
    .grid-item {
      height: 4px;
      &:nth-child(1) {
        /* rt9.png - 现在在左侧 */
        flex: none;
        width: 10px;
        background-image: url("../assets/qp/lt7.png");
      }
      &:nth-child(2) {
        /* rt8.png - 中间 */
        // flex: 1;
        min-width: 40px;
        background-image: url("../assets/qp/lt8.png");
      }
      &:nth-child(3) {
        /* rt7.png - 现在在右侧 */
        flex: none;
        width: 4px;
        background-image: url("../assets/qp/lt9.png");
      }
    }
  }
}
</style>
