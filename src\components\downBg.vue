<template>
  <div class="down-bg" :class="{ isTop: isTop }">
    <div class="down-bg-top"></div>
    <div class="down-bg-center">
      <div class="content-wrapper">
        <slot></slot>
      </div>
    </div>
    <div class="down-bg-bottom"></div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
defineProps({
  isTop: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.down-bg {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;

  &-top {
    flex: none;
    width: 264px;
    height: 8px;
    background: url("../assets/image/downBG1.png") no-repeat center bottom;
    background-size: 100% 100%;
  }
  &-center {
    width: 100%;
    flex: 1;
    background: url("../assets/image/downBG2.png") repeat-y center center;
    background-size: 100% auto;
    margin: -1px 0;
    position: relative;
    z-index: 2;
  }
  &-bottom {
    flex: none;
    width: 264px;
    height: 24px;
    background: url("../assets/image/downBG3.png") no-repeat center top;
    background-size: 100% 100%;
  }
}

.isTop {
  transform: rotate(180deg);
  
  .content-wrapper {
    transform: rotate(180deg);
  }
}
</style>
