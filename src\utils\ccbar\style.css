
.transfer-container[data-v-6a84231d] {
  padding: 10px 0;
}
.current-call-info[data-v-6a84231d] {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}
.info-item[data-v-6a84231d] {
  display: flex;
}
.label[data-v-6a84231d] {
  font-weight: bold;
  width: 80px;
  color: #606266;
}
.value[data-v-6a84231d] {
  flex: 1;
  color: #303133;
}
.transfer-tabs[data-v-6a84231d] {
  margin-bottom: 15px;
}
.transfer-form[data-v-6a84231d] {
  margin-bottom: 15px;
}
.common-targets[data-v-6a84231d] {
  margin-top: 15px;
}
.subtitle[data-v-6a84231d] {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}
.transfer-options[data-v-6a84231d] {
  display: flex;
  align-items: center;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}
.help-icon[data-v-6a84231d] {
  margin-left: 5px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}

.call-alert-container[data-v-3d7e27c4] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(3px);
}
.call-alert-box[data-v-3d7e27c4] {
  width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: shake-3d7e27c4 0.5s linear infinite;
}
@keyframes shake-3d7e27c4 {
0% { transform: translateX(0);
}
25% { transform: translateX(-3px);
}
50% { transform: translateX(0);
}
75% { transform: translateX(3px);
}
100% { transform: translateX(0);
}
}
.flashing-border[data-v-3d7e27c4] {
  border: 3px solid transparent;
  animation: flashBorder-3d7e27c4 1s linear infinite;
}
@keyframes flashBorder-3d7e27c4 {
0% { border-color: rgba(245, 108, 108, 0.4); box-shadow: 0 0 10px rgba(245, 108, 108, 0.4);
}
50% { border-color: rgba(245, 108, 108, 1); box-shadow: 0 0 20px rgba(245, 108, 108, 0.8);
}
100% { border-color: rgba(245, 108, 108, 0.4); box-shadow: 0 0 10px rgba(245, 108, 108, 0.4);
}
}
.call-alert-header[data-v-3d7e27c4] {
  background: #f56c6c;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.call-alert-title[data-v-3d7e27c4] {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.call-alert-title i[data-v-3d7e27c4] {
  margin-right: 8px;
  font-size: 20px;
  animation: pulse-3d7e27c4 1s infinite;
}
@keyframes pulse-3d7e27c4 {
0% { transform: scale(1);
}
50% { transform: scale(1.1);
}
100% { transform: scale(1);
}
}
.call-alert-timer[data-v-3d7e27c4] {
  font-size: 16px;
  font-weight: bold;
}
.call-alert-body[data-v-3d7e27c4] {
  padding: 20px;
}
.call-info[data-v-3d7e27c4] {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.phone-number[data-v-3d7e27c4], .customer-info[data-v-3d7e27c4], .call-time[data-v-3d7e27c4], .call-notes[data-v-3d7e27c4] {
  display: flex;
}
.label[data-v-3d7e27c4] {
  width: 80px;
  color: #606266;
  font-weight: bold;
}
.value[data-v-3d7e27c4] {
  flex: 1;
  color: #303133;
  font-weight: bold;
}
.phone-number .value[data-v-3d7e27c4] {
  font-size: 18px;
  color: #f56c6c;
}
.call-alert-footer[data-v-3d7e27c4] {
  padding: 15px;
  display: flex;
  justify-content: center;
  gap: 20px;
  border-top: 1px solid #ebeef5;
}

.ccbar-integrated[data-v-c1391e1b] {
  width: 100%;
  position: relative;
}

.ccbar-toolbar[data-v-f62b190b] {
  font-family: Arial, sans-serif;
  width: 100%;
  max-width: 100%;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}
.toolbar-toggle[data-v-f62b190b] {
  position: absolute;
  right: 5px;
  top: 10px;
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  z-index: 10;
}
.toolbar-main[data-v-f62b190b] {
  display: flex;
  align-items: center;
  padding: 10px;
  flex-wrap: wrap;
}
.toolbar-status[data-v-f62b190b] {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}
.agent-info[data-v-f62b190b] {
  margin-right: 15px;
}
.agent-state[data-v-f62b190b] {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 5px;
}
.state-icon[data-v-f62b190b] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}
.state-ready .state-icon[data-v-f62b190b] {
  background-color: #67c23a;
}
.state-busy .state-icon[data-v-f62b190b] {
  background-color: #e6a23c;
}
.state-talking .state-icon[data-v-f62b190b] {
  background-color: #409eff;
}
.state-ringing .state-icon[data-v-f62b190b] {
  background-color: #409eff;
  animation: blink-f62b190b 1s infinite;
}
.state-held .state-icon[data-v-f62b190b] {
  background-color: #909399;
}
.state-acw .state-icon[data-v-f62b190b] {
  background-color: #909399;
}
.state-offline .state-icon[data-v-f62b190b] {
  background-color: #f56c6c;
}
.agent-id[data-v-f62b190b] {
  font-size: 12px;
  color: #606266;
}
.agent-outbound-number[data-v-f62b190b] {
  font-size: 12px;
  color: #409eff;
  margin-top: 3px;
  background-color: #ecf5ff;
  padding: 2px 5px;
  border-radius: 3px;
  display: inline-block;
}
.state-controls[data-v-f62b190b] {
  display: flex;
  position: relative;
}
.state-menu[data-v-f62b190b] {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  min-width: 100px;
}
.menu-item[data-v-f62b190b] {
  padding: 8px 12px;
  cursor: pointer;
}
.menu-item[data-v-f62b190b]:hover {
  background-color: #f5f7fa;
}
.toolbar-call[data-v-f62b190b] {
  display: flex;
  flex-direction: column;
  margin: 0 15px;
  flex: 2;
  min-width: 250px;
}
.call-input[data-v-f62b190b] {
  display: flex;
  margin-bottom: 10px;
}
.call-input input[data-v-f62b190b] {
  flex: 1;
  margin-right: 10px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.call-controls[data-v-f62b190b] {
  display: flex;
}
.toolbar-login[data-v-f62b190b],
.toolbar-logout[data-v-f62b190b] {
  margin-left: auto;
}
.toolbar-notification[data-v-f62b190b] {
  padding: 10px;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 12px;
}
.event-item[data-v-f62b190b] {
  display: flex;
  align-items: center;
}
.event-type[data-v-f62b190b] {
  background-color: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 8px;
  font-size: 10px;
}
.event-content[data-v-f62b190b] {
  color: #606266;
}

/* 按钮样式 */
.btn[data-v-f62b190b] {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}
.btn[data-v-f62b190b]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.btn-ready[data-v-f62b190b] {
  background-color: #67c23a;
  color: white;
}
.btn-not-ready[data-v-f62b190b] {
  background-color: #e6a23c;
  color: white;
}
.btn-status[data-v-f62b190b] {
  background-color: #909399;
  color: white;
}
.btn-call[data-v-f62b190b] {
  background-color: #409eff;
  color: white;
}
.btn-hangup[data-v-f62b190b] {
  background-color: #f56c6c;
  color: white;
}
.btn-hold[data-v-f62b190b] {
  background-color: #909399;
  color: white;
}
.btn-retrieve[data-v-f62b190b] {
  background-color: #67c23a;
  color: white;
  font-weight: bold;
}
.btn-login[data-v-f62b190b] {
  background-color: #409eff;
  color: white;
}
.btn-logout[data-v-f62b190b] {
  background-color: #909399;
  color: white;
}
.btn-mute[data-v-f62b190b] {
  background-color: #409eff;
  color: white;
  position: relative;
}
.btn-mute[data-v-f62b190b]::before {
  content: "🎤";
  margin-right: 4px;
}
.btn-unmute[data-v-f62b190b] {
  background-color: #e6a23c;
  color: white;
  position: relative;
}
.btn-unmute[data-v-f62b190b]::before {
  content: "🔇";
  margin-right: 4px;
}

/* 折叠样式 */
.is-expanded[data-v-f62b190b] {
  min-height: 50px;
}
.is-expanded .toolbar-main[data-v-f62b190b] {
  flex-direction: row;
}
@keyframes blink-f62b190b {
0% {
    opacity: 1;
}
50% {
    opacity: 0.3;
}
100% {
    opacity: 1;
}
}

/* 小屏幕自适应 */
@media screen and (max-width: 768px) {
.toolbar-main[data-v-f62b190b] {
    flex-direction: column;
    align-items: stretch;
}
.toolbar-status[data-v-f62b190b],
  .toolbar-call[data-v-f62b190b],
  .toolbar-login[data-v-f62b190b],
  .toolbar-logout[data-v-f62b190b] {
    margin: 5px 0;
}
.call-input[data-v-f62b190b] {
    flex-direction: column;
}
.call-input input[data-v-f62b190b] {
    margin-right: 0;
    margin-bottom: 5px;
}
}
.agent-state-duration[data-v-f62b190b] {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

/* 在HELD状态下突出显示恢复按钮 */
.state-held ~ .toolbar-call .btn-retrieve[data-v-f62b190b]:not(:disabled) {
  animation: pulse-f62b190b 1.5s infinite;
  box-shadow: 0 0 5px #67c23a;
  background-color: #67c23a;
  transform-origin: center;
  font-weight: bold;
}
@keyframes pulse-f62b190b {
0% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.05);
    opacity: 0.8;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}

.login-overlay[data-v-55a2e1f8] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.login-dialog[data-v-55a2e1f8] {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.login-header[data-v-55a2e1f8] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
}
.login-header h3[data-v-55a2e1f8] {
  margin: 0;
  font-size: 16px;
  color: #303133;
}
.close-btn[data-v-55a2e1f8] {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 0;
}
.login-body[data-v-55a2e1f8] {
  padding: 20px;
  /* overflow-y: auto; */
}
.login-form[data-v-55a2e1f8] {
  display: flex;
  flex-direction: column;
}
.form-group[data-v-55a2e1f8] {
  margin-bottom: 15px;
}
.form-group label[data-v-55a2e1f8] {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}
.form-group input[type="text"][data-v-55a2e1f8],
.form-group input[type="password"][data-v-55a2e1f8],
.form-group select[data-v-55a2e1f8] {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}
.form-group input[data-v-55a2e1f8]:focus,
.form-group select[data-v-55a2e1f8]:focus {
  outline: none;
  border-color: #409eff;
}
.checkbox-group[data-v-55a2e1f8] {
  display: flex;
  align-items: center;
}
.checkbox-group label[data-v-55a2e1f8] {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  cursor: pointer;
}
.checkbox-group input[type="checkbox"][data-v-55a2e1f8] {
  margin-right: 5px;
}
.advanced-options[data-v-55a2e1f8] {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e4e7ed;
}
.toggle-advanced[data-v-55a2e1f8] {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
  text-align: right;
  margin-top: 5px;
}
.login-footer[data-v-55a2e1f8] {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}
.error-message[data-v-55a2e1f8] {
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 10px;
  text-align: left;
}
.login-btn[data-v-55a2e1f8] {
  background-color: #409eff;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  padding: 10px 20px;
  font-size: 14px;
}
.login-btn[data-v-55a2e1f8]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.login-btn[data-v-55a2e1f8]:not(:disabled):hover {
  background-color: #66b1ff;
}
.skills-group[data-v-55a2e1f8] {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e4e7ed;
}
.skills-container[data-v-55a2e1f8] {
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 2px;
  max-height: 180px;
  overflow-y: auto;
  /* background-color: #f9f9f9; */
}
.skill-item[data-v-55a2e1f8] {
  margin-bottom: 2px;
  padding: 5px 8px;
  border-radius: 3px;
  transition: background-color 0.2s;
}
.skill-item[data-v-55a2e1f8]:last-child {
  margin-bottom: 0;
}
.skill-item[data-v-55a2e1f8]:hover {
  /* background-color: #eef5fe; */
  /* color: #eef5fe; */
}
.skill-label[data-v-55a2e1f8] {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  width: 100%;
  font-size: 13px;
  height: 24px; /* 设置固定高度确保对齐 */
}
.skill-label input[type="checkbox"][data-v-55a2e1f8] {
  margin-right: 8px;
  position: relative;
  top: 0;
}
.skill-label input[type="checkbox"]:checked + span[data-v-55a2e1f8] {
  color: #409eff;
}
.skill-label span[data-v-55a2e1f8] {
  color: #606266;
  display: inline-block;
  line-height: 1.2;
}
.skills-group label[data-v-55a2e1f8] {
  color: #606266;
  font-size: 14px;
}
