<template>
  <div class="home" :class="{ 'iframe-mode': isIframe }">
    <HeadSearch
      :form="form"
      :status-list="status_list"
      @search="getSeat(true)"
      @batch="batch"
      @create-message="handleCreateMessage"
    />

    <div class="bottom">
      <SideMenu
        :data="data"
        :current-index="currentIndex"
        :cs-list="cs_list"
        :form="form"
        :content-cards="bottomCards"
        @menu-click="handleMenu"
        @todo-click="handleTodoClick"
        @template-click="handleTemplateClick"
        @template-list-click="handleTemplateListClick"
        @show-message-list="showMessageList"
      />

      <div class="bottom-right">
        <div class="right-top">
          <div class="seat-map-title">
            <span class="fontStyle">{{ currentName }}</span>
          </div>
          <div class="right-top-right">
            <div class="right-top-right-item">
              <div class="label">类型</div>
              <div
                :class="['btn', { active: currentTab === 'queryAgentPlace' }]"
                @click="changeCurrentTab('queryAgentPlace')"
              >
                按区域
              </div>
              <div
                :class="[
                  'btn',
                  { active: currentTab === 'queryAgentPlaceSkill' },
                ]"
                @click="changeCurrentTab('queryAgentPlaceSkill')"
              >
                按技能组
              </div>
              <div
                :class="[
                  'btn',
                  { active: currentTab === 'queryAgentPlaceWorkGroup' },
                ]"
                @click="changeCurrentTab('queryAgentPlaceWorkGroup')"
              >
                按班组
              </div>
              <div class="line"></div>
              <div class="label">技能组</div>
              <el-select
                v-model="form.agentSkills"
                placeholder="请选择"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="0"
                class="condition-select"
                @change="handleSkillGroupChange"
              >
                <el-option
                  v-for="option in skillGroupOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
              <div class="line"></div>
              <div class="label">班组</div>
              <el-select
                v-model="form.agentWorkGroup"
                placeholder="请选择"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="0"
                class="condition-select"
                style="margin-right: 1.25vw"
                @change="handleAgentWorkGroupChange"
              >
                <el-option
                  v-for="option in workGroupOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="right-content">
          <div class="content-top">
            <div
              v-for="(item, index) in contentCards"
              :key="index"
              class="content-top-item"
              :style="{
                backgroundImage: `${
                  form.currentState !== item.state
                    ? 'url(' + require('@/assets/image/right/' + item.bg) + ')'
                    : 'url(' +
                      require('@/assets/image/right/active-' + item.bg) +
                      ')'
                }`,
              }"
              @click="handleContentClick(item.state)"
            >
              <div>
                <img
                  class="rtPostion"
                  src="../assets/image/active-right-top.png"
                  alt=""
                  v-if="form.currentState === item.state"
                />
                <img
                  :src="require(`@/assets/image/right/${item.icon}`)"
                  alt=""
                />
                <!-- <img
                  v-else
                  :src="require(`@/assets/image/right/active-${item.icon}`)"
                  alt=""
                /> -->
              </div>
              <div>
                <div
                  class="content-top-item-title"
                  :style="{
                    color:
                      form.currentState === item.state ? '#FFDC00' : '#fff',
                  }"
                >
                  {{ item.title }}
                </div>
                <div
                  class="content-top-item-content"
                  :style="{
                    color:
                      form.currentState === item.state ? '#FFDC00' : '#fff',
                  }"
                >
                  {{ item.value }}
                </div>
              </div>
            </div>
          </div>
          <div class="content-bottom">
            <div
              v-for="(item, index) in bottomCards"
              :key="index"
              :class="[
                'content-bottom-item',
                { 'active-bottom-item': form.currentState === item.state },
              ]"
              @click="handleContentClick(item.state)"
            >
              <div class="content-bottom-item-title">{{ item.title }}</div>
              <div class="content-bottom-item-content">
                {{ item.value }}
              </div>
            </div>
          </div>
        </div>
        <div class="right-head">
          <div class="right-head-span fontStyle">座位列表</div>
          <div class="right-head-right" v-if="currentTab === 'queryAgentPlace'">
            <div class="tl">
              <div class="tl-item">
                <img src="@/assets/image/gj.png" alt="" />
                告警
              </div>
              <div class="tl-item">
                <img src="@/assets/image/zbz.png" alt="" />
                值班长
              </div>
              <div class="tl-item">
                <img src="@/assets/image/bz.png" alt="" />
                班长
              </div>
            </div>
            <div class="tl-line"></div>
            <div class="right-head-right-label">坐席缩放:</div>
            <div class="right-head-right-item" style="margin-right: 1.25vw">
              <el-slider
                v-model="zoomNumber"
                :max="2"
                :min="0.2"
                :step="0.1"
              ></el-slider>
            </div>
          </div>
        </div>

        <div class="right-bottom">
          <SeatMap
            v-if="currentTab === 'queryAgentPlace'"
            :seat-list="seat_list"
            :status-list="status_list"
            :zoom-number="zoomNumber"
            @view-seat="handleView"
            @zoom-change="handleWheel"
            @refresh="getSeat(true)"
          />

          <SeatGroup
            v-else
            :current-tab="currentTab"
            :group-list="group_list"
            :status-list="status_list"
            @view-seat="handleView"
            @view-workgroup="handleWorkGroupInfo"
          />
        </div>
      </div>
    </div>
    <!-- 告警弹窗 -->
    <AlarmDialog
      :visible="centerDialogVisible"
      :deal-obj="dealObj"
      @close="centerDialogVisible = false"
      @refresh-backlog="getTodoListFn"
    />
    <!-- 批量处理 -->
    <BatchDialog
      :visible="batchDialogVisible"
      @close="batchDialogVisible = false"
    ></BatchDialog>
    <!-- 个人画像 -->
    <base-drawer
      title="个人画像"
      :visible="agentInfoVisible"
      @close="closeAgentInfo"
    >
      <PersonalInfo
        ref="personalInfoRef"
        :info="person_info"
        @close="closeAgentInfo"
        @getCallData="handleGetCallData"
        @functionStateChange="handleFunctionStateChange"
      />
    </base-drawer>
    <!-- 班组画像 -->
    <base-drawer
      title="班组画像"
      :visible="workGroupInfoVisible"
      @close="workGroupInfoVisible = false"
    >
      <WorkGroupInfo :info="workGroupInfo" @getCallData="handleGetCallData" />
    </base-drawer>
    <!-- 历史转写记录 -->
    <base-drawer
      title="历史转写记录"
      :visible="historyRecordVisible"
      @close="historyRecordVisible = false"
    >
      <HistoryRecord
        v-model:currentType="currentType"
        :isWorkGroup="helpTitle === '班组求助明细'"
        :callData="callData"
        :agentName="agentName"
        :agentId="currentAgentId"
        :workId="currentWorkId"
        :showCallInfo="showCallInfo"
        :avatar="currentAvartar"
        :total="callDataTotal"
        @getCallData="handleGetCallData"
      />
    </base-drawer>
    <!-- 求助明细 -->
    <base-drawer
      :title="helpTitle"
      :visible="helpRecordVisible"
      @close="helpRecordVisible = false"
    >
      <HelpRecord
        :helpRecordData="helpRecordData"
        :total="helpToast"
        v-model:pageIndex="helpPageIndex"
        v-model:pageSize="helpPageSize"
        @refresh="handleGetHelpRecord"
      />
    </base-drawer>
    <!-- 待办列表 -->
    <base-drawer
      title="待办列表"
      :visible="backlogVisible"
      @close="backlogVisible = false"
    >
      <div class="selectBox">
        <el-input
          v-model="backlogParams.agentName"
          placeholder="坐席名称"
          class="addinput"
        />
        <el-input
          v-model="backlogParams.agentId"
          placeholder="坐席工号"
          class="addinput"
        />
        <el-select
          v-model="backlogParams.alarmType"
          clearable
          placeholder="告警类型"
          class="addinput"
        >
          <el-option label="超长通话" value="1"></el-option>
          <el-option label="话后超时" value="2"></el-option>
          <el-option label="静音" value="3"></el-option>
          <el-option label="语速过快" value="4"></el-option>
          <el-option label="抢话" value="5"></el-option>
          <el-option label="坐席违规词" value="6"></el-option>
          <el-option label="市民敏感词" value="7"></el-option>
          <el-option label="求助" value="8"></el-option>
        </el-select>
        <div class="btn" @click="getTodoListFn">
          <img src="../assets/image/header/search-icon.png" alt="待办列表" />
          <span>搜索</span>
        </div>
        <!-- <el-button type="primary" @click="getTodoListFn">查询</el-button> -->
      </div>
      <BacklogList
        v-model:pageIndex="backlogPageIndex"
        v-model:pageSize="backlogPageSize"
        :backlogList="backlogList"
        :total="backlogTotal"
        @refresh="getTodoListFn"
        @update:deal="handleDeal"
      />
    </base-drawer>
    <!-- 模版列表 -->
    <base-drawer
      title="模版列表"
      :visible="templateListVisible"
      @close="templateListVisible = false"
    >
      <TemplateList
        :template-list="templateList"
        :template-total="templateTotal"
        :template-params="templateParams"
        @params-change="handleTemplateParamsChange"
        @edit-template="handleEditTemplate"
        @view-template-detail="handleViewTemplateDetail"
      />
    </base-drawer>
    <!-- 新建或编辑模版 -->
    <base-drawer
      :title="isEditTemplate ? '编辑模板' : '新建模板'"
      :visible="templateVisible"
      @close="closeTemplateDrawer"
      :showBtn="false"
    >
      <TemplateAddOrEdit
        :templateId="currentTemplateId"
        :templateType="currentTemplateType"
        @close="closeTemplateDrawer"
        @confirm="handleConfirm"
      />
    </base-drawer>

    <!-- 模板详情 -->
    <base-drawer
      title="模板详情"
      :visible="templateDetailVisible"
      @close="templateDetailVisible = false"
    >
      <TemplateDetail :info="templateDetailInfo" />
    </base-drawer>

    <!-- 新建消息 -->
    <base-drawer
      :title="messageTitle"
      :visible="addMessageVisible"
      @close="addMessageVisible = false"
      :showBtn="false"
    >
      <MessageAdd
        :message-type="messageType"
        :options="workGroupOptions"
        @close="addMessageVisible = false"
      />
    </base-drawer>
    <!-- 消息明细 -->
    <base-drawer
      :title="messageListTitle"
      :visible="messageListVisible"
      @close="messageListVisible = false"
    >
      <MessageList
        :message-type="messageType"
        :backlog-list="messageList"
        :total="messageTotal"
        :page-index="messageParams.pageIndex"
        :page-size="messageParams.pageSize"
        :loading="messageListLoading"
        @loading="onLoading"
        @refresh="handleMessageParamsChange"
        @view-detail="handleMessageDetail"
        @view-inner-detail="handleMessageInnerDetail"
      />
    </base-drawer>

    <!-- 详情 -->
    <base-drawer
      title="消息详情"
      :visible="messageDetailVisible"
      @close="messageDetailVisible = false"
    >
      <MessageDetail :info="messageDetailInfo" />
    </base-drawer>

    <!-- 消息明细 -->
    <base-drawer
      title="消息明细"
      :visible="messageInnerDetailVisible"
      @close="messageInnerDetailVisible = false"
    >
      <MessageInnerDetail
        :info="messageInnerDetailInfo"
        @refresh="
          (params) =>
            handleMessageInnerDetail(messageInnerDetailInfo, {
              ...params,
              isRefresh: true,
            })
        "
      />
    </base-drawer>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  defineOptions,
  computed,
} from "vue";
import {
  queryPhoneRoom,
  queryAgentById,
  queryAgentPlace as apiQueryAgentPlace,
  queryAgentInfoById,
  getSkillGroupList,
  getCallData,
  getHelpRecord,
  getWorkGroupInfo,
  getWorkGroupList,
  getToDuList,
  getUsetType,
  getTemplateList,
  getTemplateDetail,
  getSmsList,
  getCallList,
  getAssistantList,
  getMessageDetail,
} from "../api";
import { getQueryParam } from "../utils";
import HeadSearch from "../components/HeadSearch.vue";
import SideMenu from "../components/SideMenu.vue";
import SeatMap from "../components/SeatMap.vue";
import AlarmDialog from "../components/AlarmDialog.vue";
import BaseDrawer from "../components/baseComponents/baseDrawer.vue";
import { ElMessage } from "element-plus";
import SeatGroup from "../components/SeatGroup.vue";
import PersonalInfo from "../components/PersonalInfo.vue";
import HistoryRecord from "../components/HistoryRecord.vue";
import HelpRecord from "../components/HelpRecord.vue";
import WorkGroupInfo from "../components/WorkGroupInfo.vue";
import BacklogList from "../components/BacklogList.vue";
import BatchDialog from "../components/BatchDialog.vue";
import TemplateAddOrEdit from "../components/template/templateAddOrEdit.vue";
import TemplateList from "../components/template/TemplateList.vue";
import TemplateDetail from "../components/template/templateDetail.vue";
import MessageAdd from "../components/message/MessageAdd.vue";
import MessageList from "../components/message/MessageList.vue";
import MessageDetail from "../components/message/MessageDetail.vue";
import MessageInnerDetail from "../components/message/MessageInnerDetail.vue";
import axios from "axios";
import {
  defaultStartTime,
  todayStartTime,
} from "@/utils/messageList/messageList";
defineOptions({
  name: "homePage",
});
const backlogParams = reactive({
  agentName: "",
  agentId: "",
  alarmType: "",
});
const currentAvartar = ref("");
const personalInfoRef = ref(null);
// 模板详情
const templateDetailVisible = ref(false);
const templateDetailInfo = ref({});

// 新建模板
const templateVisible = ref(false);
const isEditTemplate = ref(false);
const currentTemplateId = ref("");
const currentTemplateType = ref("");

// 模版列表
const templateListVisible = ref(false);
const templateList = ref([]);
const templateTotal = ref(0);
const templateParams = reactive({
  pageType: "3",
  pageIndex: "1",
  pageSize: "10",
  startTime: "",
  endTime: "",
  templateType: "",
  templateName: "",
  createBy: "",
});

// 消息明细
const messageListVisible = ref(false);
const messageListTitle = ref("消息明细");
const messageList = ref([]); // 消息列表数据
const messageTotal = ref(0); // 消息总数
const messageParams = reactive({
  pageType: "3",
  // 消息查询参数
  pageIndex: 1,
  pageSize: 15,
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  agentName: "", // 坐席姓名
  agentNum: "", // 坐席工号
  phone: "", // 手机号
  submitUser: "", // 提交人
  sendState: "", // 发送状态
  receiptState: "", // 回执状态
});

// 消息详情
const messageDetailVisible = ref(false);
const messageDetailInfo = ref({});

// 处理消息明细
const showMessageList = async (type) => {
  messageListLoading.value = true;
  messageType.value = type;
  // 根据类型设置不同的标题
  const titles = { 0: "助手消息明细", 1: "短信下发明细", 2: "外呼通知明细" };
  messageListTitle.value = titles[type] || "消息明细";
  // 重置分页和筛选参数
  messageParams.pageIndex = 1;
  messageParams.startTime = defaultStartTime();
  messageParams.endTime = todayStartTime();
  messageParams.agentName = "";
  messageParams.agentNum = "";
  messageParams.phone = "";
  messageParams.submitUser = "";
  messageParams.sendState = "";
  messageParams.receiptState = "";
  try {
    messageListVisible.value = true;
    // 根据不同类型调用不同接口
    if (type === 1) {
      // 短信下发明细
      const res = await getSmsList({
        pageType: "3",
        pageIndex: 1, //页码
        pageSize: 15, //每页条数
        startTime: defaultStartTime(), //开始时间
        endTime: todayStartTime(), //结束时间
        agentName: "", //坐席姓名
        agentNum: "", //坐席工号
        phone: "", //坐席手机号
        submitUser: "", //提交人
        receiptState: "", //回执状态
        sendState: "", //发送状态
      });
      if (res.data) {
        messageList.value = res.data.data || [];
        messageTotal.value = res.data.totalRow || 0;
      }
    } else if (type === 2) {
      // 外呼通知明细
      const res = await getCallList({
        pageType: "3",
        pageIndex: 1, //页码
        pageSize: 15, //每页条数
        startTime: defaultStartTime(), //开始时间
        endTime: todayStartTime(), //结束时间
        agentName: "", //坐席姓名
        agentNum: "", //坐席工号
        phone: "", //坐席手机号
        submitUser: "", //提交人
        callResult: "", //外呼结果
      });
      if (res.data) {
        messageList.value = res.data.data || [];
        messageTotal.value = res.data.totalRow || 0;
      }
    } else if (type === 0) {
      // 助手消息明细
      const res = await getAssistantList({
        startTime: messageParams.startTime,
        endTime: messageParams.endTime,
        msgRemindType: messageParams.msgRemindType,
        page: messageParams.pageIndex,
        limit: messageParams.pageSize,
      });
      if (res.data) {
        // 如果接口返回数据为空，使用模拟数据
        if (!res.data.data || res.data.data.length === 0) {
          messageList.value = [];
          messageTotal.value = 3;
        } else {
          messageList.value = res.data.data;
          messageTotal.value = res.data.count || 0;
        }
      }
    }
  } catch (error) {
    console.error(`获取${titles[type]}失败:`, error);
    ElMessage.error(`获取${titles[type]}失败`);

    // 如果是助手消息明细，即使接口失败也显示模拟数据
    if (type === 0) {
      messageList.value = [];
      messageTotal.value = 3;
    } else {
      messageList.value = [];
      messageTotal.value = 0;
    }
  } finally {
    messageListLoading.value = false;
  }
};
// 处理模版列表点击事件
const handleTemplateListClick = async () => {
  await getTemplateListData();
  templateListVisible.value = true;
};

// 获取模板列表数据
const getTemplateListData = async () => {
  try {
    const res = await getTemplateList(templateParams);
    if (res.data) {
      templateList.value = res.data.data;
      templateTotal.value = res.data.totalRow;
    }
  } catch (error) {
    console.error("获取模板列表失败：", error);
    ElMessage.error("获取模板列表失败");
  }
};

// 处理模板列表筛选或分页
const handleTemplateParamsChange = (params) => {
  Object.assign(templateParams, params);
  getTemplateListData();
};

// 处理编辑模板
const handleEditTemplate = (templateData) => {
  currentTemplateId.value = templateData.id;
  currentTemplateType.value = templateData.templateType;
  isEditTemplate.value = true;
  templateVisible.value = true;
};

// 关闭模板抽屉
const closeTemplateDrawer = () => {
  templateVisible.value = false;
  // 重置编辑状态和模板ID
  setTimeout(() => {
    isEditTemplate.value = false;
    currentTemplateId.value = "";
    currentTemplateType.value = "";
  }, 300);
};

// 处理模板确认
const handleConfirm = (result) => {
  closeTemplateDrawer();

  // 如果操作成功，刷新模板列表数据
  if (result && result.success) {
    // 无论模板列表是否显示，都刷新数据，以便下次打开时数据是最新的
    getTemplateListData();
  }
};

// 处理模板点击
const handleTemplateClick = () => {
  isEditTemplate.value = false;
  currentTemplateId.value = "";
  currentTemplateType.value = "";
  templateVisible.value = true;
};

// 消息明细
const messageType = ref(0); // 默认为助手消息类型

// 根据消息类型计算标题
const messageTitle = computed(() => {
  const titles = {
    0: "新建助手消息",
    1: "新建短信通知",
    2: "新建外呼通知",
  };
  return titles[messageType.value] || "新建消息";
});

// 是否批量处理
const batchDialogVisible = ref(false);

// 待办列表
const backlogVisible = ref(false);
const backlogList = ref([]);
const backlogPageIndex = ref(1);
const backlogPageSize = ref(15);
const backlogTotal = ref(0);
const dealObj = ref({});
const getTodoListFn = async () => {
  try {
    const response = await getToDuList({
      messageId: "getToDuList",
      ...backlogParams,
      pageIndex: backlogPageIndex.value,
      pageSize: backlogPageSize.value,
    });
    console.log("response", response);
    backlogList.value = response.data.data;
    backlogTotal.value = response.data.totalRow;
    console.log("response", backlogList.value);
    backlogVisible.value = true;
  } catch (error) {
    console.error("获取待办列表失败：", error);
  }
};

const handleDeal = (item) => {
  console.log("item", item);
  dealObj.value = item;
  centerDialogVisible.value = true;
};

const handleTodoClick = () => {
  getTodoListFn();
};
// 表单数据
const form = reactive({
  currentState: "",
  agentName: "",
  cs: "0",
  agentSkills: [], // 修改为数组
  agentWorkGroup: [], // 修改为数组
});

// 状态列表
const status_list = {
  0: "未签入",
  1: "空闲",
  2: "通话",
  3: "通话",
  4: "通话",
  5: "通话",
  6: "话后",
  7: "离席",
  8: "离席",
  9: "离席",
  10: "话后",
};

// 查看方式列表
const cs_list = {
  0: "场所",
};

// 活动选项卡
// const activeName = ref("first");

// 场所数据
const data = ref([]);

// 当前选中的索引
const currentIndex = ref(0);

// 当前选中的场所名称
const currentName = ref("");

// 当前选中的场所ID
const currentId = ref("");

// 技能组选择
// const skillGroup = ref([]);
// 技能组列表
const skillGroupOptions = ref([]);

// 班组选择
// const workGroup = ref([]);
// 班组列表
const workGroupOptions = ref([]);

// 座席列表
const seat_list = ref([]);
const group_list = ref([]);

// 人员信息
const person_info = reactive({
  deviceType: null,
  agentId: "",
  agentType: "",
  passwordStatus: 0,
  surplusDay: 0,
  currentStateTime: 0,
  agentName: "",
  deviceNo: 0,
  agentWarnList: [],
  agentIp: "",
  loginTime: "",
  locationId: 0,
  workGroupName: "",
  skillGroupName: "",
  currentState: 0,
  phoneState: 0,
  seatNo: "",
  cb: 0,
});

// 选中的座席
const selectItem = ref({});

// 缩放比例
const zoomNumber = ref(1);

// 对话框
const centerDialogVisible = ref(false);

// 个人画像
const agentInfoVisible = ref(false);

// 历史转写记录
const historyRecordVisible = ref(false);

const currentType = ref(1);
const callData = ref([]);
const callDataTotal = ref(0);
const helpRecordData = ref({});
const helpToast = ref(0);
const helpPageIndex = ref(1);
const helpPageSize = ref(10);

// 个人求助明细
const helpRecordVisible = ref(false);

const handleGetHelpRecord = async () => {
  const result = await getHelpRecord({
    agentId: person_info.agentId,
    workId: person_info.workId,
    pageIndex: helpPageIndex.value,
    pageSize: helpPageSize.value,
  });
  helpRecordData.value = result.data.data.data;
  helpToast.value = result.data.data.totalRow;
};
const helpTitle = ref("个人求助明细");
const currentAgentId = ref("");
const currentWorkId = ref("");
const handleGetCallData = async ({
  type,
  name,
  agentId,
  workId,
  pageIndex = 1,
  pageSize = 10,
}) => {
  console.log("handleGetCallData接收参数:", {
    type,
    name,
    agentId,
    workId,
    pageIndex,
    pageSize,
  });
  currentAgentId.value = agentId;
  currentWorkId.value = workId;
  currentType.value = type;

  // 在请求前清空数据
  callData.value = [];
  callDataTotal.value = 0;

  // 调用接口获取数据
  if (workId) {
    helpTitle.value = "班组求助明细";
  } else {
    helpTitle.value = "个人求助明细";
  }
  console.log("agentId", agentId, name);
  if (type === 8) {
    const result = await getHelpRecord({
      agentId: currentAgentId.value,
      workGroupId: currentWorkId.value,
      pageIndex: helpPageIndex.value,
      pageSize: helpPageSize.value,
    });
    console.log("result", result);
    helpRecordData.value = result.data.data.data;
    helpToast.value = result.data.data.totalRow;
    helpRecordVisible.value = true;
  } else {
    // 确保使用传入的分页参数
    const requestParams = {
      type,
      agentId: currentAgentId.value,
      workGroupId: currentWorkId.value,
      pageIndex: parseInt(pageIndex, 10),
      pageSize: parseInt(pageSize, 10),
    };

    try {
      const result = await getCallData(requestParams);
      console.log("getCallData data structure:", result.data);

      // 检查数据结构
      if (result.data?.data) {
        console.log(
          "data.data exists, structure:",
          JSON.stringify(result.data.data)
        );

        callData.value = result.data.data;
        callDataTotal.value = result.data.totalRow || 0;

        console.log("Final callData:", callData.value);
        console.log("Final callDataTotal:", callDataTotal.value);
      }
    } catch (error) {
      console.error("获取通话记录失败:", error);
      // 即使出错也要显示抽屉，但数据为空
      callData.value = [];
      callDataTotal.value = 0;
    } finally {
      historyRecordVisible.value = true;
    }
  }
};

// 选中的班组
const selectWorkGroup = ref({});

// 用户类型
const userType = ref(0);

// 内容区域卡片数据
const contentCards = ref([
  {
    icon: "sl-icon.png",
    bg: "sl.png",
    title: "受理签入人数",
    value: 0,
    state: 1,
  },
  { icon: "th-icon.png", bg: "th.png", title: "通话", value: 0, state: 2 },
  { icon: "kx-icon.png", bg: "kx.png", title: "空闲", value: 0, state: 3 },
  { icon: "hh-icon.png", bg: "hh.png", title: "话后", value: 0, state: 4 },
  { icon: "sm-icon.png", bg: "sm.png", title: "离席", value: 0, state: 5 },
  { icon: "pd-icon.png", bg: "pd.png", title: "排队", value: 0, state: 6 },
]);

// 底部菜单数据
const bottomCards = ref([
  {
    title: "超长通话",
    value: 0,
    state: 7,
  },
  {
    title: "话后超时",
    value: 0,
    state: 8,
  },
  {
    title: "静音",
    value: 0,
    state: 9,
  },
  {
    title: "语速过快",
    value: 0,
    state: 10,
  },
  {
    title: "抢话",
    value: 0,
    state: 11,
  },
  {
    title: "坐席违规词",
    value: 0,
    state: 12,
  },
  {
    title: "市民敏感词",
    value: 0,
    state: 13,
  },
  {
    title: "求助",
    value: 0,
    state: 14,
  },
  // {
  //   title: "首次诉求",
  //   value: 0,
  //   state: 15,
  // },
]);

// 定时器
let timer1 = null;
let personalInfoTimer = null; // 个人画像刷新定时器

// 点击内容区域卡片
const handleContentClick = (state) => {
  console.log("点击了内容区域卡片，状态为:", state);
  if (state === 6) return;
  if (form.currentState === state) {
    form.currentState = "";
  } else {
    form.currentState = state;
  }
  getSeat(true); // 传递true表示手动搜索
};

// 技能组选择
const handleSkillGroupChange = (value) => {
  form.agentSkills = value;
  console.log("form.agentSkills", form.agentSkills);
  getSeat(true); // 传递true表示手动搜索
};

// 班组选择
const handleAgentWorkGroupChange = (value) => {
  form.agentWorkGroup = value;
  console.log("form.agentWorkGroup", form.agentWorkGroup);
  getSeat(true); // 传递true表示手动搜索
};

// 查询场所
const queryPhoneRoomFn = async () => {
  try {
    // 只在第一次加载时请求树状数据
    if (!data.value || Object.keys(data.value).length === 0) {
      const res = await queryPhoneRoom();
      if (res.data) {
        data.value = res.data.data;
        // 默认选中"话房列表"节点
        currentName.value = data.value.NAME;
        currentId.value = data.value.CODE;
        currentIndex.value = data.value.CODE;
      }
    }

    // 只更新座席数据
    const agentId = getQueryParam("agentId", "");
    if (agentId) {
      jumpResult(agentId);
    } else {
      getSeat();
    }
  } catch (err) {
    ElMessage.error(err.message || "查询场所失败");
  }
};

// 从人员活动列表点双击后，跳到坐席位置图
const jumpResult = async (agentId) => {
  try {
    const res = await queryAgentById(agentId);
    if (res.data) {
      const array = res.data;
      seat_list.value = [array.data];
      seat_list.value.forEach((element) => {
        element.select = false;
      });
      currentId.value = array.data.code;
      currentIndex.value = Number(array.data.code) - 1;

      data.value.forEach((el) => {
        if (el.id == array.data.code) {
          currentName.value = el.name;
        }
      });
    }
  } catch (err) {
    ElMessage.error(err.message || "查询座席失败");
  }
};

// 添加一个标志表示当前是否正在请求中
let isGetSeatRequesting = false;
// 添加请求队列
const seatRequestQueue = [];

// 添加一个变量跟踪自动请求的abort controller
let autoRequestController = null;

// 封装queryAgentPlace调用，支持AbortController
const queryAgentPlace = async (params, options = {}) => {
  try {
    return await apiQueryAgentPlace(params, options);
  } catch (error) {
    // 将取消的请求错误转换为AbortError
    if (axios.isCancel(error)) {
      const abortError = new Error("请求被取消");
      abortError.name = "AbortError";
      throw abortError;
    }
    throw error;
  }
};

// 查询座席位置
const getSeat = async (isManual = false) => {
  // 如果是手动搜索，暂停轮询
  if (isManual) {
    isManualSearch.value = true;
    if (timer1) {
      clearInterval(timer1);
      timer1 = null;
    }

    // 如果有自动请求正在进行中，取消它
    if (autoRequestController) {
      console.log("取消当前自动请求，优先处理手动请求");
      autoRequestController.abort();
      autoRequestController = null;
    }
  }

  // 如果当前正在请求中，将请求添加到队列中并返回
  if (isGetSeatRequesting) {
    // 如果是手动请求，并且队列中有请求，将手动请求放到队列前面
    if (isManual) {
      return new Promise((resolve) => {
        seatRequestQueue.unshift({
          isManual,
          resolve,
        });
      });
    } else {
      return new Promise((resolve) => {
        seatRequestQueue.push({
          isManual,
          resolve,
        });
      });
    }
  }

  // 标记开始请求
  isGetSeatRequesting = true;

  // 为非手动请求创建abort controller
  if (!isManual) {
    autoRequestController = new AbortController();
  }

  try {
    const params = {
      messageId: currentTab.value,
      hfCode: currentId.value,
      agentName: form.agentName,
      currentState: form.currentState,
      agentSkills: form.agentSkills,
      agentWorkGroup: form.agentWorkGroup,
    };

    // 使用不同的fetch选项区分手动和自动请求
    let fetchOptions = {};
    if (!isManual && autoRequestController) {
      fetchOptions.signal = autoRequestController.signal;
    }

    // 使用自定义选项调用接口
    const res = await queryAgentPlace(params, fetchOptions);
    if (!res.data) return;

    // 处理座席数据
    if (currentTab.value === "queryAgentPlace") {
      // 确保按区域时数据是数组格式
      const array = res.data.data.userMap || [];
      // 确保是二维数组格式
      seat_list.value = Array.isArray(array) ? array : [];
    } else {
      // 按技能组时的数据处理
      const groupData = res.data.data.userMap || {};
      group_list.value = groupData;
    }

    // 使用解构赋值简化数据获取
    const { statData = {} } = res.data.data;

    // 更新卡片数据
    updateCards(statData);
  } catch (err) {
    // 如果是被取消的请求，不显示错误
    if (err.name === "AbortError") {
      console.log("请求被取消");
    } else {
      console.error("查询座席位置失败:", err);
    }
  } finally {
    // 如果是手动搜索，延迟一段时间后恢复轮询，确保DOM更新完成
    if (isManual && isManualSearch.value) {
      setTimeout(() => {
        isManualSearch.value = false;
        startTimer();
      }, 300); // 延迟300毫秒再重启定时器
    }

    // 清除abort controller引用
    if (!isManual) {
      autoRequestController = null;
    }

    // 标记请求结束
    isGetSeatRequesting = false;

    // 检查队列中是否有等待的请求
    if (seatRequestQueue.length > 0) {
      const nextRequest = seatRequestQueue.shift();
      // 延迟一点时间再执行下一个请求，确保DOM更新完成
      setTimeout(() => {
        getSeat(nextRequest.isManual).then(nextRequest.resolve);
      }, 100);
    }
  }
};

// 抽取更新卡片的逻辑为单独的函数
const updateCards = (statData) => {
  // 更新顶部卡片
  const cardUpdates = {
    loginCount: 0,
    talkingCount: 1,
    ideaCount: 2,
    agentRecordHandleCount: 3,
    setBusyCount: 4,
    callWaitNums: 5,
  };
  Object.entries(cardUpdates).forEach(([key, index]) => {
    if (contentCards.value[index]) {
      contentCards.value[index].value = statData[key] || 0;
    }
  });

  // 更新底部卡片
  const bottomCardUpdates = {
    extraLongCallCount: 0,
    afterLongCount: 1,
    muteCount: 2,
    speechSpeedCount: 3,
    robTalkCount: 4,
    violationWordCount: 5,
    sensitiveWordCount: 6,
    seekHelpCount: 7,
    debugAppealCount: 8,
  };

  Object.entries(bottomCardUpdates).forEach(([key, index]) => {
    if (bottomCards.value[index]) {
      bottomCards.value[index].value = statData[key] || 0;
    }
  });
};

const changeCurrentTab = (value) => {
  // 切换tab前清空数据
  if (value === "queryAgentPlace") {
    seat_list.value = [];
  } else {
    group_list.value = {};
  }
  currentTab.value = value;
  getSeat(true);
};

// 点击菜单
const handleMenu = (index, item) => {
  currentName.value = item.NAME;
  currentId.value = item.CODE;
  currentIndex.value = index;
  getSeat(true);
  // console.log("item", item);
  // if (item.hfWarnList && item.hfWarnList.length) {
  //   jumpClick(item.hfWarnList);
  // }
};

// 查看座席详情
const handleView = async (item) => {
  // ElMessage.warning("详情页面开发中");
  agentInfoVisible.value = false;
  selectItem.value = item;

  if (item.agentId) {
    let msgStr = [];
    if (item.seekHelpMsgId) {
      msgStr.push(item.seekHelpMsgId);
    }
    if (item.extraLongCallMsgId) {
      msgStr.push(item.extraLongCallMsgId);
    }
    if (item.speechSpeedMsgId) {
      msgStr.push(item.speechSpeedMsgId);
    }
    if (item.afterLongMsgId) {
      msgStr.push(item.afterLongMsgId);
    }

    try {
      const data = {
        agentId: item.agentId,
        // seatNo: item.seatNo,
        // msgId: msgStr.join(","),
        // pageIndex: 1, //首发诉求分页
        // pageSize: 30, //首发诉求分页数
      };

      const res = await queryAgentInfoById(data);
      if (res.data) {
        const obj = res.data;

        Object.assign(person_info, obj.data);

        agentInfoVisible.value = true;

        // 启动个人画像刷新定时器
        startPersonalInfoTimer(data);
      }
    } catch (err) {
      ElMessage.error(err.message || "查询座席详情失败");
    }
  }
};

// 启动个人画像刷新定时器
const startPersonalInfoTimer = (queryData) => {
  // 先清除可能存在的定时器
  if (personalInfoTimer) {
    clearInterval(personalInfoTimer);
  }

  // 设置新的定时器，每10秒刷新一次个人画像数据
  personalInfoTimer = setInterval(async () => {
    if (!agentInfoVisible.value) {
      clearInterval(personalInfoTimer);
      return;
    }

    try {
      const res = await queryAgentInfoById(queryData);
      if (res.data) {
        Object.assign(person_info, res.data.data);
      }
    } catch (err) {
      console.error("刷新个人画像失败:", err);
    }
  }, 3000);
};

// 监听个人画像抽屉关闭，清除定时器
const closeAgentInfo = () => {
  console.log(personalInfoRef.value.monitorActive); // 监听状态
  console.log(personalInfoRef.value.interceptActive); // 强插状态
  console.log(personalInfoRef.value.whisperActive); // 密语状态

  if(personalInfoRef.value.monitorActive || personalInfoRef.value.interceptActive || personalInfoRef.value.whisperActive) {
    return  ElMessage.error("请先结束干预操作");
  }
  agentInfoVisible.value = false;
  if (personalInfoTimer) {
    clearInterval(personalInfoTimer);
    personalInfoTimer = null;
  }
};

const workGroupInfoVisible = ref(false);
const workGroupInfo = ref({});
const handleWorkGroupInfo = async (group) => {
  console.log("group", group.users[0].agentGroup);
  selectWorkGroup.value = group;
  try {
    const data = {
      workGroupId: group.users[0].agentGroup,
      // seatNo: item.seatNo,
      // msgId: msgStr.join(","),
    };

    const res = await getWorkGroupInfo(data);
    if (res.data) {
      const obj = res.data;
      workGroupInfo.value = obj.data;
      workGroupInfoVisible.value = true;
    }
  } catch (err) {
    ElMessage.error(err.message || "查询座席详情失败");
  }
};

// 跳转到指定座席
// const jumpClick = (arr) => {
//   console.log("arr", arr);
//   if (arr && arr.length) {
//     seat_list.value.forEach((rows) => {
//       console.log("rows", rows);
//       if (rows.seatNo === arr[0]) {
//         handleView(rows);
//       }
//     });

//     setTimeout(() => {
//       const element = document.querySelector(`#S${arr[0]}`);
//       if (element) {
//         element.scrollIntoView({ behavior: "smooth" });
//       }
//     }, 100);
//   }
// };

// 批量处理
const batch = () => {
  batchDialogVisible.value = true;
};

// 获取用户类型
const getUsetTypeFn = async () => {
  try {
    const res = await getUsetType();
    if (res.data) {
      userType.value = res.data.data;
      localStorage.setItem("userType", userType.value);
      console.log("userType", userType.value);
    }
  } catch (err) {
    ElMessage.error(err.message || "获取用户类型失败");
  }
};

const startTimer = () => {
  // 清除现有定时器
  if (timer1) {
    clearInterval(timer1);
    timer1 = null;
  }

  // 如果正在手动搜索，不启动轮询
  if (isManualSearch.value) {
    return;
  }

  // 延迟一小段时间后再启动定时器，确保DOM更新完成
  setTimeout(() => {
    // 再次检查是否仍在手动搜索状态
    if (isManualSearch.value) return;

    let isRequesting = false;
    timer1 = setInterval(async () => {
      // 如果手动搜索中或已有轮询请求在进行中或当前getSeat正在请求中，则跳过此次轮询
      if (isRequesting || isManualSearch.value || isGetSeatRequesting) return;

      try {
        isRequesting = true;
        // 根据当前tab状态决定是否需要更新树状数据
        if (currentTab.value === "queryAgentPlace") {
          await queryPhoneRoomFn();
        } else {
          // 其他tab只需要更新座席数据
          await getSeat();
        }
      } catch (error) {
        console.error("轮询更新失败:", error);
      } finally {
        isRequesting = false;
      }
    }, 5000);
  }, 200);
};

// 处理缩放变化
const handleWheel = (newZoom) => {
  zoomNumber.value = newZoom;
};

const currentTab = ref("queryAgentPlace");

// 获取技能组列表
const getSkillGroupListFn = async () => {
  try {
    const res = await getSkillGroupList();
    if (res.data && res.data.result === "000") {
      skillGroupOptions.value = res.data.data.map((item) => ({
        label: item.SKILLGROUPNAME,
        value: item.SKILLGROUPID,
      }));

      // 获取URL中的skillId参数
      const skillId = getQueryParam("skillId", "");

      // 初始化技能组选择
      if (skillId) {
        // 如果URL中有skillId参数，将其添加到技能组选择中
        form.agentSkills = [skillId];
      } else {
        // 否则使用第一个技能组作为默认值
        form.agentSkills = [skillGroupOptions.value[0].value];
      }

      handleSkillGroupChange(form.agentSkills);
    }
  } catch (err) {
    ElMessage.error(err.message || "获取技能组列表失败");
  }
};

const getWorkGroupListFn = async () => {
  try {
    const res = await getWorkGroupList();
    if (res.data && res.data.result === "000") {
      workGroupOptions.value = res.data.data.map((item) => ({
        label: item.WORKGROUP,
        value: item.WORKGROUPID,
      }));
    }
  } catch (err) {
    ElMessage.error(err.message || "获取班组列表失败");
  }
};

// 添加 isIframe 变量
const isIframe = ref(false);

// 检查 URL 参数
const checkIsIframe = () => {
  const hash = window.location.hash;
  console.log("完整哈希:", hash);

  // 检查哈希中是否包含查询参数
  if (hash.includes("?")) {
    // 提取查询参数部分
    const queryString = hash.split("?")[1];

    // 创建URLSearchParams对象解析参数
    isIframe.value = queryString.indexOf("iframe=true") !== -1;
  } else {
    isIframe.value = false;
  }

  console.log("iframe模式:", isIframe.value);
};

// 处理查看模板详情
const handleViewTemplateDetail = async (templateData) => {
  console.log("templateData", templateData);
  try {
    const res = await getTemplateDetail({
      templateId: templateData.id,
      templateType: templateData.templateType,
    });
    if (res.data) {
      templateDetailInfo.value = res.data.data;
      templateDetailVisible.value = true;
    } else {
      ElMessage.error(res.data?.msg || "获取模板详情失败");
    }
  } catch (error) {
    console.error("获取模板详情失败：", error);
    ElMessage.error("获取模板详情失败");
  }
};
const addMessageVisible = ref(false);
// 处理创建消息
const handleCreateMessage = (type) => {
  console.log("接收到创建消息类型:", type);
  messageType.value = type;
  addMessageVisible.value = true;
};
const messageListLoading = ref(false);
const onLoading = (loading) => {
  messageListLoading.value = loading;
};
// 处理消息分页参数变化
const handleMessageParamsChange = async (params) => {
  messageListLoading.value = true;
  Object.assign(messageParams, params);
  console.log("分页传进来的参数messageParams", messageParams, params);

  try {
    // 根据不同类型调用不同接口
    if (messageType.value === 1) {
      // 短信下发明细
      console.log("短信下发明细参数", messageParams);
      const res = await getSmsList({
        pageType: "3",
        pageIndex: messageParams.pageIndex, //页码
        pageSize: messageParams.pageSize, //每页条数
        startTime: messageParams.startTime, //开始时间
        endTime: messageParams.endTime, //结束时间
        agentName: messageParams.agentName, //坐席姓名
        agentNum: messageParams.agentNum, //坐席工号
        phone: messageParams.phone, //坐席手机号
        submitUser: messageParams.submitUser, //提交人
        receiptState: messageParams.receiptState, //回执状态
        sendState: messageParams.sendState, //发送状态
      });
      if (res.data) {
        messageList.value = res.data.data || [];
        messageTotal.value = res.data.totalRow || 0;
      }
    } else if (messageType.value === 2) {
      // 外呼通知明细
      console.log("外呼通知明细参数", messageParams);
      const res = await getCallList({
        pageType: "3",
        pageIndex: messageParams.pageIndex, //页码
        pageSize: messageParams.pageSize, //每页条数
        startTime: messageParams.startTime, //开始时间
        endTime: messageParams.endTime, //结束时间
        agentName: messageParams.agentName, //坐席姓名
        agentNum: messageParams.agentNum, //坐席工号
        phone: messageParams.phone, //坐席手机号
        submitUser: messageParams.submitUser, //提交人
        callResult: messageParams.callResult, //外呼结果
      });
      if (res.data) {
        messageList.value = res.data.data || [];
        messageTotal.value = res.data.totalRow || 0;
      }
    } else if (messageType.value === 0) {
      // 助手消息明细
      const res = await getAssistantList({
        startTime: messageParams.startTime,
        endTime: messageParams.endTime,
        msgRemindType: messageParams.msgRemindType,
        page: messageParams.pageIndex,
        limit: messageParams.pageSize,
      });
      if (res.data) {
        // 如果接口返回数据为空，使用模拟数据
        if (!res.data.data || res.data.data.length === 0) {
          messageList.value = [];
          messageTotal.value = 3;
        } else {
          messageList.value = res.data.data;
          messageTotal.value = res.data.count || 0;
        }
      }
    }
  } catch (error) {
    console.error("获取消息明细失败:", error);
    ElMessage.error("获取消息明细失败");

    // 如果是助手消息明细，即使接口失败也显示模拟数据
    if (messageType.value === 0) {
      messageList.value = [];
      messageTotal.value = 3;
    } else {
      messageList.value = [];
      messageTotal.value = 0;
    }
  } finally {
    messageListLoading.value = false;
  }
};

// 处理消息详情查看
const handleMessageDetail = (item) => {
  console.log("查看消息详情:", item);
  // 将当前消息项数据传递给消息详情组件
  messageDetailInfo.value = item;
  // 显示消息详情抽屉
  messageDetailVisible.value = true;
};

// 在消息列表组件中添加处理"查看明细"的函数
const messageInnerDetailVisible = ref(false);
const messageInnerDetailInfo = ref([]);

// 处理消息明细
const handleMessageInnerDetail = async (item, params = {}) => {
  console.log("查看消息明细:", item, params);

  try {
    // 默认查询参数
    const queryParams = {
      msgId: item.MSG_ID, // 消息ID
      page: params.page || 1,
      limit: params.limit || 10,
      // 添加可选的筛选条件
      agentName: params.agentName || "",
      agentNo: params.agentNo || "",
      agentPhone: params.agentPhone || "",
      msgRemindType: params.msgRemindType || "",
      msgSendStatus: params.msgSendStatus || "",
      msgReadStatus: params.msgReadStatus || "",
    };

    // 调用获取消息明细接口
    const res = await getMessageDetail(queryParams);

    if (res.data && res.data.code === 0) {
      messageInnerDetailInfo.value = {
        ...item,
        detailList: res.data.data || [],
        total: res.data.count || 0,
        currentParams: queryParams, // 保存当前查询参数，用于组件内分页和筛选
      };

      // 首次查询时显示抽屉
      if (!params.isRefresh) {
        messageInnerDetailVisible.value = true;
      }
    }
  } catch (error) {
    console.error("获取消息明细失败:", error);
    ElMessage.error("获取消息明细失败");

    // 首次查询时显示抽屉
    if (!params.isRefresh) {
      messageInnerDetailVisible.value = true;
    }
  }
};
onMounted(() => {
  getUsetTypeFn();
  checkIsIframe();
  queryPhoneRoomFn();
  // 延迟启动轮询，确保组件已完全挂载
  setTimeout(() => {
    startTimer();
  }, 500);
  getSkillGroupListFn();
  getWorkGroupListFn();
});

onBeforeUnmount(() => {
  if (timer1) {
    clearInterval(timer1);
    timer1 = null;
  }

  // 清除个人画像定时器
  if (personalInfoTimer) {
    clearInterval(personalInfoTimer);
    personalInfoTimer = null;
  }
});

// 添加一个标志位表示是否手动搜索
const isManualSearch = ref(false);
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  height: 100%;

  background: url("../assets/image/bg-black.jpg") no-repeat center center;
  background-size: cover;
}

.bottom {
  height: calc(100% - 82px);
  display: flex;
  overflow: hidden;
}
.selectBox {
  display: flex;
  margin: 12px 0 8px 0;
  align-items: center;
  .addinput {
    margin-right: 10px;
  }
  .btn {
    height: 32px;
    display: flex;
    align-items: center;
    padding: 5px 16px;
    border-radius: 4px;
    background: rgba(0, 255, 255, 0.1);
    box-sizing: border-box;
    font-family: Alibaba PuHuiTi 3;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: normal;
    color: #00ffff;
    cursor: pointer;
    margin-right: 16px;
    white-space: nowrap;
    flex-shrink: 0;

    &:hover {
      background-image: url(@/assets/image/header/hoverBtn.png);
      background-size: 100% 100%;
    }

    &.active {
      background-image: url(@/assets/image/header/hoverBtn.png);
      background-size: 100% 100%;
    }

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
}
.bottom-right {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;

  .seat-map-title {
    width: 506px;
    background: url(@/assets/image/right/title-bg.png) no-repeat left bottom;
    padding-left: 24px;
    span {
      font-size: 24px;
      font-weight: bold;
      line-height: 36px;
      letter-spacing: normal;
    }
  }
  .right-top {
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-bottom: 16px;
  }
  .right-top-right {
    display: flex;
    flex-direction: row;
    flex: 1;
    overflow: hidden;
    // padding-right: 16px;
    .right-top-right-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex: 1;
      flex-wrap: nowrap;
      justify-content: end;
      .label {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        color: #ffffff;
        margin-right: 16px;
        white-space: nowrap;
      }
      .btn {
        height: 32px;
        display: flex;
        align-items: center;
        padding: 5px 16px;
        border-radius: 4px;
        background: rgba(0, 255, 255, 0.1);
        box-sizing: border-box;
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        color: #00ffff;
        cursor: pointer;
        margin-right: 16px;
        white-space: nowrap;
        flex-shrink: 0;

        &:hover {
          background-image: url(@/assets/image/header/hoverBtn.png);
          background-size: 100% 100%;
        }

        &.active {
          background-image: url(@/assets/image/header/hoverBtn.png);
          background-size: 100% 100%;
        }

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
      .line {
        height: 16px;
        width: 1px;
        background: rgba(0, 255, 255, 0.1);
        margin: 0 16px;
      }

      :deep(.condition-select) {
        width: 160px !important;
        height: 32px !important;
        flex-shrink: 0; /* 防止选择框被压缩 */

        .el-input__wrapper {
          height: 32px !important;
          line-height: 32px !important;
          box-sizing: border-box;
        }

        .el-input__inner {
          height: 32px !important;
          line-height: 32px !important;
        }

        .el-select__tags {
          height: 100%;
          overflow: hidden;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
.right-content {
  overflow: hidden;
  flex-shrink: 0;
  .content-top {
    display: flex;
    justify-content: space-between;
    .content-top-item {
      width: 246px;
      height: 80px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      padding: 16px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      position: relative;
      .rtPostion {
        position: absolute;
        right: -16px;
        top: -1px;
        width: 28px;
        height: 28px;
      }
      img {
        width: 48px;
        height: 48px;
        margin-right: 16px;
      }
      .content-top-item-title {
        /* 文字/正文 */
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        text-align: right;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/白色-文字用色 */
        /* 样式描述：文字主要用色 */
        color: #ffffff;
      }
      .content-top-item-content {
        font-family: zcoolqingkehuangyouti;
        font-size: 24px;
        font-weight: normal;
        line-height: 36px;
        text-align: right;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/白色-文字用色 */
        /* 样式描述：文字主要用色 */
        color: #ffffff;
      }
    }
  }

  .content-bottom {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    margin-top: 16px;
    justify-content: space-between;
    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 255, 255, 0.3);
      border-radius: 2px;
    }

    .content-bottom-item {
      min-width: 189px;
      height: 40px;
      border-radius: 3.2px;
      /* 数据图表色/苍青 */
      background: rgba(0, 128, 255, 0.1);
      display: flex;
      align-items: center;
      padding: 9px 16px;
      box-sizing: border-box;
      justify-content: space-between;

      .content-bottom-item-title {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: normal;
        color: #ffffff;
      }
      .content-bottom-item-content {
        font-family: zcoolqingkehuangyouti;
        font-size: 20px;
        font-weight: normal;
        line-height: 30px;
        text-align: right;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        margin-left: 12px;
      }
    }
    .active-bottom-item {
      background: url(@/assets/image/right/active-bottom.png) no-repeat center
        center;
      background-size: 100% 100%;
      .content-bottom-item-title {
        color: #ffdc00;
      }
      .content-bottom-item-content {
        /* 主色/卓越青 */
        color: #ffdc00;
      }
    }
  }
}
.right-head {
  box-sizing: border-box;
  height: 56px;
  padding: 16px 0px 16px 0px;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;

  .right-head-right {
    display: flex;
    align-items: center;

    .tl {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 16px;
      gap: 16px;
      .tl-item {
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/白色-文字用色 */
        /* 样式描述：文字主要用色 */
        color: #ffffff;
        img {
          width: 18px;
          height: 18px;
          margin-right: 4px;
        }
      }
    }

    .tl-line {
      width: 1px;
      height: 16px;
      background-color: rgba(0, 255, 255, 0.1);
      margin-right: 16px;
    }
    &-label {
      font-size: 14px;
      color: #ffffff;
      margin-right: 16px;
    }
    &-item {
      width: 160px;

      :deep(.el-slider) {
        width: 160px;
        height: 16px;
      }

      :deep(.el-slider__runway) {
        height: 16px;
        background-color: rgba(0, 255, 255, 0.1);
      }

      :deep(.el-slider__bar) {
        height: 16px;
        background-color: #00ffff;
      }

      :deep(.el-slider__button-wrapper) {
        top: -10px;
      }

      :deep(.el-slider__button) {
        border-color: #00ffff;
      }
    }
  }
}

.right-head-span {
  color: #262626;
  font-size: 18px;
  font-weight: bold;
  &::before {
    content: "";
    display: inline-block;

    width: 4px;
    height: 14px; /* 主色/卓越青 */
    background: #00ffff;
    margin-right: 8px;
  }
}

.right-bottom {
  display: flex;
  flex: 1;
  width: 100%;
  overflow: hidden;
  min-height: 0;
  max-height: calc(100% - 260px); // 减去其他组件的大致高度

  :deep(.seat-map) {
    width: 100%;
    height: 100%;
  }
}

// 添加 iframe 模式样式
.iframe-mode {
  background: none !important;

  .bottom-right {
    .seat-map-title {
      background: none !important;
    }
  }

  .right-top-right-item {
    .btn {
      &:hover,
      &.active {
        background-image: none !important;
        background-color: rgba(0, 255, 255, 0.3) !important;
      }
    }
  }

  .content-top-item {
    background-image: none !important;
    background-color: rgba(0, 128, 255, 0.1) !important;
  }

  .content-bottom-item {
    &.active-bottom-item {
      background-image: none !important;
      background-color: rgba(0, 255, 255, 0.3) !important;
    }
  }
}
</style>
