<template>
  <div class="content-box">
    <div class="personal-info">
      <!-- 完成区域 开始 -->
      <!-- 个人基本信息 -->
      <div class="agent-basic">
        <div class="agent-avatar">
          <img :src="info.url" alt="" />
        </div>
        <div class="agent-info">
          <div class="info-row nameBox">
            <span class="name fontStyle">{{ info.agentName }}</span>
            <div class="honorBox">
              <div class="honorItem king" v-if="info?.agentScoreInfo?.HONOR1">
                <img src="../assets/image/king-icon.png" alt="" />
                {{ info?.agentScoreInfo?.HONOR1 }}
              </div>
              <div class="honorItem silver" v-if="info?.agentScoreInfo?.HONOR2">
                <img src="../assets/image/silver-icon.png" alt="" />
                {{ info?.agentScoreInfo?.HONOR2 }}
              </div>
              <div class="honorItem copper" v-if="info?.agentScoreInfo?.HONOR3">
                <img src="../assets/image/copper-icon.png" alt="" />
                {{ info?.agentScoreInfo?.HONOR3 }}
              </div>
            </div>
          </div>
          <div class="info-row">
            <div>
              <span class="label">工号：</span>
              <span class="value number">{{ info.agentId }}</span>
            </div>
            <div>
              <span class="label">性别： </span>
              <span class="value">{{ info.sex || "--" }}</span>
            </div>
            <div>
              <span class="label">年龄：</span>
              <span class="value number">{{ info.brithDate || "--" }}</span>
              <span class="value">岁</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 基础信息区域 -->
      <div class="basic-info">
        <div class="info-title">
          <div class="title-text fontStyle">基础信息</div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <div class="label w100">受理业务类型：</div>
            <span class="value">{{ info.businessType }}</span>
          </div>
          <div class="info-item">
            <div class="label w70">入职年限：</div>
            <span class="value number">{{ info.entryDate }}年</span>
          </div>
          <div class="info-item">
            <div class="label w100">分机号：</div>
            <span class="value number">{{ info.agentPhone }}</span>
          </div>
          <div class="info-item">
            <div class="label w70">所属班组：</div>
            <span class="value">{{ info.workGroupName }}</span>
          </div>
          <div class="info-item">
            <div class="label w100">区号：</div>
            <span class="value">{{ getRoomLocation(info.roomLocation) }}</span>
          </div>
          <div class="info-item">
            <div class="label w70">座位号：</div>
            <span class="value number">{{ info.seatNo }}</span>
          </div>
          <div class="info-item">
            <div class="label w100">上班时间：</div>
            <span class="value number">{{ info.loginTime }}</span>
          </div>
          <div class="info-item">
            <div class="label w70">技能组：</div>
            <span class="value">{{ info.skillGroupName }}</span>
          </div>
        </div>
      </div>
      <!-- 个人活动信息 -->
      <div class="activity-info">
        <div class="info-title">
          <div class="title-text fontStyle">个人话务信息</div>
        </div>
        <div class="info-content">
          <div class="activity-item currentStatus">
            <div class="currentStatus-info">
              <div class="currentStatus-info-title">当前状态</div>
              <div class="currentStatus-info-status">
                <span class="dot"></span>{{ getAgentState(info?.currentState) }}
                <img
                  src="../assets/image/sszx.png"
                  alt=""
                  @click="handleShowCallInfo"
                  v-if="
                    ['2', '3', '4', '5'].includes(info?.currentState) &&
                    !showCallInfo
                  "
                />
                <img
                  src="../assets/image/active-sszx.png"
                  alt=""
                  @click="handleShowCallInfo"
                  v-if="
                    ['2', '3', '4', '5'].includes(info?.currentState) &&
                    showCallInfo
                  "
                />
              </div>
            </div>
          </div>
          <div class="activity-item currentTime">
            <div class="currentTime-info">
              <div class="currentTime-info-title">当前状态持续时间</div>
              <div class="currentTime-info-status number">
                {{ useFormatTime(info?.currentStateTime).formattedTime }}
              </div>
            </div>
          </div>
        </div>
        <div class="workload-day">
          <div class="left-top-title">当天话务</div>
          <div class="workload-day-content">
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听量</div>
              <div class="workload-day-content-item-value">
                {{ info?.toDayCall?.CALL_IN_COUNT_ALL || 0 }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均通话时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.AVG_CALL_IN_TIME).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均话后时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.AVG_ARRANGE_TIME).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">签入总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.LOGIN_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听总时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.CALL_IN_TIME_ALL).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">话后总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.ARRANGE_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">离席总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.BUSY_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">挂机满意度</div>
              <div class="workload-day-content-item-value">
                {{ info?.toDayCall?.GOOD_PERCENT || 0 }}%
              </div>
            </div>
          </div>
        </div>
        <div class="workload-info">
          <div class="left-top-title">工作量</div>
          <div class="tabBox">
            <div
              class="tabItem"
              :class="{ active: currentTab === 'day' }"
              @click="handleTabClick('day')"
            >
              <span class="tabItem-text">日</span>
            </div>
            <div
              class="tabItem"
              :class="{ active: currentTab === 'week' }"
              @click="handleTabClick('week')"
            >
              <span class="tabItem-text">周</span>
            </div>
            <div
              class="tabItem"
              :class="{ active: currentTab === 'month' }"
              @click="handleTabClick('month')"
            >
              <span class="tabItem-text">月</span>
            </div>
          </div>
          <div class="workload-info-content">
            <div class="workload-echarts" ref="workloadEcharts"></div>
          </div>
        </div>
        <div class="last-month-score">
          <div class="left-top-title">上月综合评分</div>
          <div class="last-month-score-content">
            <div
              class="last-month-score-echarts"
              ref="lastMonthScoreEcharts"
            ></div>
            <div class="last-month-score-text-content">
              <div class="last-month-score-text">
                <div class="last-month-score-text-item">
                  <div class="last-month-score-text-item-title">总分</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.agentScoreInfo?.ALL_SCORE || 0 }}
                  </div>
                </div>
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">扣减</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.agentScoreInfo?.DEDUCTION || 0 }}
                  </div>
                </div>
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">奖励</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.agentScoreInfo?.REWARD || 0 }}
                  </div>
                </div>
              </div>
              <div class="last-month-score-text1">
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">月度排名</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.agentScoreInfo?.MONTHLY_RANKING || 0 }}
                  </div>
                </div>
              </div>
              <div class="last-month-score-text1">
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">挂机满意度</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.agentScoreInfo?.ON_HOOK_SATISFACTION || 0 }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人告警信息 -->
      <div class="workload">
        <div class="info-title">
          <div class="title-text fontStyle">个人告警信息</div>
        </div>
        <div class="info-content">
          <div class="info-content-grid">
            <div
              class="info-content-item"
              @click="handleAlarmClick(1, '超长通话', info?.agentId)"
            >
              <div class="item-title">
                <span>超长通话</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.CALL_LONG_COUNT || 0 }}
              </div>
            </div>
            <div class="info-content-item">
              <div class="item-title">
                <span>话后超时</span>
                <!-- <img src="../assets/image/arrow-icon.png" alt="" /> -->
              </div>
              <div class="item-value">
                {{ info?.alarms?.PHONE_AFTEL_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(3, '静默', info?.agentId)"
            >
              <div class="item-title">
                <span>静默</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.VOICE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(4, '语速过快', info?.agentId || '')"
            >
              <div class="item-title">
                <span>语速过快</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.SPEECH_FAST_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(5, '抢话', info?.agentId || '')"
            >
              <div class="item-title">
                <span>抢话</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.ROB_TRAFFICE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(6, '坐席违规词', info?.agentId || '')"
            >
              <div class="item-title">
                <span>坐席违规词</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.DISABLE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(7, '市民敏感词', info?.agentId || '')"
            >
              <div class="item-title">
                <span>市民敏感词</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.SENS_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(8, '求助', info?.agentId || '')"
            >
              <div class="item-title">
                <span>求助</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.HELP_COUNT || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 完成区域 结束 -->
      <div class="workHeight" v-if="ccbarService.isLoggedIn()"></div>
      <!-- 话务条 -->
      <div class="workload-bar" v-if="ccbarService.isLoggedIn()">
        <div
          class="workload-bar-item"
          :class="{ disabled: isAnyFunctionActive }"
          @click="handleForceSignOut"
        >
          <img src="../assets/image/force-sign-out.png" alt="" />
          强制签出
        </div>
        <div
          class="workload-bar-item"
          :class="{
            disabled: !isInCall || interceptActive || whisperActive,
            active: monitorActive,
          }"
          @click="handleMonitor"
        >
          <img src="../assets/image/listen.png" alt="" />
          {{ monitorActive ? "结束监听" : "监听" }}
        </div>
        <div
          class="workload-bar-item"
          :class="{
            disabled: !isInCall || monitorActive || whisperActive,
            active: interceptActive,
          }"
          @click="handleIntercept"
        >
          <img src="../assets/image/strong-insert.png" alt="" />
          {{ interceptActive ? "结束强插" : "强插" }}
        </div>
        <div
          class="workload-bar-item"
          :class="{
            disabled: !(monitorActive || interceptActive || whisperActive),
            active: bargeInActive,
          }"
          @click="handleBargeIn"
        >
          <img src="../assets/image/intercept.png" alt="" />
          拦截
        </div>
        <div
          class="workload-bar-item"
          :class="{
            disabled:
              !isInCall || monitorActive || interceptActive || whisperActive,
          }"
          @click="handleForceDisconnect"
        >
          <img src="../assets/image/strong-disconnection.png" alt="" />
          强拆
        </div>
        <div
          class="workload-bar-item"
          :class="{
            disabled: !isInCall || monitorActive || interceptActive,
            active: whisperActive,
          }"
          @click="handleWhisper"
        >
          <img src="../assets/image/secret-language.png" alt="" />
          {{ whisperActive ? "结束密语" : "密语" }}
        </div>
        <!-- TODO: 强置闲和强置忙功能暂时隐藏 -->
        <div
          class="workload-bar-item"
          style="visibility: hidden"
          @click="handleForceIdle"
          v-if="getAgentState(info?.currentState) !== '空闲'"
        >
          <img src="../assets/image/strong-idle.png" alt="" />
          强置闲
        </div>
        <div
          class="workload-bar-item"
          @click="handleForceBusy"
          style="visibility: hidden"
          v-else
        >
          <img src="../assets/image/strong-idle.png" alt="" />
          强置忙
        </div>
      </div>
    </div>

    <div class="imbox-line" v-if="showCallInfo"></div>

    <div class="imbox" v-if="showCallInfo" ref="imboxRef">
      <div class="conversation-list">
        <div
          v-for="(item, index) in CallInfoData.callInfo?.txtList"
          :key="index"
          class="conversation-item"
          :class="{
            citizen: item.clientId === '1',
            agent: item.clientId === '2',
          }"
        >
          <div
            class="chat-content"
            :class="{ 'agent-content': item.clientId === '2' }"
          >
            <div class="avatar" v-if="item.clientId === '1'">
              <img src="../assets/image/avatar.png" alt="" />
            </div>
            <div class="message">
              <div
                class="role-name"
                :class="{ 'text-right': item.clientId === '2' }"
              >
                <span class="role-name-text">{{
                  item.clientId === "1" ? "市民" : info.agentName
                }}</span>
                <span>{{ item.chatTime }}</span>
              </div>
              <NineSquareGrid :side="item.clientId === '1' ? 'left' : 'right'">
                <div
                  class="text-content"
                  :class="{ 'text-right': item.clientId === '2' }"
                >
                  {{ item.txt }}
                </div>
              </NineSquareGrid>
              <!-- 
              <div
                class="time-info"
                :class="{ 'text-right': item.clientId === 2 }"
              >
                {{ item.start }}s - {{ item.end }}s
              </div> -->
            </div>
            <div class="avatar" v-if="item.clientId === '2'">
              <img :src="info.url" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useFormatTime } from "../hooks/useFormatTime";
import * as echarts from "echarts";
import {
  onMounted,
  ref,
  defineProps,
  watch,
  defineEmits,
  onBeforeUnmount,
  computed,
  defineExpose,
} from "vue";
import NineSquareGrid from "./NineSquareGrid.vue";
import { getAgentCallInfo } from "../api";
import { ElMessage, ElMessageBox } from "element-plus";
import { useCCBar } from "../composables/useCCBar";
// import PentagonChart from './PentagonChart.vue';

const { ccbarService } = useCCBar();
const props = defineProps({
  info: {
    type: Object,
    required: true,
  },
});

const workloadEcharts = ref(null);
const lastMonthScoreEcharts = ref(null);
const currentTab = ref("day"); // 默认显示日数据
const showCallInfo = ref(false); // 添加控制通话信息显示的变量
const CallInfoData = ref({
  code: "0",
  msg: "",
  callStatus: "0",
  playLeftTime: 0,
  callInfo: {
    callId: "",
    agentPhone: "",
    agentNo: "",
    txtList: [],
    startTime: "",
    callTime: "",
    recordPath: "",
  },
});
const timerInterval = ref(null); // 用于存储定时器ID
const isRequesting = ref(false); // 标记是否正在请求中
const imboxRef = ref(null); // 添加imbox的引用

const ccbarState = ref('BUSY');

// 功能状态管理
const whisperActive = ref(false); //密语
const interceptActive = ref(false); //强插
const monitorActive = ref(false); //监听

// 判断座席是否在通话中
const isInCall = computed(() => {
  const state = props.info?.currentState;
  console.log(props.info.currentState, "判断是否在通话中");
  console.log(props.info, "props.infoprops.infoprops.infoprops.info");
  if (!state) return false;
  // 通话状态：["2", "3", "4", "5"]
  return ["2", "3", "4", "5"].includes(state);
});

// 判断是否有任何功能处于激活状态
const isAnyFunctionActive = computed(() => {
  return monitorActive.value || interceptActive.value || whisperActive.value;
});

// 监听通话状态变化，通话结束时重置所有功能状态
watch(isInCall, (newValue) => {
  if (!newValue) {
    whisperActive.value = false;
    interceptActive.value = false;
    monitorActive.value = false;
  }
});

// 定义一个函数来获取最新的通话信息
const fetchLatestCallInfo = () => {
  // 如果已经在请求中，则不重复请求
  if (isRequesting.value) return;

  isRequesting.value = true;
  getAgentCallInfo({ agentPhone: props.info.agentPhone })
    .then((res) => {
      if (res.data.code === "0") {
        // 处理通话数据
        const callData = res.data;
        // 保存当前消息数量用于比较
        const currentMessageCount =
          CallInfoData.value.callInfo?.txtList?.length || 0;
        CallInfoData.value = {
          ...callData,
          callInfo: {
            ...callData.callInfo,
            // 格式化转写记录
            txtList: callData.callInfo.txtList.map((item) => ({
              ...item,
              // 转换角色标识
              role: item.clientId === "1" ? "市民" : "坐席",
              // 格式化时间
              chatTime: item.chatTime || item.start?.split(" ")[1] || "",
              // 添加开始和结束时间
              startTime: item.start,
              endTime: item.end,
            })),
          },
        };
        // 检查是否有新消息
        const newMessageCount =
          CallInfoData.value.callInfo?.txtList?.length || 0;
        if (newMessageCount > currentMessageCount) {
          // 有新消息，在下一个DOM更新循环中滚动到底部
          setTimeout(() => {
            if (imboxRef.value) {
              imboxRef.value.scrollTop = imboxRef.value.scrollHeight;
            }
          }, 0);
        }
      } else {
        console.error(res.data.msg || "获取通话记录失败");
      }
    })
    .catch((error) => {
      console.error("获取通话记录失败:", error);
    })
    .finally(() => {
      // 请求完成后，重置标记
      isRequesting.value = false;
    });
};

// 监听showCallInfo的变化
watch(showCallInfo, (newVal) => {
  if (newVal) {
    // 如果showCallInfo为true，立即获取一次数据
    fetchLatestCallInfo();
    // 然后开始定时获取通话信息
    timerInterval.value = setInterval(() => {
      // 每次定时器触发时，只有在上一次请求完成的情况下才发起新请求
      if (!isRequesting.value) {
        fetchLatestCallInfo();
      }
    }, 1000);

    // 首次显示时也滚动到底部（在DOM更新后）
    setTimeout(() => {
      if (imboxRef.value) {
        imboxRef.value.scrollTop = imboxRef.value.scrollHeight;
      }
    }, 100);
  } else {
    // 如果showCallInfo为false，清除定时器
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
    }
  }
});

// 监听消息列表变化
watch(
  () => CallInfoData.value.callInfo?.txtList,
  (newVal, oldVal) => {
    if (newVal && oldVal && newVal.length > oldVal.length) {
      // 有新消息时，滚动到底部
      setTimeout(() => {
        if (imboxRef.value) {
          imboxRef.value.scrollTop = imboxRef.value.scrollHeight;
        }
      }, 0);
    }
  },
  { deep: true }
);

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
});

const updateChart = (chart, type) => {
  const data = props.info?.workNumberFive?.[type];
  if (!data) return;

  const option = {
    radar: {
      shape: "polygon",
      splitNumber: 4,
      center: ["50%", "50%"],
      radius: "70%",
      nameGap: 15,
      triggerEvent: true,
      name: {
        formatter: (text, indicator) => {
          const name = text.split("\n").join("");
          const wrappedName = name.replace(/(.{8})/g, "$1\n");
          return [
            "{name|" + wrappedName + "}",
            "{value|" + indicator.realValue + "}",
          ].join(" ");
        },
        rich: {
          name: {
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: 14,
            width: 84,
            lineHeight: 18,
            padding: [0, 0, 0, 0],
          },
          value: {
            color: "#00FFFF",
            fontSize: 14,
            padding: [0, 0, 0, 4],
            fontFamily: "zcoolqingkehuangyouti",
          },
        },
      },
      axisLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["rgba(0, 255, 255, 0.02)", "rgba(0, 255, 255, 0.05)"],
        },
      },
      indicator: [
        {
          name: "接线量",
          max: 100,
          text: data.callInCountLeven,
          realValue: data.callInCount,
        },
        {
          name: "工单量",
          max: 100,
          text: data.orderCountLeven,
          realValue: data.orderCount,
        },
        {
          name: "签入时长",
          max: 100,
          text: data.loginTimeLeven,
          realValue: useFormatTime(data.loginTime).formattedTime.value,
        },
        {
          name: "平均通话时长",
          max: 100,
          text: data.avgCallInTimeLeven,
          realValue: useFormatTime(data.avgCallInTime).formattedTime.value,
        },
        {
          name: "平均话后处理时长",
          max: 100,
          text: data.avgArrangeTimeLeven,
          realValue: useFormatTime(data.avgArrangeTime).formattedTime.value,
        },
      ],
    },
    series: [
      {
        type: "radar",
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          color: "#00FFFF",
          width: 2,
        },
        itemStyle: {
          color: "#00FFFF",
        },
        areaStyle: {
          color: "rgba(0, 255, 255, 0.3)",
        },
        data: [
          {
            value: [
              data.callInCountLeven,
              data.orderCountLeven,
              data.loginTimeLeven,
              data.avgCallInTimeLeven,
              data.avgArrangeTimeLeven,
            ],
          },
        ],
      },
    ],
  };
  chart.setOption(option);
};

// 更新上月综合评分图表
const updateLastMonthScoreChart = (chart) => {
  const data = props.info?.lastMonthScoreFive;
  if (!data) return;

  const option = {
    radar: {
      shape: "polygon",
      splitNumber: 4,
      center: ["50%", "60%"],
      radius: "70%",
      nameGap: 15,
      triggerEvent: true,
      name: {
        formatter: (text, indicator) => {
          const name = text.split("\n").join("");
          const wrappedName = name.replace(/(.{4})/g, "$1\n");
          return [
            "{name|" + wrappedName + "}",
            "{value|" + indicator.realValue + "}",
          ].join(" ");
        },
        rich: {
          name: {
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: 14,
            width: 84,
            lineHeight: 14,
            padding: [0, 0, 0, 0],
          },
          value: {
            color: "#00FFFF",
            fontSize: 14,
            padding: [0, 0, 0, 4],
            fontFamily: "zcoolqingkehuangyouti",
          },
        },
      },
      axisLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["rgba(0, 255, 255, 0.02)", "rgba(0, 255, 255, 0.05)"],
        },
      },
      indicator: [
        {
          name: "质检得分",
          max: 5,
          text: data.qualityScoreLeven,
          realValue: data.qualityScore,
        },
        {
          name: "月考得分",
          max: 5,
          text: data.monthlyExamScoreLeven,
          realValue: data.monthlyExamScore,
        },
        {
          name: "业务量得分",
          max: 5,
          text: data.busiNumberScoreLeven,
          realValue: data.busiNumberScore,
        },
        {
          name: "话后处理得分",
          max: 5,
          text: data.afterLongScoreLeven,
          realValue: data.afterLongScore,
        },
        {
          name: "出勤得分",
          max: 5,
          text: data.attendanceScoreLeven,
          realValue: data.attendanceScore,
        },
      ],
    },
    series: [
      {
        type: "radar",
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          color: "#00FFFF",
          width: 2,
        },
        itemStyle: {
          color: "#00FFFF",
        },
        areaStyle: {
          color: "rgba(0, 255, 255, 0.3)",
        },
        data: [
          {
            value: [
              data.qualityScoreLeven,
              data.monthlyExamScoreLeven,
              data.busiNumberScoreLeven,
              data.afterLongScoreLeven,
              data.attendanceScoreLeven,
            ],
          },
        ],
      },
    ],
  };
  chart.setOption(option);
};

// 切换tab的处理函数
const handleTabClick = (type) => {
  currentTab.value = type;
  const chart = echarts.init(workloadEcharts.value);
  updateChart(chart, type);
};

// 添加点击事件处理函数
const handleShowCallInfo = () => {
  getAgentCallInfo({
    agentPhone: props.info.agentPhone,
  })
    .then((res) => {
      console.log("通话记录数据:", res);
      if (res.data.code === "0") {
        // 修改为字符串比较
        // 处理通话数据
        const callData = res.data;
        CallInfoData.value = {
          ...callData,
          callInfo: {
            ...callData.callInfo,
            // 格式化转写记录
            txtList: callData.callInfo.txtList.map((item) => ({
              ...item,
              // 转换角色标识
              role: item.clientId === "1" ? "市民" : "坐席",
              // 格式化时间
              chatTime: item.chatTime || item.start?.split(" ")[1] || "",
              // 添加开始和结束时间
              startTime: item.start,
              endTime: item.end,
            })),
          },
        };

        // 根据通话状态判断是否可以立即播放

        showCallInfo.value = true;
      } else {
        ElMessage.error(res.data.msg || "获取通话记录失败");
      }
    })
    .catch((error) => {
      console.error("获取通话记录失败:", error);
      ElMessage.error("获取通话记录失败");
    });
};

onMounted(() => {
  ccbarState.value = localStorage.getItem('ccbarState')
      console.log(localStorage.getItem('ccbarState'),'当前状态，个人画像');
  // 初始化工作量图表
  const workloadChart = echarts.init(workloadEcharts.value);
  updateChart(workloadChart, currentTab.value);

  // 初始化上月综合评分图表
  const lastMonthScoreChart = echarts.init(lastMonthScoreEcharts.value);
  updateLastMonthScoreChart(lastMonthScoreChart);

  // 添加窗口大小改变时的自适应
  window.addEventListener("resize", () => {
    workloadChart.resize();
    lastMonthScoreChart.resize();
  });
});

// 监听info的变化
watch(
  () => props.info,
  (newVal) => {
    if (newVal?.workNumberFive) {
      const workloadChart = echarts.init(workloadEcharts.value);
      updateChart(workloadChart, currentTab.value);
    }
    if (newVal?.lastMonthScoreFive) {
      const lastMonthScoreChart = echarts.init(lastMonthScoreEcharts.value);
      updateLastMonthScoreChart(lastMonthScoreChart);
    }
  },
  { deep: true }
);

// 获取区域位置名称
const getRoomLocation = (code) => {
  const locations = {
    8: "六里桥4楼B区",
    9: "六里桥4楼A区",
    10: "六里桥4楼C区",
    11: "六里桥5楼C区",
    12: "六里桥5楼A区",
  };
  return locations[code] || code;
};
console.log(ccbarService.getAgentState(), "当前状态");
// 添加状态映射函数
const getAgentState = (state) => {
  if (!state) return "";

  // 通话状态
  if (["2", "3", "4", "5"].includes(state)) {
    return "通话";
  }
  // 空闲状态
  if (state === "1") {
    return "空闲";
  }
  // 话后状态
  if (["6", "10"].includes(state)) {
    return "话后";
  }
  // 离席状态
  if (["7", "8", "9"].includes(state)) {
    return "离席";
  }
  return state;
};

const emit = defineEmits(["getCallData", "close"]);

// 处理告警点击事件
const handleAlarmClick = (type, name, agentId) => {
  emit("getCallData", {
    type,
    name,
    agentId,
    workId: "",
    avatar: props.info.url,
  });
};

// 处理强制签出
const handleForceSignOut = async () => {
  try {
    if (!props.info?.agentId) {
      ElMessage.error("座席ID不存在");
      return;
    }
    const result = await ccbarService.supervisorManager.forceLogout(
      props.info.agentId,
      "主管强制签出"
    );

    if (result.state) {
      ElMessage.success("强制签出成功");
    } else {
      ElMessage.error(result.msg || "强制签出失败");
    }
  } catch (error) {
    console.error("强制签出异常:", error);
    ElMessage.error("强制签出异常");
  }
};

// 强制置闲处理函数
const handleForceIdle = async () => {
  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行强制置闲");
    return;
  }

  try {
    const result = await ccbarService.supervisorManager.forceIdle(
      props.info.agentId
    );

    if (result.state) {
      ElMessage.success("强制置闲执行成功");
    } else {
      ElMessage.error(`强制置闲失败: ${result.msg || "未知错误"}`);
    }
  } catch (error) {
    console.error("强制置闲异常:", error);
    ElMessage.error(`强制置闲失败: ${error.message || "网络异常"}`);
  }
};

// 强制置忙处理函数（带确认和忙碌类型选择）
const handleForceBusy = async () => {
  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行强制置忙");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要强制置忙座席 ${props.info.agentName || props.info.agentId} 吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    // 可以根据需要设置不同的忙碌类型
    // 1: 休息, 2: 会议, 3: 培训, 0: 其他
    const busyType = "1"; // 默认为休息

    const result = await ccbarService.supervisorManager.forceBusy(
      props.info.agentId,
      "主管强制置忙",
      { busyType }
    );

    if (result.state) {
      // ElMessage.success('强制置忙执行成功');
    } else {
      ElMessage.error(`强制置忙失败: ${result.msg || "未知错误"}`);
    }
  } catch (error) {
    if (error === "cancel") {
      return; // 用户取消操作
    }
    console.error("强制置忙异常:", error);
    ElMessage.error(`强制置忙失败: ${error.message || "网络异常"}`);
  }
};

// 监听功能 - 支持开始/结束切换
const handleMonitor = async () => {
  if (!isInCall.value) {
    ElMessage.warning("只有在通话状态下才能使用监听功能");
    return;
  }

  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行监听");
    return;
  }

  try {
    let result;
    if (monitorActive.value) {
      // 结束监听
      result = await ccbarService.supervisorManager.stopMonitor(
        props.info.agentId
      );

      if (result.state) {
        monitorActive.value = false;
        ElMessage.success("监听已结束");
      } else {
        ElMessage.error(`结束监听失败: ${result.msg || "未知错误"}`);
      }
    } else {
      // 开始监听
      result = await ccbarService.supervisorManager.startMonitor(
        props.info.agentId,
        null,
        { monitorType: "silent" }
      );

      if (result.state) {
        monitorActive.value = true;
        ElMessage.success("开始监听");
      } else {
        ElMessage.error(`监听失败: ${result.msg || "未知错误"}`);
      }
    }
  } catch (error) {
    console.error("监听操作异常:", error);
    ElMessage.error(`监听操作失败: ${error.message || "网络异常"}`);
  }
};

// 强插功能 - 支持开始/结束切换
const handleIntercept = async () => {
  if (!isInCall.value) {
    ElMessage.warning("只有在通话状态下才能使用强插功能");
    return;
  }

  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行强插");
    return;
  }

  try {
    let result;
    if (interceptActive.value) {
      // 结束强插
      result = await ccbarService.supervisorManager.stopIntercept(
        props.info.agentId
      );

      if (result.state) {
        interceptActive.value = false;
        ElMessage.success("强插已结束");
      } else {
        ElMessage.error(`结束强插失败: ${result.msg || "未知错误"}`);
      }
    } else {
      // 开始强插
      await ElMessageBox.confirm(
        `确定要强插座席 ${
          props.info.agentName || props.info.agentId
        } 的通话吗？`,
        "确认操作",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );

      result = await ccbarService.supervisorManager.startIntercept(
        props.info.agentId,
        null,
        { interceptType: "barge" }
      );

      if (result.state) {
        interceptActive.value = true;
        ElMessage.success("强插成功");
      } else {
        ElMessage.error(`强插失败: ${result.msg || "未知错误"}`);
      }
    }
  } catch (error) {
    if (error === "cancel") return;
    console.error("强插操作异常:", error);
    ElMessage.error(`强插操作失败: ${error.message || "网络异常"}`);
  }
};

// 挂断拦截功能
const handleBargeIn = async () => {
  // let result;
  if (!isInCall.value) {
    ElMessage.warning("只有在通话状态下才能使用拦截功能");
    return;
  }

  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行拦截");
    return;
  }
  try {
    await ElMessageBox.confirm(
      `确定要拦截座席 ${props.info.agentName || props.info.agentId} 的通话吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    const result = await ccbarService.supervisorManager.interceptCall(
      props.info.agentId
    );
    if (result.state) {
      monitorActive.value = false;
      interceptActive.value = false;
      whisperActive.value = false;
      ElMessage.success("拦截成功");
      setTimeout(() => {
        emit("close");
      }, 500);
    } else {
      ElMessage.error(`拦截失败: ${result.msg || "未知错误"}`);
    }
    // }
  } catch (error) {
    if (error === "cancel") return;
    console.error("拦截异常:", error);
    ElMessage.error(`拦截失败: ${error.message || "网络异常"}`);
  }
};

// 强拆功能
const handleForceDisconnect = async () => {
  if (!isInCall.value) {
    ElMessage.warning("只有在通话状态下才能使用强拆功能");
    return;
  }

  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行强拆");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要强制挂断座席 ${
        props.info.agentName || props.info.agentId
      } 的通话吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }
    );

    const result = await ccbarService.supervisorManager.ClearForceCall(
      props.info.agentId
    );

    if (result.state) {
      ElMessage.success("强拆成功");
    } else {
      ElMessage.error(`强拆失败: ${result.msg || "未知错误"}`);
    }
  } catch (error) {
    if (error === "cancel") return;
    console.error("强拆异常:", error);
    ElMessage.error(`强拆失败: ${error.message || "网络异常"}`);
  }
};

// 密语功能 - 支持开始/结束切换
const handleWhisper = async () => {
  if (!isInCall.value) {
    ElMessage.warning("只有在通话状态下才能使用密语功能");
    return;
  }

  if (!props.info?.agentId) {
    ElMessage.warning("座席ID不存在，无法执行密语");
    return;
  }

  try {
    let result;
    if (whisperActive.value) {
      // 结束密语
      result = await ccbarService.supervisorManager.stopWhisper(
        props.info.agentId
      );

      if (result.state) {
        whisperActive.value = false;
        ElMessage.success("密语已结束");
      } else {
        ElMessage.error(`结束密语失败: ${result.msg || "未知错误"}`);
      }
    } else {
      // 开始密语
      result = await ccbarService.supervisorManager.startWhisper(
        props.info.agentId
      );

      if (result.state) {
        whisperActive.value = true;
        ElMessage.success("开始密语");
      } else {
        ElMessage.error(`密语失败: ${result.msg || "未知错误"}`);
      }
    }
  } catch (error) {
    console.error("密语操作异常:", error);
    ElMessage.error(`密语操作失败: ${error.message || "网络异常"}`);
  }
};

defineExpose({
  monitorActive,
  interceptActive,
  whisperActive,
});
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  .personal-info {
    width: 652px;
    height: calc(100vh - 160px);
    overflow-y: auto;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    .agent-basic {
      display: flex;
      margin-bottom: 14px;
      padding-top: 16px;
      border-radius: 8px;
      box-sizing: border-box;

      .agent-avatar {
        width: 64px;
        height: 64px;
        margin-right: 16px;
        border-radius: 50%;
        overflow: hidden;
        background: #00ffff;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .agent-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .nameBox {
          gap: 0px !important;
        }
        .info-row {
          display: flex;
          align-items: center;
          gap: 24px;
          &:first-child {
            .name {
              font-size: 20px;
              font-weight: bold;
              margin-right: 16px;
            }
          }
          .honorBox {
            display: flex;
            gap: 16px;
            align-items: center;
            .honorItem {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              letter-spacing: normal;
              color: #ffffff;
              background-size: 100% 100%;
              padding: 1px 9px 1px 12px;
              position: relative;

              img {
                width: 28px;
                height: 28px;
                position: absolute;
                left: -14px;
                top: -2px;
              }
            }
            .king {
              background-image: url("../assets/image/king-bg.png");
            }
            .silver {
              background-image: url("../assets/image/silver-bg.png");
            }
            .copper {
              background-image: url("../assets/image/copper-bg.png");
            }
          }
          .label {
            flex: none;
            width: 100px;
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            text-align: center;
            letter-spacing: normal;
            color: #ffffff;
          }

          .value {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 24px;
            letter-spacing: normal;
            color: #00ffff;
          }
        }
      }
    }

    .info-title {
      margin-bottom: 16px;
      background: url("../assets/image/labelBg.png") no-repeat left bottom;
      background-size: 100% 20px;
      padding-left: 24px;
      box-sizing: border-box;
      .title-text {
        font-family: Alibaba PuHuiTi 3;
        font-size: 18px;
        font-weight: normal;
        line-height: 28px;
      }
    }

    .basic-info {
      margin-bottom: 14px;

      .info-content {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        border-radius: 3.2px;
        background: rgba(0, 128, 255, 0.1);
        padding: 16px;
        box-sizing: border-box;
      }

      .info-item {
        display: flex;
        align-items: start;
        .label {
          /* 文字/正文 */
          /* 样式描述：正文文本/表单文本/常规组件文本/次级按钮文本 */
          font-family: Alibaba PuHuiTi 3;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          display: flex;
          align-items: center;
          letter-spacing: normal;
          color: rgba(255, 255, 255, 0.8);
        }
        .w100 {
          flex: none;
          width: 100px;
        }
        .w70 {
          flex: none;
          width: 70px;
        }

        .value {
          font-family: Alibaba PuHuiTi 3;
          font-size: 14px;
          font-weight: normal;
          line-height: 24px;
          letter-spacing: normal;
          color: #00ffff;
        }
      }
    }

    .activity-info {
      margin-bottom: 14px;

      .info-content {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
      }

      .activity-item {
        display: flex;
        align-items: center;
        padding: 16px;
        height: 82px;
        background: rgba(0, 255, 255, 0.1);

        border-radius: 4px;
        flex: 1;
        box-sizing: border-box;

        .activity-icon {
          width: 48px;
          height: 48px;
          margin-right: 16px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .activity-details {
          .activity-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }

          .activity-time {
            font-size: 20px;
            color: #00ffff;
            font-family: "zcoolqingkehuangyouti";
          }
        }
      }
      .currentTime {
        background-color: rgba(0, 220, 170, 0.2);
        background-image: url("../assets/image/currentTime.png");
        background-repeat: no-repeat;
        background-size: 82px 82px;
        background-position: right center;

        &-info {
          display: flex;
          flex-direction: column;

          &-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }
          &-status {
            /* 文字/特一级标题 */
            /* 样式描述：用于顶级菜单选中，及数据卡片项数值 */
            font-family: Alibaba PuHuiTi 3;
            font-size: 18px;
            font-weight: bold;
            line-height: 28px;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
          }
        }
      }
      .currentStatus {
        background-color: rgba(0, 170, 255, 0.2);
        background-image: url("../assets/image/currentStatus.png");
        background-repeat: no-repeat;
        background-size: 82px 82px;
        background-position: right center;

        &-info {
          display: flex;
          flex-direction: column;

          &-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }
          &-status {
            /* 文字/特一级标题 */
            /* 样式描述：用于顶级菜单选中，及数据卡片项数值 */
            font-family: Alibaba PuHuiTi 3;
            font-size: 18px;
            font-weight: bold;
            line-height: 28px;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
            .dot {
              width: 8px;
              height: 8px;
              background-color: #00ffff;
              border-radius: 50%;
              margin-right: 8px;
            }
            img {
              width: 92px;
              height: 32px;
              margin-left: 24px;
              &:hover {
                cursor: pointer;
                opacity: 0.8;
              }
            }
          }
        }
      }
      .left-top-title {
        border-radius: 4px 0px 16px 0px;
        padding: 8px 16px;
        background: rgba(0, 255, 255, 0.1);
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        position: absolute;
        top: 0;
        left: 0;
      }
      .workload-day {
        // width: 652px;
        height: 166px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        margin-bottom: 16px;
        position: relative;
        overflow: hidden;
        padding: 46px 16px 16px;
        box-sizing: border-box;

        &-content {
          width: 100%;
          height: 100%;
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          grid-template-rows: repeat(2, 1fr);
          gap: 16px;
          &-item {
            &-title {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              display: flex;
              align-items: center;
              letter-spacing: normal;
              color: rgba(255, 255, 255, 0.6);
            }
            &-value {
              font-family: zcoolqingkehuangyouti;
              font-size: 16px;
              font-weight: normal;
              line-height: 24px;
              text-align: right;
              display: flex;
              align-items: center;
              letter-spacing: normal;
              /* 主色/卓越青 */
              color: #00ffff;
            }
          }
        }
      }
      .workload-info {
        width: 100%;
        height: 328px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        position: relative;
        overflow: hidden;
        padding: 58px 16px 16px;
        box-sizing: border-box;
        margin-bottom: 16px;
        &-content {
          width: 100%;
          height: 100%;
          .workload-echarts {
            width: 100%;
            height: 100%;
          }
        }

        .tabBox {
          padding: 4px 8px;
          display: flex;
          position: absolute;
          height: 32px;
          top: 16px;
          right: 16px;
          border-radius: 4px;
          background: rgba(0, 128, 255, 0.2);
          .tabItem {
            border-radius: 4px;
            /* 颜色/正文渐变 */
            padding: 1px 8px;
            display: flex;
            align-items: center;
            cursor: pointer;
            &-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
          .active {
            background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
            .tabItem-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: #0055ff;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
      }
      .last-month-score {
        width: 100%;
        height: 400px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        position: relative;
        overflow: hidden;
        padding: 28px 16px 16px;
        box-sizing: border-box;
        margin-bottom: 16px;
        &-content {
          width: 100%;
          height: 100%;
          .last-month-score-echarts {
            width: 620px;
            height: 254px;
            margin: 0 auto 22px;
          }
        }
        .last-month-score-text-content {
          display: flex;
          .last-month-score-text {
            width: 359px;
            height: 78px;
            border-radius: 4px;
            background: linear-gradient(
              90deg,
              rgba(0, 128, 255, 0.2) 0%,
              rgba(0, 128, 255, 0) 100%
            );
            display: flex;
            padding: 16px;
            box-sizing: border-box;
            &-item {
              display: flex;
              flex: 1;
              &-title {
                font-family: Alibaba PuHuiTi 3;
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                color: #ffffff;
                margin-right: 8px;
              }
              &-value {
                font-family: zcoolqingkehuangyouti;
                font-size: 32px;
                font-weight: normal;
                line-height: 48px;
                text-align: right;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                /* 主色/卓越青 */
                color: #00ffff;
              }
            }
            &-item1 {
              flex: 1;
              .last-month-score-text-item-title {
                font-family: Alibaba PuHuiTi 3;
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                color: #ffffff;
                margin-right: 8px;
              }
              .last-month-score-text-item-value {
                font-family: zcoolqingkehuangyouti;
                font-size: 16px;
                font-weight: normal;
                line-height: 24px;
                text-align: right;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                /* 主色/卓越青 */
                color: #00ffff;
              }
            }
          }
          .last-month-score-text1 {
            width: 121px;
            height: 78px;
            border-radius: 4px;
            background: linear-gradient(
              90deg,
              rgba(0, 128, 255, 0.2) 0%,
              rgba(0, 128, 255, 0) 100%
            );
            padding: 16px;
            box-sizing: border-box;
          }
        }

        .tabBox {
          padding: 4px 8px;
          display: flex;
          position: absolute;
          height: 32px;
          top: 16px;
          right: 16px;
          border-radius: 4px;
          background: rgba(0, 128, 255, 0.2);
          .tabItem {
            border-radius: 4px;
            /* 颜色/正文渐变 */
            padding: 1px 8px;
            display: flex;
            align-items: center;
            cursor: pointer;
            &-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
          .active {
            background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
            .tabItem-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: #0055ff;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
      }
    }

    .workload,
    .ranking-analysis {
      margin-bottom: 24px;

      .chart-container {
        height: 200px;
        position: relative;

        .pentagon-chart {
          width: 100%;
          height: 100%;
        }

        .chart-labels {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          justify-content: space-around;
          align-items: center;
          flex-wrap: wrap;

          .chart-label {
            font-size: 14px;
            color: #ffffff;
          }
        }
      }
    }

    .total-score {
      display: flex;
      justify-content: space-between;
      gap: 16px;

      .score-item {
        flex: 1;
        background: rgba(0, 255, 255, 0.1);
        border-radius: 4px;
        padding: 12px;
        text-align: center;

        .score-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          margin-bottom: 4px;
        }

        .score-value {
          font-size: 20px;
          color: #00ffff;
          font-family: "zcoolqingkehuangyouti";
        }
      }
    }

    .workload {
      .info-content {
        // padding: 16px;
        border-radius: 4px;

        &-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          grid-template-rows: repeat(2, 1fr);
          gap: 16px;
        }

        &-item {
          padding: 16px;
          background: rgba(0, 128, 255, 0.1);
          border-radius: 3.2px;
          cursor: pointer;
          .item-title {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            img {
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
          }

          .item-value {
            font-family: zcoolqingkehuangyouti;
            font-size: 16px;
            line-height: 24px;
            color: #00ffff;
          }
        }
      }
    }
    .workHeight {
      height: 72px;
    }
    .workload-bar {
      display: flex;
      padding: 24px 0px;
      box-sizing: border-box;
      position: absolute;
      bottom: -28px;
      width: 652px;
      justify-content: space-between;
      background: rgb(12, 33, 75);
      border-top: 1px solid rgba(0, 255, 255, 0.1);
      left: 0;
      &-item {
        display: flex;

        font-family: Alibaba PuHuiTi 3;
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        align-items: center;

        border-radius: 4px;
        background: rgba(0, 255, 255, 0.1);
        padding: 8px 14px;
        box-sizing: border-box;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }
  }
  .imbox-line {
    width: 1px;
    background: rgba(0, 255, 255, 0.2);
    margin-left: 24px;
  }
  .imbox {
    width: 508px;
    height: calc(100vh - 160px);
    background: rgba(12, 33, 75, 0.9);
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;

    .call-info-header {
      padding: 16px;
      background: rgba(0, 128, 255, 0.1);
      border-radius: 4px;
      margin-bottom: 16px;

      .call-basic-info {
        .info-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: rgba(255, 255, 255, 0.6);
            width: 80px;
          }

          .value {
            color: #00ffff;
            flex: 1;
          }
        }
      }
    }

    .conversation-list {
      .conversation-item {
        margin-bottom: 24px;

        .chat-content {
          display: flex;
          gap: 12px;

          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(
              180deg,
              #00ffff 0%,
              rgba(0, 255, 255, 0.2) 100%
            );
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .message {
            .role-name {
              color: rgba(255, 255, 255, 0.6);
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 4px;
              &.text-right {
                flex-direction: row-reverse;
              }
            }

            .text-content {
              color: #ffffff;
              font-size: 16px;
            }
          }
        }

        .agent-content {
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>

<style scoped>
.workload-bar-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.workload-bar-item.disabled img {
  filter: grayscale(100%);
}

.workload-bar-item:not(.disabled):hover {
  background-color: rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

.workload-bar-item.active {
  background-color: rgba(0, 255, 255, 0.3) !important;
  border: 1px solid #00ffff;
}
</style>
