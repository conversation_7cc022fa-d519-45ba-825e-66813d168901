<template>
  <div class="audioContent" ref="audioContent">
    <div class="btnContent" ref="btnContent">
      <div class="playBtn" @click="togglePlay">
        <div class="iconPlay" v-show="!isPlaying"></div>
        <div class="iconPause" v-show="isPlaying"></div>
      </div>
    </div>
    <canvas class="canvasObj" ref="canvas" @click="handleCanvasClick"></canvas>
    <audio
      ref="audio"
      :src="audioUrl"
      @timeupdate="updateTime"
      @ended="onAudioEnd"
    ></audio>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  defineProps,
  watch,
  nextTick,
  computed,
} from "vue";

const props = defineProps({
  audioSrc: {
    type: String,
    default: "",
  },
});

// 计算完整的音频URL
const audioUrl = computed(() => {
  if (!props.audioSrc) return "";
  const baseURL = window.location.protocol + "//" + window.location.host;
  return `${baseURL}/aiamgr/record/play.do?recordePath=${props.audioSrc}`;
});

const audioContent = ref(null);
const btnContent = ref(null);
const canvas = ref(null);
const audio = ref(null);
const audioContext = ref(null);
const analyser = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const waveformData = ref([]);
const canvasHeight = ref(72);
// /aiamgr/record/play.do
const initAudio = async () => {
  try {
    audioContext.value = new (window.AudioContext ||
      window.webkitAudioContext)();
    analyser.value = audioContext.value.createAnalyser();
    analyser.value.fftSize = 2048;

    // 创建媒体元素源
    if (audio.value) {
      const source = audioContext.value.createMediaElementSource(audio.value);
      source.connect(analyser.value);
      analyser.value.connect(audioContext.value.destination);
    }

    // 确保在初始化后立即设置 canvas 尺寸
    nextTick(() => {
      handleResize();
    });
  } catch (error) {
    console.error("初始化音频上下文失败:", error);
  }
};

// 修改 loadAudio 方法来使用 audio 元素
const loadAudio = async () => {
  try {
    if (!audioContext.value) {
      await initAudio();
    }

    // 等待音频加载完成
    await new Promise((resolve) => {
      audio.value.addEventListener('loadedmetadata', () => {
        duration.value = audio.value.duration;
        resolve();
      }, { once: true });
    });

    // 生成波形数据
    generateWaveformData();

    // 确保在DOM更新后重新调整大小
    nextTick(() => {
      handleResize();
    });
  } catch (error) {
    console.error("加载音频失败:", error);
  }
};

const generateWaveformData = () => {
  if (!analyser.value) {
    console.warn("分析器未就绪");
    return;
  }

  try {
    console.log("开始生成波形数据");
    const bufferLength = analyser.value.frequencyBinCount;
    const dataArray = new Float32Array(bufferLength);
    
    // 获取当前的音频数据
    analyser.value.getFloatTimeDomainData(dataArray);

    // 增加采样点数量以获得更细腻的波形
    const numberOfBars = Math.floor(canvas.value.width / 5); // 每5像素一个波形条
    const samplesPerBar = Math.floor(bufferLength / numberOfBars);
    const newWaveformData = [];

    for (let i = 0; i < numberOfBars; i++) {
      const start = i * samplesPerBar;
      const end = Math.min(start + samplesPerBar, bufferLength);
      let min = 1.0;
      let max = -1.0;

      for (let j = start; j < end; j++) {
        const value = dataArray[j];
        min = Math.min(min, value);
        max = Math.max(max, value);
      }

      newWaveformData.push({ min, max });
    }

    waveformData.value = newWaveformData;
    console.log("波形数据生成完成，数据点数:", waveformData.value.length);

    // 绘制波形
    drawWaveform();
  } catch (error) {
    console.error("生成波形数据失败:", error);
  }
};

const drawWaveform = () => {
  if (!canvas.value || !waveformData.value.length) {
    console.warn("无法绘制波形：canvas未就绪或没有波形数据");
    return;
  }

  console.log("开始绘制波形");
  const ctx = canvas.value.getContext("2d");
  if (!ctx) {
    console.error("无法获取canvas上下文");
    return;
  }

  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);

  const margin = {
    left: 10,
    right: 10,
  };
  const width = canvas.value.width - margin.left - margin.right;
  const height = canvas.value.height;
  const centerY = height / 2;

  // 计算进度位置
  const progressX = Math.max(
    margin.left,
    Math.min(
      canvas.value.width - margin.right,
      margin.left + (currentTime.value / duration.value) * width
    )
  );

  // 设置波形参数
  const barWidth = 3; // 每个波形条的宽度
  const barGap = 2; // 波形条之间的间隔
  const barSpacing = barWidth + barGap; // 总间距
  const maxBarHeight = Math.floor(height * 0.6); // 最大波形高度为容器高度的60%
  const cornerRadius = barWidth / 2; // 圆角半径

  // 找出所有波形数据中的最大振幅，用于归一化
  const maxAmplitude = Math.max(
    ...waveformData.value.map((point) => Math.abs(point.max - point.min))
  );

  // 绘制波形
  waveformData.value.forEach((point, i) => {
    const x = margin.left + i * barSpacing;

    // 计算波形高度（归一化后乘以最大高度）
    const amplitude = Math.abs(point.max - point.min);
    const normalizedHeight = (amplitude / maxAmplitude) * maxBarHeight;
    const barHeight = Math.max(4, normalizedHeight); // 最小高度设为4px以确保圆角效果

    // 确定是否在播放进度之前
    const isPlayed = x <= progressX;

    // 设置渐变色
    if (isPlayed) {
      // 已播放部分（亮蓝色）
      ctx.fillStyle = "#00feff";
    } else {
      // 未播放部分（深蓝色）
      ctx.fillStyle = "#003790";
    }

    // 绘制圆角波形条
    const y = centerY - barHeight / 2;

    // 开始绘制圆角矩形
    ctx.beginPath();
    ctx.moveTo(x + cornerRadius, y);
    ctx.lineTo(x + barWidth - cornerRadius, y);
    ctx.arc(
      x + barWidth - cornerRadius,
      y + cornerRadius,
      cornerRadius,
      -Math.PI / 2,
      0
    );
    ctx.lineTo(x + barWidth, y + barHeight - cornerRadius);
    ctx.arc(
      x + barWidth - cornerRadius,
      y + barHeight - cornerRadius,
      cornerRadius,
      0,
      Math.PI / 2
    );
    ctx.lineTo(x + cornerRadius, y + barHeight);
    ctx.arc(
      x + cornerRadius,
      y + barHeight - cornerRadius,
      cornerRadius,
      Math.PI / 2,
      Math.PI
    );
    ctx.lineTo(x, y + cornerRadius);
    ctx.arc(
      x + cornerRadius,
      y + cornerRadius,
      cornerRadius,
      Math.PI,
      -Math.PI / 2
    );
    ctx.closePath();
    ctx.fill();
  });

  // 绘制进度指示器
  // 粉色竖线
  const lineGradient = ctx.createLinearGradient(0, 0, 0, height);
  lineGradient.addColorStop(0, "#ff89b7");
  lineGradient.addColorStop(0.5, "#ee4586");
  lineGradient.addColorStop(1, "#dd0559");
  ctx.strokeStyle = lineGradient;
  ctx.lineWidth = 2;

  // 绘制从顶部到圆形指示器顶部的线
  const markerY = height - 8;
  ctx.beginPath();
  ctx.moveTo(progressX, 0);
  ctx.lineTo(progressX, markerY - 8); // 停在圆形指示器上方
  ctx.stroke();

  // 保存当前上下文状态
  ctx.save();

  // 创建圆形裁剪区域以确保渐变只在圆内显示
  ctx.beginPath();
  ctx.arc(progressX, markerY, 8, 0, Math.PI * 2);
  ctx.clip();

  // 绘制粉色大圆背景
  const bgColor1 = ctx.createLinearGradient(
    progressX - 8,
    markerY - 8,
    progressX + 8,
    markerY + 8
  );
  bgColor1.addColorStop(0, "#d70156");
  bgColor1.addColorStop(0.5, "#aa105e");
  bgColor1.addColorStop(1, "#c60358");
  ctx.fillStyle = bgColor1;
  ctx.fill();

  // 恢复上下文状态
  ctx.restore();

  // 绘制白色小圆
  ctx.beginPath();
  ctx.arc(progressX, markerY, 3, 0, Math.PI * 2);
  ctx.fillStyle = "#ffffff";
  ctx.fill();

  console.log("波形绘制完成");
};

const togglePlay = () => {
  if (isPlaying.value) {
    pauseAudio();
  } else {
    playAudio();
  }
};

// 添加实时波形更新
const updateWaveform = () => {
  if (isPlaying.value) {
    generateWaveformData();
    requestAnimationFrame(updateWaveform);
  }
};

// 修改播放控制函数
const playAudio = async (time) => {
  try {
    isPlaying.value = true;
    audio.value.currentTime = typeof time !== "undefined" ? time : currentTime.value;
    await audio.value.play();
    // 开始实时更新波形
    updateWaveform();
  } catch (e) {
    console.error("播放失败:", e);
    isPlaying.value = false;
  }
};

const pauseAudio = () => {
  audio.value.pause();
  isPlaying.value = false;
};

const updateTime = () => {
  currentTime.value = audio.value.currentTime;
  drawWaveform();
};

const onAudioEnd = () => {
  isPlaying.value = false;
  currentTime.value = 0;
  drawWaveform();
};

const handleCanvasClick = (event) => {
  const rect = canvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const percent = x / canvas.value.width;
  currentTime.value = percent * duration.value;
  if (isPlaying.value) {
    pauseAudio();
    playAudio();
  }
  drawWaveform();
};

const handleResize = () => {
  if (!canvas.value || !audioContent.value || !btnContent.value) {
    console.warn("handleResize: DOM 元素未就绪");
    return;
  }

  try {
    const canvasWidth =
      audioContent.value.clientWidth - btnContent.value.clientWidth - 16;
    canvas.value.width = Math.max(canvasWidth, 0);
    canvas.value.height = canvasHeight.value;

    // 只有在有波形数据时才重新绘制
    if (waveformData.value && waveformData.value.length > 0) {
      drawWaveform();
    }
  } catch (error) {
    console.error("调整 canvas 尺寸失败:", error);
  }
};

// const formatTime = (seconds) => {
//   const minutes = Math.floor(seconds / 60);
//   seconds = Math.floor(seconds % 60);
//   return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
// };

// 修改 watch 来使用新的 loadAudio 方法
watch(
  () => props.audioSrc,
  async (newSrc) => {
    if (newSrc) {
      try {
        await loadAudio();
      } catch (error) {
        console.error("加载音频失败:", error);
      }
    }
  }
);

onMounted(async () => {
  console.log("组件挂载，初始化音频");
  await initAudio();

  // 确保在组件完全挂载后再加载音频
  nextTick(async () => {
    if (props.audioSrc) {
      try {
        await loadAudio();
      } catch (error) {
        console.error("初始化加载音频失败:", error);
      }
    }
    window.addEventListener("resize", handleResize);
  });
});

onBeforeUnmount(() => {
  if (audioContext.value) {
    audioContext.value.close();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.audioContent {
  display: flex;
  width: 100%;
  min-height: 72px;
  position: relative;

  .btnContent {
    flex-shrink: 0;
    align-items: center;
    .playBtn {
      width: 40px;
      height: 40px;
      border-radius: 40px;
      background: linear-gradient(134deg, #00dcaa -1%, #00aaff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 14px 8px 0 0px;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
      .iconPlay,
      .iconPause {
        width: 20px;
        height: 20px;
      }
      .iconPlay {
        background: url("../assets/image/icon_play.svg") no-repeat center center;
        background-size: 100% 100%;
      }
      .iconPause {
        background: url("../assets/image/icon_pause.svg") no-repeat center
          center;
        background-size: 100% 100%;
      }
    }
  }

  .canvasObj {
    flex: 1;
    cursor: pointer;
    height: 72px;
    display: block;
    min-width: 100px;
  }
}
</style>
