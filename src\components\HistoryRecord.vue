<template>
  <div class="content-box">
    <div class="history-record-info">
      <div class="history-record-tab-box">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          class="history-record-tab-item"
          :class="{ active: props.currentType === item.type }"
          @click="handleTabClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <div class="history-record-list" v-else-if="callData?.length">
        <div
          v-for="(item, index) in callData"
          :key="index"
          class="history-record-item"
          :class="{ active: currentCallId == item.CALL_ID }"
          @click="handleItemClick(item)"
        >
          <div class="item-left"></div>
          <div class="item-right">
            <div class="phone-number">
              <span class="fontStyle" :class="{ number: !props.isWorkGroup }">{{
                props.isWorkGroup
                  ? item.AGENT_NAME
                  : usePhoneMask(item.CALL_PHONE)
              }}</span>
              <div
                class="alarm-type"
                :class="getAlarmTypeClass(item.ALARM_TYPE)"
              >
                {{ item.ALARM_TYPE }}
              </div>
            </div>
            <div class="call-info" v-if="props.isWorkGroup">
              <div class="time">
                <span class="label">工号：</span>
                <span class="value number">{{ item.AGENT_ID || "--" }}</span>
              </div>
              <div class="time">
                <span class="label">来电号码：</span>
                <span class="value number">{{
                  usePhoneMask(item.CALL_PHONE)
                }}</span>
              </div>
            </div>
            <div class="call-info">
              <div class="time">
                <span class="label">通话开始时间：</span>
                <span class="value number">{{ item.CALL_BEGIN }}</span>
              </div>
              <div class="time">
                <span class="label">通话结束时间：</span>
                <span class="value number">{{ item.CALL_END }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <img src="../assets/image/empty.png" alt="" class="empty-icon" />
        <div class="empty-text">暂无数据</div>
      </div>
      <!-- 添加分页器 -->
      <div class="pagination">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @update:current-page="(val) => (currentPage = val)"
          @update:page-size="(val) => (pageSize = val)"
        />
      </div>
    </div>

    <div class="imbox-line" v-if="showCallInfo"></div>

    <div class="imbox" v-if="showCallInfo">
      <div class="conversation-list">
        <div
          v-for="(item, index) in callInfo"
          :key="index"
          class="conversation-item"
          :class="{ citizen: item.clientId == 1, agent: item.clientId == 2 }"
        >
          <div
            class="chat-content"
            :class="{ 'agent-content': item.clientId == 2 }"
          >
            <div class="avatar" v-if="item.clientId == 1">
              <img src="../assets/image/avatar.png" alt="" />
            </div>
            <div class="message">
              <div
                class="role-name"
                :class="{ 'text-right': item.clientId == 2 }"
              >
                <span class="role-name-text">{{
                  item.clientId == 1 ? "市民" : currentItem.AGENT_NAME
                }}</span>
                <span>{{ item.timestamp }}</span>
              </div>
              <NineSquareGrid :side="item.clientId == 1 ? 'left' : 'right'">
                <div
                  class="text-content"
                  :class="{ 'text-right': item.clientId == 2 }"
                  v-html="checkUpText(item)"
                ></div>
              </NineSquareGrid>
              <!-- <div
                  class="time-info"
                  :class="{ 'text-right': item.clientId === 2 }"
                >
                  {{ item.start }}s - {{ item.end }}s
                </div> -->

              <div class="alarm-type-box">
                <div
                  class="tag-item"
                  v-if="item?.speedFlag == 1"
                  style="color: #ffd322"
                >
                  <span>语速过快</span>
                </div>
                <div
                  class="tag-item"
                  v-if="item?.foreStallFlag == 0"
                  style="color: #ffd322"
                >
                  <span>抢话</span>
                </div>
                <div
                  class="tag-item"
                  v-if="item?.outlineWord"
                  style="color: #ffd322"
                >
                  <span>坐席违规词</span>
                </div>
                <div
                  class="tag-item"
                  v-if="item?.sensWord"
                  style="color: #ffd322"
                >
                  <span>市民敏感词</span>
                </div>
                <div
                  class="tag-item"
                  v-if="item?.muteFlag == 1"
                  style="color: #ffd322"
                >
                  <span>静音</span>
                </div>
                <div
                  class="tag-item"
                  v-if="item?.feelingFlag == 1"
                  style="color: #ffd322"
                >
                  <span>情绪异常</span>
                </div>
              </div>
            </div>
            <div class="avatar" v-if="item.clientId == 2">
              <img :src="avatar" alt="" />
            </div>
          </div>
        </div>
      </div>
      <div v-if="currentItem.RECORD_PATH">
        <AudioPlayer :audioSrc="currentItem.RECORD_PATH"></AudioPlayer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, nextTick, computed } from "vue";
import NineSquareGrid from "./NineSquareGrid.vue";
// import PentagonChart from './PentagonChart.vue';
import AudioPlayer from "./AudioPlayer.vue";
import { ElPagination } from "element-plus";

// 手机号码脱敏hook
import { usePhoneMask } from "../hooks/useFormatTime";

const emit = defineEmits(["update:currentType", "reload"]);
const tabList = ref([
  { type: 1, label: "超长通话" },
  { type: 3, label: "静默" },
  { type: 4, label: "语速过快" },
  { type: 5, label: "抢话" },
  { type: 6, label: "坐席违规词" },
  { type: 7, label: "市民敏感词" },
]);

const props = defineProps({
  callData: {
    type: Array,
    default: () => [
      {
        ALARM_TYPE: "超长通话", //告警类型
        AGENT_NAME: "曹烁", //告警坐席名称
        CALL_BEGIN: "2025-06-16 10:51:55", //通话开始时间
        CALL_END: "2025-06-16 10:53:13", //通话结束时间
        CALL_PHONE: "13611308667", //坐席手机号
        CALL_CONTENT: "", //转写内容
        CALL_ID: "", //通话记录id
        RECORD_PATH: "录音地址",
      },
    ],
  },
  currentType: {
    type: Number,
    default: 1,
  },
  agentName: {
    type: String,
    default: "",
  },
  agentId: {
    type: String,
    default: "",
  },
  workId: {
    type: String,
    default: "",
  },
  isWorkGroup: {
    type: Boolean,
    default: false,
  },
  avatar: {
    type: String,
    default: "",
  },
  total: {
    type: Number,
    default: 0,
  },
});

let showCallInfo = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false); // 添加loading状态

const handleTabClick = (type) => {
  currentPage.value = 1;
  emit("update:currentType", type);
  loading.value = true; // 设置loading状态
  emit("getCallData", {
    type,
    name: props.agentName,
    agentId: props.agentId,
    workId: props.workId,
    pageIndex: currentPage.value,
    pageSize: pageSize.value,
  });
};
let currentCallId = ref("");
const callInfo = ref();
const currentItem = ref({});

// 分页相关
const total = computed(() => props.total || 0);

// 处理分页大小变化
const handleSizeChange = (size) => {
  console.log("页面大小变更为:", size);
  pageSize.value = size;
  currentPage.value = 1;
  loading.value = true; // 设置loading状态

  // 确保使用最新的pageSize值
  const params = {
    type: props.currentType,
    name: props.agentName,
    agentId: props.agentId,
    workId: props.workId,
    pageIndex: currentPage.value,
    pageSize: size, // 直接使用传入的size参数而不是pageSize.value
  };

  console.log("发送分页请求参数:", params);
  emit("getCallData", params);
};

const checkUpText = (item) => {
  console.log(item);
  if (item?.outlineWord && item?.outlineWord != "") {
    if (item?.outlineWord.indexOf(",") != -1) {
      let parts = item?.outlineWord.split(",").map((part) => {
        return part.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // 转义正则表达式中的特殊字符
      });
      let regex = new RegExp(`(${parts.join(",")})`, "gi");
      return item.txt.replace(regex, `<span style="color:red">$1</span>`);
    } else {
      let regex = new RegExp(item?.outlineWord, "gi");
      return item.txt.replace(
        regex,
        `<span style="color:red">${item?.outlineWord}</span>`
      );
    }
  } else if (item?.sensWord && item?.sensWord != "") {
    if (item?.sensWord.indexOf(",") != -1) {
      let parts = item?.sensWord.split(",").map((part) => {
        return part.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // 转义正则表达式中的特殊字符
      });
      let regex = new RegExp(`(${parts.join(",")})`, "gi");
      return item.txt.replace(regex, `<span style="color:red">$1</span>`);
    } else {
      let regex = new RegExp(item?.sensWord, "gi");
      return item.txt.replace(
        regex,
        `<span style="color:red">${item?.sensWord}</span>`
      );
    }
  } else {
    return item.txt;
  }
};

// 处理当前页变化
const handleCurrentChange = (page) => {
  console.log("页码变更为:", page);
  currentPage.value = page;
  loading.value = true; // 设置loading状态

  // 确保使用最新的pageSize值
  const params = {
    type: props.currentType,
    name: props.agentName,
    agentId: props.agentId,
    workId: props.workId,
    pageIndex: page, // 直接使用传入的page参数而不是currentPage.value
    pageSize: pageSize.value,
  };

  console.log("发送分页请求参数:", params);
  emit("getCallData", params);
};

// 监听callData变化，重置loading状态
watch(
  () => props.callData,
  () => {
    // 数据加载完成，取消loading状态
    loading.value = false;
    console.log("callData变化，取消loading状态");
  }
);

// 监听callInfo变化，滚动到底部
watch(
  callInfo,
  async () => {
    if (showCallInfo.value) {
      await nextTick();
      const conversationList = document.querySelector(".conversation-list");
      if (conversationList) {
        conversationList.scrollTop = conversationList.scrollHeight;
      }
    }
  },
  { deep: true }
);

// 获取告警类型对应的样式类名
const getAlarmTypeClass = (type) => {
  const classMap = {
    超长通话: "type-long",
    静默: "type-silent",
    语速过快: "type-fast",
    抢话: "type-interrupt",
    坐席违规词: "type-violation",
    市民敏感词: "type-sensitive",
  };
  return classMap[type] || "";
};

const handleItemClick = (item) => {
  // 处理点击事件，可以播放录音或显示详情等
  console.log("点击了通话记录:", item);
  currentItem.value = item;
  currentCallId.value = item.CALL_ID;

  try {
    if (item.CALL_CONTENT) {
      console.log("解析CALL_CONTENT:", item.CALL_CONTENT);
      callInfo.value = JSON.parse(item.CALL_CONTENT);
      console.log("解析后的callInfo:", callInfo.value);
    } else {
      console.log("CALL_CONTENT为空，使用默认值");
      callInfo.value = [];
    }
  } catch (error) {
    console.error("解析CALL_CONTENT失败:", error);
    callInfo.value = [];
  }

  showCallInfo.value = true;
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  height: 100%;
  .history-record-info {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    display: flex;
    flex-direction: column;

    .history-record-tab-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      .history-record-tab-item {
        padding: 5px 16px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.2);
        cursor: pointer;
        display: flex;
        align-items: center;

        &.active {
          border-radius: 4px;
          background: rgba(255, 220, 0, 0.2);
          box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72);
          span {
            background: #ffdc00;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
          }
        }

        span {
          font-family: Alibaba PuHuiTi 3;
          font-size: 16px;
          font-weight: normal;
          text-align: center;
          letter-spacing: normal;
          background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
      }
    }

    .history-record-list {
      margin-top: 16px;
      flex: 1;
      overflow-y: auto;

      .history-record-item {
        background: rgba(0, 128, 255, 0.1);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;

        &:hover {
          background: rgba(0, 128, 255, 0.2);
        }

        .item-left {
          margin-right: 16px;
          background: url("../assets/image/phone.png") no-repeat center center;
          background-size: 100% 100%;
          width: 64px;
          height: 64px;
        }

        .item-right {
          flex: 1;

          .phone-number {
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 8px;
            display: flex;
            gap: 12px;
            align-items: center;
            span {
              font-size: 24px;
            }
          }

          .call-info {
            display: flex;
            gap: 24px;

            .time {
              .label {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
              }
              .value {
                font-size: 14px;
                font-weight: normal;
                text-align: center;
                letter-spacing: normal;
                color: #ffffff;
              }
            }
          }
        }

        .alarm-type {
          padding: 4px 12px;
          border-radius: 4px;
          font-size: 14px;

          &.type-long {
            background: rgba(220, 0, 85, 0.1);
            color: #dc0055;
          }

          &.type-silent {
            background: rgba(0, 150, 136, 0.1);
            color: #009688;
          }

          &.type-fast {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
          }

          &.type-interrupt {
            background: rgba(156, 39, 176, 0.1);
            color: #9c27b0;
          }

          &.type-violation {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
          }

          &.type-sensitive {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
          }
        }
      }
      .active {
        background: rgba(255, 220, 0, 0.2) !important;
        box-sizing: border-box;
        /* 辅助色/湘叶 */
        border: 1px solid #ffdc00 !important;
        box-shadow: inset 0px 0px 16px 0px rgba(255, 220, 0, 0.72) !important;
        position: relative;
        &:before {
          content: "";
          position: absolute;
          top: 40%;
          right: -8px;
          border-bottom: 8px solid #ffdc00;
          border-right: 8px solid #ffdc00;
          border-top: 8px solid transparent;
          border-left: 8px solid transparent;
          transform: rotate(-45deg);
        }

        .item-left {
          margin-right: 16px;
          background: url("../assets/image/active-phone.png") no-repeat center
            center;
          background-size: 100% 100%;
          width: 64px;
          height: 64px;
        }
      }
    }

    .empty-state {
      margin-top: 20vh;
      text-align: center;
      flex: 1;
      .empty-icon {
        width: 308px;
        height: 252px;
      }

      .empty-text {
        font-size: 14px;
        color: #fff;
      }
    }
  }
  .imbox-line {
    width: 1px;
    background: rgba(0, 255, 255, 0.2);
    margin-left: 24px;
  }
  .imbox {
    width: 508px;
    height: 100%;
    background: rgba(12, 33, 75, 0.9);
    padding: 16px 16px 0;
    box-sizing: border-box;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .call-info-header {
      padding: 16px;
      background: rgba(0, 128, 255, 0.1);
      border-radius: 4px;
      margin-bottom: 16px;

      .call-basic-info {
        .info-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: rgba(255, 255, 255, 0.6);
            width: 80px;
          }

          .value {
            color: #00ffff;
            flex: 1;
          }
        }
      }
    }

    .conversation-list {
      flex: 1;
      overflow-y: auto;
      .conversation-item {
        margin-bottom: 24px;

        .chat-content {
          display: flex;
          gap: 12px;

          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(
              180deg,
              #00ffff 0%,
              rgba(0, 255, 255, 0.2) 100%
            );
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .message {
            .role-name {
              color: rgba(255, 255, 255, 0.6);
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 4px;
              &.text-right {
                flex-direction: row-reverse;
              }
            }

            .text-content {
              font-size: 16px;
              color: #ffffff;
            }
            .alarm-type-box {
              .tag-item {
                span {
                  text-wrap: nowrap;
                  display: inline-block;
                  padding: 4px 12px;
                  background: rgba(255, 193, 7, 0.1);
                  border-radius: 6px;
                  margin-top: 10px;
                  margin-right: 8px;
                  float: right;
                }
              }
            }
          }
        }

        .agent-content {
          justify-content: flex-end;
        }
      }
    }
  }
}

/* 添加分页样式 */
.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  :deep(.el-pagination) {
    .el-input__inner {
      color: #fff;
    }
    .is-active {
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 128, 255, 0.3) 2%,
        rgba(0, 128, 255, 0.2) 100%
      ) !important;
      background-color: none !important;
      box-sizing: border-box;
      border: 1px solid #0080ff;
      box-shadow: inset 0px 0px 12px 0px #0080ff;
    }
    .el-pagination__total {
      color: #fff;
    }
    .el-pagination__jump {
      color: #fff;
    }

    .el-select {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      height: 32px;
      color: rgba(0, 255, 255, 1);
    }
    .el-select__selected-item {
      color: rgba(0, 255, 255, 1);
    }

    .el-pager {
      gap: 8px;
      margin-left: 8px;
      margin-right: 8px;
    }
    .number,
    .more {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
    .btn-prev,
    .btn-next {
      border-radius: 4px;
      background: rgba(0, 128, 255, 0.1);
      width: 32px;
      height: 32px;
      color: #fff;
    }
  }
}

/* 添加loading样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ffffff;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
  border: 4px solid rgba(0, 255, 255, 0.2);
  border-top: 4px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #00ffff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
