<template>
  <div class="template-detail">
    <div class="template-detail-content">
      <div class="template-detail-content-item">
        <span class="label">消息名称</span>
        <span class="value">{{ info.MSG_NAME || "--" }}</span>
      </div>
      <div class="template-detail-content-item">
        <span class="label">消息主题</span>
        <span class="value">{{ info.MSG_SUB || "--" }}</span>
      </div>

      <div class="template-detail-content-item">
        <span class="label detail">详细信息</span>
        <span class="value detail">{{ info.MSG_CONTENT || "--" }}</span>
      </div>
      <div class="template-detail-content-item">
        <span class="label">消息类型</span>
        <span class="value">
          <!-- <span class="value-tag">
            {{ messageTypeDict[info.MSG_REMIND_TYPE] || "--" }}
          </span> -->
          <span class="value-tag" >
            {{ getRemindTypeText(info.MSG_REMIND_TYPE) || "--" }}
          </span>
        </span>
      </div>
      <div class="template-detail-content-item">
        <span class="label">消息发布时间</span>
        <span class="value">
          <span>
            {{ info.MSG_TIME || "--" }}
          </span>
        </span>
      </div>
      <div class="template-detail-content-item">
        <span class="label">提交人</span>
        <span class="value">
          <span>
            {{ info.MSG_SEND_NAME || "--" }}
          </span>
        </span>
      </div>
      <div class="template-detail-content-item">
        <span class="label">提交时间</span>
        <span class="value">
          <span>
            {{ info.MSG_TIME || "--" }}
          </span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineOptions } from "vue";
import { computed } from "vue";

defineOptions({
  name: "templateDetail",
});
const props = defineProps({
  info: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
// 获取提醒类型文本
const getRemindTypeText = (type) => {
  const types = {
    0: "话后超时",
    1: "超长通话",
    2: "语速过快",
    3: "求助",
    4: "其它",
    5: "转派",
    6: "公告",
    7: "静默",
    8: "抢话",
    9: "坐席违规词",
    10: "市民敏感词",
    11: "首发诉求",
  };
  return types[type] || "未知";
};
// 消息类型字典
// const messageTypeDict = {
//   1: "公告",
//   2: "话后超时",
//   3: "超长通话",
//   4: "静默",
//   5: "语速过快",
//   6: "抢话",
//   7: "坐席违规词",
//   8: "其他",
// };

// 提取DETAIL中的参数
const detailParams = computed(() => {
  const paramsArray = [];
  if (props.info && props.info.DETAIL) {
    // 使用正则表达式匹配所有${xxx}格式的参数
    const regex = /\{([^}]+)\}/g;
    let match;
    let index = 0;

    // 查找所有匹配项
    while ((match = regex.exec(props.info.DETAIL)) !== null) {
      // 创建参数对象并添加到数组中
      const paramName = match[1];
      const paramObj = {};
      paramObj[`PARAMS_${index}`] = paramName;
      paramsArray.push(paramObj);
      index++;
    }
  }
  return paramsArray;
});

// 提取DETAIL中方括号[]包围的内容
const bracketsParams = computed(() => {
  const bracketsArray = [];
  if (props.info && props.info.DETAIL) {
    // 使用正则表达式匹配所有[xxx]格式的内容
    const regex = /\[([^\]]+)\]/g;
    let match;
    let index = 0;

    // 查找所有匹配项
    while ((match = regex.exec(props.info.DETAIL)) !== null) {
      // 创建参数对象并添加到数组中
      const content = match[1];
      const paramObj = {};
      paramObj[`SYSTEM_${index}`] = content;
      bracketsArray.push(paramObj);
      index++;
    }
  }
  return bracketsArray;
});

// 可以在控制台查看提取的参数
console.log("提取的参数:", detailParams.value);
// 可以在控制台查看提取的方括号内容
console.log("提取的方括号内容:", bracketsParams.value);
</script>

<style lang="scss" scoped>
.template-type-box-title {
  display: flex;
  align-items: center;
  margin-left: -6px;
  margin-bottom: 10px;
  .fontStyle {
    font-size: 18px;
    font-weight: bold;
  }
  img {
    width: 36px;
    height: 36px;
  }
}
.template-detail {
  margin-top: 16px;
  width: 652px;

  &-header {
    width: 652px;
    border-radius: 4px;
    background: rgba(0, 128, 255, 0.1);
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    &-left {
      width: 64px;
      height: 64px;
      flex: none;
    }
    &-right {
      display: flex;
      flex-direction: column;
      gap: 8px;
      &-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      &-time {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        .time {
          .label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
          }
          .value {
            font-size: 14px;
            font-weight: normal;
            text-align: center;
            letter-spacing: normal;
            color: #ffffff;
          }
        }
      }
    }
    .assistant {
      background: url("../../assets/image/zsicon.png") no-repeat center center;
      background-size: 100% 100%;
    }
    .sms {
      background: url("../../assets/image/dxicon.png") no-repeat center center;
      background-size: 100% 100%;
    }
    .call {
      background: url("../../assets/image/callicon.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }

  &-content {
    width: 652px;
    height: 100%;
    border-radius: 4px;

    border-bottom: 1px solid rgba(0, 128, 255, 0.2);
    box-sizing: border-box;
    &-item {
      display: flex;
      align-items: center;
      .label {
        width: 160px;
        height: 50px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        padding-left: 16px;
        line-height: 50px;
        background: rgba(0, 128, 255, 0.1);
        box-sizing: border-box;
        border-width: 1px 0px 0px 1px;
        border-style: solid;
        border-color: rgba(0, 128, 255, 0.2);
        color: #ffffff;
        font-size: 14px;
      }
      .value {
        font-size: 14px;
        color: #ffffff;
        border-width: 1px 1px 0px 1px;
        border-style: solid;
        border-color: rgba(0, 128, 255, 0.2);
        height: 50px;
        line-height: 50px;
        flex: 1;
        padding-left: 16px;
        .value-tag {
          padding: 4px 8px;
          border-radius: 4px;
          background: rgba(0, 220, 85, 0.1);
          font-size: 12px;
          color: rgba(0, 220, 85, 1);
        }
      }

      .detail {
        height: 100px;
        padding-left: 16px;
        line-height: 22px;
        padding-top: 8px;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
      }
    }
  }
}

.alarm-type {
  width: 100px;
  height: 26px;
}
.type-sms {
  background: url("../../assets/image/dxtztag.png") no-repeat center center;
  background-size: 100% 100%;
}
.type-call {
  background: url("../../assets/image/whtztag.png") no-repeat center center;
  background-size: 100% 100%;
}
.type-assistant {
  background: url("../../assets/image/zsxxtag.png") no-repeat center center;
  background-size: 100% 100%;
}
</style>
