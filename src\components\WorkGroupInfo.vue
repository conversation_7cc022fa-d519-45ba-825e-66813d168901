<template>
  <div class="content-box">
    <div class="personal-info">
      <!-- 完成区域 开始 -->
      <!-- 个人基本信息 -->
      <div class="agent-basic">
        <div class="agent-avatar">
          <img src="../assets/image/group-avatar.png" alt="" />
        </div>
        <div class="agent-info">
          <div class="info-row nameBox">
            <span class="name fontStyle">{{ info.workGroupName }}</span>
            <div class="honorBox">
              <div
                class="honorItem king"
                v-if="info?.workGroupScoreInfo?.HONOR1"
              >
                <img src="../assets/image/king-icon.png" alt="" />
                {{ info?.workGroupScoreInfo?.HONOR1 }}
              </div>
              <div
                class="honorItem silver"
                v-if="info?.workGroupScoreInfo?.HONOR2"
              >
                <img src="../assets/image/silver-icon.png" alt="" />
                {{ info?.workGroupScoreInfo?.HONOR2 }}
              </div>
              <div
                class="honorItem copper"
                v-if="info?.workGroupScoreInfo?.HONOR3"
              >
                <img src="../assets/image/copper-icon.png" alt="" />
                {{ info?.workGroupScoreInfo?.HONOR3 }}
              </div>
            </div>
          </div>
          <div class="info-row">
            <div>
              <span class="label">人数： </span>
              <span class="value">{{ info.workGroupCount || "--" }}</span>
            </div>
            <div>
              <span class="label">班长：</span>
              <span class="value">{{ info.workGroupMonitors || "--" }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 班组人数信息区域 -->
      <div class="basic-info">
        <div class="info-title">
          <div class="title-text fontStyle">班组人数信息</div>
        </div>
        <div class="info-number-content">
          <div
            v-for="(item, index) in contentCards"
            :key="index"
            class="content-top-item"
            :style="{
              backgroundImage: `${
                'url(' + require('@/assets/image/right/' + item.bg) + ')'
              }`,
            }"
          >
            <div>
              <img :src="require(`@/assets/image/right/${item.icon}`)" alt="" />
            </div>
            <div>
              <div
                class="content-top-item-title"
                :style="{
                  color: '#fff',
                }"
              >
                {{ item.title }}
              </div>
              <div
                class="content-top-item-content"
                :style="{
                  color: '#fff',
                }"
              >
                {{ info[item.value] || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 班组话务信息 -->
      <div class="activity-info">
        <div class="info-title">
          <div class="title-text fontStyle">班组话务信息</div>
        </div>

        <div class="workload-day">
          <div class="left-top-title">当天话务</div>
          <div class="workload-day-content">
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听量</div>
              <div class="workload-day-content-item-value">
                {{ info?.toDayCall?.CALL_IN_COUNT_ALL || 0 }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均通话时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.AVG_CALL_IN_TIME).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均话后时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.AVG_ARRANGE_TIME).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">签入总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.LOGIN_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听总时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toDayCall?.CALL_IN_TIME_ALL).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">话后总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.ARRANGE_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">离席总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toDayCall?.BUSY_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">挂机满意度</div>
              <div class="workload-day-content-item-value">
                {{ info?.toDayCall?.GOOD_PERCENT || 0 }}%
              </div>
            </div>
          </div>
        </div>
        <div class="workload-day">
          <div class="left-top-title">当月话务</div>
          <div class="workload-day-content">
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听量</div>
              <div class="workload-day-content-item-value">
                {{ info?.toMonthCall?.CALL_IN_COUNT_ALL || 0 }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均通话时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toMonthCall?.AVG_CALL_IN_TIME)
                    .formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">平均话后时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toMonthCall?.AVG_ARRANGE_TIME)
                    .formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">签入总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toMonthCall?.LOGIN_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">接听总时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toMonthCall?.CALL_IN_TIME_ALL)
                    .formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">话后总时长</div>
              <div class="workload-day-content-item-value">
                {{
                  useFormatTime(info?.toMonthCall?.ARRANGE_TIME).formattedTime
                }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">离席总时长</div>
              <div class="workload-day-content-item-value">
                {{ useFormatTime(info?.toMonthCall?.BUSY_TIME).formattedTime }}
              </div>
            </div>
            <div class="workload-day-content-item">
              <div class="workload-day-content-item-title">挂机满意度</div>
              <div class="workload-day-content-item-value">
                {{ info?.toMonthCall?.GOOD_PERCENT || 0 }}%
              </div>
            </div>
          </div>
        </div>
        <div class="workload-info">
          <div class="left-top-title">工作量</div>
          <div class="tabBox">
            <div
              class="tabItem"
              :class="{ active: currentTab === 'day' }"
              @click="handleTabClick('day')"
            >
              <span class="tabItem-text">日</span>
            </div>
            <div
              class="tabItem"
              :class="{ active: currentTab === 'week' }"
              @click="handleTabClick('week')"
            >
              <span class="tabItem-text">周</span>
            </div>
            <div
              class="tabItem"
              :class="{ active: currentTab === 'month' }"
              @click="handleTabClick('month')"
            >
              <span class="tabItem-text">月</span>
            </div>
          </div>
          <div class="workload-info-content">
            <div class="workload-echarts" ref="workloadEcharts"></div>
          </div>
        </div>
        <div class="last-month-score">
          <div class="left-top-title">上月综合评分</div>
          <div class="last-month-score-content">
            <div
              class="last-month-score-echarts"
              ref="lastMonthScoreEcharts"
            ></div>
            <div class="last-month-score-text-content">
              <div class="last-month-score-text">
                <div class="last-month-score-text-item">
                  <div class="last-month-score-text-item-title">总分</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.workGroupScoreInfo?.ALL_SCORE || 0 }}
                  </div>
                </div>
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">扣减</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.workGroupScoreInfo?.DEDUCTION || 0 }}
                  </div>
                </div>
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">奖励</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.workGroupScoreInfo?.REWARD || 0 }}
                  </div>
                </div>
              </div>
              <div class="last-month-score-text1">
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">月度排名</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.workGroupScoreInfo?.MONTHLY_RANKING || 0 }}
                  </div>
                </div>
              </div>
              <div class="last-month-score-text1">
                <div class="last-month-score-text-item1">
                  <div class="last-month-score-text-item-title">挂机满意度</div>
                  <div class="last-month-score-text-item-value">
                    {{ info?.workGroupScoreInfo?.ON_HOOK_SATISFACTION || 0 }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人告警信息 -->
      <div class="workload">
        <div class="info-title">
          <div class="title-text fontStyle">班组告警信息</div>
        </div>
        <div class="info-content">
          <div class="info-content-grid">
            <div
              class="info-content-item"
              @click="handleAlarmClick(1, '超长通话', info?.workGroupId)"
            >
              <div class="item-title">
                <span>超长通话</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.CALL_LONG_COUNT || 0 }}
              </div>
            </div>
            <div class="info-content-item">
              <div class="item-title">
                <span>话后超时</span>
                <!-- <img src="../assets/image/arrow-icon.png" alt="" /> -->
              </div>
              <div class="item-value">
                {{ info?.alarms?.PHONE_AFTEL_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(3, '静默', info?.workGroupId)"
            >
              <div class="item-title">
                <span>静默</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.VOICE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(4, '语速过快', info?.workGroupId || '')"
            >
              <div class="item-title">
                <span>语速过快</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.SPEECH_FAST_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(5, '抢话', info?.workGroupId || '')"
            >
              <div class="item-title">
                <span>抢话</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.ROB_TRAFFICE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="
                handleAlarmClick(6, '坐席违规词', info?.workGroupId || '')
              "
            >
              <div class="item-title">
                <span>坐席违规词</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.DISABLE_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="
                handleAlarmClick(7, '市民敏感词', info?.workGroupId || '')
              "
            >
              <div class="item-title">
                <span>市民敏感词</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.SENS_COUNT || 0 }}
              </div>
            </div>
            <div
              class="info-content-item"
              @click="handleAlarmClick(8, '求助', info?.workGroupId || '')"
            >
              <div class="item-title">
                <span>求助</span>
                <img src="../assets/image/arrow-icon.png" alt="" />
              </div>
              <div class="item-value">
                {{ info?.alarms?.HELP_COUNT || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 完成区域 结束 -->
      <div class="workHeight" v-if="false"></div>
    </div>
  </div>
</template>

<script setup>
import { useFormatTime } from "../hooks/useFormatTime";
import * as echarts from "echarts";
import { onMounted, ref, defineProps, watch, defineEmits } from "vue";
// import PentagonChart from './PentagonChart.vue';

const props = defineProps({
  info: {
    type: Object,
    required: true,
  },
});

const workloadEcharts = ref(null);
const lastMonthScoreEcharts = ref(null);
const currentTab = ref("day"); // 默认显示日数据

const contentCards = ref([
  {
    icon: "sl-icon.png",
    bg: "sl.png",
    title: "受理签入人数",
    value: "loginCount",
    state: 1,
  },
  {
    icon: "th-icon.png",
    bg: "th.png",
    title: "通话",
    value: "callCount",
    state: 2,
  },
  {
    icon: "kx-icon.png",
    bg: "kx.png",
    title: "空闲",
    value: "ideaCount",
    state: 3,
  },
  {
    icon: "hh-icon.png",
    bg: "hh.png",
    title: "话后",
    value: "afterCallCount",
    state: 4,
  },
  {
    icon: "sm-icon.png",
    bg: "sm.png",
    title: "离席",
    value: "busyCount",
    state: 5,
  },
  // { icon: "pd-icon.png", bg: "pd.png", title: "排队", value: 0, state: 6 },
]);

const updateChart = (chart, type) => {
  const data = props.info?.workNumberFive?.[type];
  if (!data) return;

  const option = {
    radar: {
      shape: "polygon",
      splitNumber: 4,
      center: ["50%", "50%"],
      radius: "70%",
      nameGap: 15,
      triggerEvent: true,
      name: {
        formatter: (text, indicator) => {
          const name = text.split("\n").join("");
          const wrappedName = name.replace(/(.{8})/g, "$1\n");
          return [
            "{name|" + wrappedName + "}",
            "{value|" + indicator.realValue + "}",
          ].join(" ");
        },
        rich: {
          name: {
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: 14,
            width: 84,
            lineHeight: 18,
            padding: [0, 0, 0, 0],
          },
          value: {
            color: "#00FFFF",
            fontSize: 14,
            padding: [0, 0, 0, 4],
            fontFamily: "zcoolqingkehuangyouti",
          },
        },
      },
      axisLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["rgba(0, 255, 255, 0.02)", "rgba(0, 255, 255, 0.05)"],
        },
      },
      indicator: [
        {
          name: "接线量",
          max: 100,
          text: data.callInCountLeven,
          realValue: data.callInCount,
        },
        {
          name: "工单量",
          max: 100,
          text: data.orderCountLeven,
          realValue: data.orderCount,
        },
        {
          name: "签入时长",
          max: 100,
          text: data.loginTimeLeven,
          realValue: useFormatTime(data.loginTime).formattedTime.value,
        },
        {
          name: "平均通话时长",
          max: 100,
          text: data.avgCallInTimeLeven,
          realValue: useFormatTime(data.avgCallInTime).formattedTime.value,
        },
        {
          name: "平均话后处理时长",
          max: 100,
          text: data.avgArrangeTimeLeven,
          realValue: useFormatTime(data.avgArrangeTime).formattedTime.value,
        },
      ],
    },
    series: [
      {
        type: "radar",
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          color: "#00FFFF",
          width: 2,
        },
        itemStyle: {
          color: "#00FFFF",
        },
        areaStyle: {
          color: "rgba(0, 255, 255, 0.3)",
        },
        data: [
          {
            value: [
              data.callInCountLeven,
              data.orderCountLeven,
              data.loginTimeLeven,
              data.avgCallInTimeLeven,
              data.avgArrangeTimeLeven,
            ],
          },
        ],
      },
    ],
  };
  chart.setOption(option);
};

// 更新上月综合评分图表
const updateLastMonthScoreChart = (chart) => {
  const data = props.info?.lastMonthScoreFive;
  if (!data) return;

  const option = {
    radar: {
      shape: "polygon",
      splitNumber: 4,
      center: ["50%", "60%"],
      radius: "70%",
      nameGap: 15,
      triggerEvent: true,
      name: {
        formatter: (text, indicator) => {
          const name = text.split("\n").join("");
          const wrappedName = name.replace(/(.{4})/g, "$1\n");
          return [
            "{name|" + wrappedName + "}",
            "{value|" + indicator.realValue + "}",
          ].join(" ");
        },
        rich: {
          name: {
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: 14,
            width: 84,
            lineHeight: 14,
            padding: [0, 0, 0, 0],
          },
          value: {
            color: "#00FFFF",
            fontSize: 14,
            padding: [0, 0, 0, 4],
            fontFamily: "zcoolqingkehuangyouti",
          },
        },
      },
      axisLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(0, 255, 255, 0.2)",
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ["rgba(0, 255, 255, 0.02)", "rgba(0, 255, 255, 0.05)"],
        },
      },
      indicator: [
        {
          name: "质检得分",
          max: 5,
          text: data.qualityScoreLeven,
          realValue: data.qualityScore,
        },
        {
          name: "月考得分",
          max: 5,
          text: data.monthlyExamScoreLeven,
          realValue: data.monthlyExamScore,
        },
        {
          name: "业务量得分",
          max: 5,
          text: data.busiNumberScoreLeven,
          realValue: data.busiNumberScore,
        },
        {
          name: "话后处理得分",
          max: 5,
          text: data.afterLongScoreLeven,
          realValue: data.afterLongScore,
        },
        {
          name: "出勤得分",
          max: 5,
          text: data.attendanceScoreLeven,
          realValue: data.attendanceScore,
        },
      ],
    },
    series: [
      {
        type: "radar",
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          color: "#00FFFF",
          width: 2,
        },
        itemStyle: {
          color: "#00FFFF",
        },
        areaStyle: {
          color: "rgba(0, 255, 255, 0.3)",
        },
        data: [
          {
            value: [
              data.qualityScoreLeven,
              data.monthlyExamScoreLeven,
              data.busiNumberScoreLeven,
              data.afterLongScoreLeven,
              data.attendanceScoreLeven,
            ],
          },
        ],
      },
    ],
  };
  chart.setOption(option);
};

// 切换tab的处理函数
const handleTabClick = (type) => {
  currentTab.value = type;
  const chart = echarts.init(workloadEcharts.value);
  updateChart(chart, type);
};

onMounted(() => {
  // 初始化工作量图表
  const workloadChart = echarts.init(workloadEcharts.value);
  updateChart(workloadChart, currentTab.value);

  // 初始化上月综合评分图表
  const lastMonthScoreChart = echarts.init(lastMonthScoreEcharts.value);
  updateLastMonthScoreChart(lastMonthScoreChart);

  // 添加窗口大小改变时的自适应
  window.addEventListener("resize", () => {
    workloadChart.resize();
    lastMonthScoreChart.resize();
  });
});

// 监听info的变化
watch(
  () => props.info,
  (newVal) => {
    if (newVal?.workNumberFive) {
      const workloadChart = echarts.init(workloadEcharts.value);
      updateChart(workloadChart, currentTab.value);
    }
    if (newVal?.lastMonthScoreFive) {
      const lastMonthScoreChart = echarts.init(lastMonthScoreEcharts.value);
      updateLastMonthScoreChart(lastMonthScoreChart);
    }
  },
  { deep: true }
);

// 获取区域位置名称
// const getRoomLocation = (code) => {
//   const locations = {
//     8: "六里桥4楼B区",
//     9: "六里桥4楼A区",
//     10: "六里桥4楼C区",
//     11: "六里桥5楼C区",
//     12: "六里桥5楼A区",
//   };
//   return locations[code] || code;
// };

const emit = defineEmits(["getCallData"]);

// 处理告警点击事件
const handleAlarmClick = (type, name, workId) => {
  emit("getCallData", { type, name, agentId: "", workId });
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;
  position: relative;
  .personal-info {
    width: 652px;
    box-sizing: border-box;
    border-radius: 8px;
    color: #ffffff;
    .agent-basic {
      display: flex;
      margin-bottom: 14px;
      padding-top: 16px;
      border-radius: 8px;
      box-sizing: border-box;

      .agent-avatar {
        width: 64px;
        height: 64px;
        margin-right: 16px;
        border-radius: 50%;
        overflow: hidden;
        // background: #00ffff;

        img {
          width: 110%;
          height: 110%;
          object-fit: cover;
        }
      }

      .agent-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .nameBox {
          gap: 0px !important;
        }
        .info-row {
          display: flex;
          align-items: center;
          gap: 24px;
          &:first-child {
            .name {
              font-size: 20px;
              font-weight: bold;
              margin-right: 16px;
            }
          }
          .honorBox {
            display: flex;
            gap: 16px;
            align-items: center;
            .honorItem {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              letter-spacing: normal;
              color: #ffffff;
              background-size: 100% 100%;
              padding: 1px 9px 1px 12px;
              position: relative;

              img {
                width: 28px;
                height: 28px;
                position: absolute;
                left: -14px;
                top: -2px;
              }
            }
            .king {
              background-image: url("../assets/image/king-bg.png");
            }
            .silver {
              background-image: url("../assets/image/silver-bg.png");
            }
            .copper {
              background-image: url("../assets/image/copper-bg.png");
            }
          }
          .label {
            flex: none;
            width: 100px;
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            text-align: center;
            letter-spacing: normal;
            color: #ffffff;
          }

          .value {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 24px;
            letter-spacing: normal;
            color: #00ffff;
          }
        }
      }
    }

    .info-title {
      margin-bottom: 16px;
      background: url("../assets/image/labelBg.png") no-repeat left bottom;
      background-size: 100% 20px;
      padding-left: 24px;
      box-sizing: border-box;
      .title-text {
        font-family: Alibaba PuHuiTi 3;
        font-size: 18px;
        font-weight: normal;
        line-height: 28px;
      }
    }

    .basic-info {
      margin-bottom: 14px;

      .info-number-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 16px;

        .content-top-item {
          width: 100%;
          height: 80px;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          padding: 16px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          img {
            width: 48px;
            height: 48px;
            margin-right: 16px;
          }
          .content-top-item-title {
            /* 文字/正文 */
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            text-align: right;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
          }
          .content-top-item-content {
            font-family: zcoolqingkehuangyouti;
            font-size: 24px;
            font-weight: normal;
            line-height: 36px;
            text-align: right;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
          }
        }
      }

      .info-content {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        border-radius: 3.2px;
        background: rgba(0, 128, 255, 0.1);
        padding: 16px;
        box-sizing: border-box;
      }

      .info-item {
        display: flex;
        align-items: start;
        .label {
          /* 文字/正文 */
          /* 样式描述：正文文本/表单文本/常规组件文本/次级按钮文本 */
          font-family: Alibaba PuHuiTi 3;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          display: flex;
          align-items: center;
          letter-spacing: normal;
          color: rgba(255, 255, 255, 0.8);
        }
        .w100 {
          flex: none;
          width: 100px;
        }
        .w70 {
          flex: none;
          width: 70px;
        }

        .value {
          font-family: Alibaba PuHuiTi 3;
          font-size: 14px;
          font-weight: normal;
          line-height: 24px;
          letter-spacing: normal;
          color: #00ffff;
        }
      }
    }

    .activity-info {
      margin-bottom: 14px;

      .info-content {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
      }

      .activity-item {
        display: flex;
        align-items: center;
        padding: 16px;
        height: 82px;
        background: rgba(0, 255, 255, 0.1);

        border-radius: 4px;
        flex: 1;
        box-sizing: border-box;

        .activity-icon {
          width: 48px;
          height: 48px;
          margin-right: 16px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .activity-details {
          .activity-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }

          .activity-time {
            font-size: 20px;
            color: #00ffff;
            font-family: "zcoolqingkehuangyouti";
          }
        }
      }
      .currentTime {
        background-color: rgba(0, 220, 170, 0.2);
        background-image: url("../assets/image/currentTime.png");
        background-repeat: no-repeat;
        background-size: 82px 82px;
        background-position: right center;

        &-info {
          display: flex;
          flex-direction: column;

          &-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }
          &-status {
            /* 文字/特一级标题 */
            /* 样式描述：用于顶级菜单选中，及数据卡片项数值 */
            font-family: Alibaba PuHuiTi 3;
            font-size: 18px;
            font-weight: bold;
            line-height: 28px;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
          }
        }
      }
      .currentStatus {
        background-color: rgba(0, 170, 255, 0.2);
        background-image: url("../assets/image/currentStatus.png");
        background-repeat: no-repeat;
        background-size: 82px 82px;
        background-position: right center;

        &-info {
          display: flex;
          flex-direction: column;

          &-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
          }
          &-status {
            /* 文字/特一级标题 */
            /* 样式描述：用于顶级菜单选中，及数据卡片项数值 */
            font-family: Alibaba PuHuiTi 3;
            font-size: 18px;
            font-weight: bold;
            line-height: 28px;
            display: flex;
            align-items: center;
            letter-spacing: normal;
            /* 主色/白色-文字用色 */
            /* 样式描述：文字主要用色 */
            color: #ffffff;
            .dot {
              width: 8px;
              height: 8px;
              background-color: #00ffff;
              border-radius: 50%;
              margin-right: 8px;
            }
            img {
              width: 92px;
              height: 32px;
              margin-left: 24px;
              &:hover {
                cursor: pointer;
                opacity: 0.8;
              }
            }
          }
        }
      }
      .left-top-title {
        border-radius: 4px 0px 16px 0px;
        padding: 8px 16px;
        background: rgba(0, 255, 255, 0.1);
        font-family: Alibaba PuHuiTi 3;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
        display: flex;
        align-items: center;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        position: absolute;
        top: 0;
        left: 0;
      }
      .workload-day {
        // width: 652px;
        height: 166px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        margin-bottom: 16px;
        position: relative;
        overflow: hidden;
        padding: 46px 16px 16px;
        box-sizing: border-box;

        &-content {
          width: 100%;
          height: 100%;
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          grid-template-rows: repeat(2, 1fr);
          gap: 16px;
          &-item {
            &-title {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 22px;
              display: flex;
              align-items: center;
              letter-spacing: normal;
              color: rgba(255, 255, 255, 0.6);
            }
            &-value {
              font-family: zcoolqingkehuangyouti;
              font-size: 16px;
              font-weight: normal;
              line-height: 24px;
              text-align: right;
              display: flex;
              align-items: center;
              letter-spacing: normal;
              /* 主色/卓越青 */
              color: #00ffff;
            }
          }
        }
      }
      .workload-info {
        width: 100%;
        height: 328px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        position: relative;
        overflow: hidden;
        padding: 58px 16px 16px;
        box-sizing: border-box;
        margin-bottom: 16px;
        &-content {
          width: 100%;
          height: 100%;
          .workload-echarts {
            width: 100%;
            height: 100%;
          }
        }

        .tabBox {
          padding: 4px 8px;
          display: flex;
          position: absolute;
          height: 32px;
          top: 16px;
          right: 16px;
          border-radius: 4px;
          background: rgba(0, 128, 255, 0.2);
          .tabItem {
            border-radius: 4px;
            /* 颜色/正文渐变 */
            padding: 1px 8px;
            display: flex;
            align-items: center;
            cursor: pointer;
            &-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
          .active {
            background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
            .tabItem-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: #0055ff;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
      }
      .last-month-score {
        width: 100%;
        height: 400px;
        border-radius: 4px;
        background: rgba(0, 128, 255, 0.1);
        position: relative;
        overflow: hidden;
        padding: 28px 16px 16px;
        box-sizing: border-box;
        margin-bottom: 16px;
        &-content {
          width: 100%;
          height: 100%;
          .last-month-score-echarts {
            width: 620px;
            height: 254px;
            margin: 0 auto 22px;
          }
        }
        .last-month-score-text-content {
          display: flex;
          .last-month-score-text {
            width: 359px;
            height: 78px;
            border-radius: 4px;
            background: linear-gradient(
              90deg,
              rgba(0, 128, 255, 0.2) 0%,
              rgba(0, 128, 255, 0) 100%
            );
            display: flex;
            padding: 16px;
            box-sizing: border-box;
            &-item {
              display: flex;
              flex: 1;
              &-title {
                font-family: Alibaba PuHuiTi 3;
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                color: #ffffff;
                margin-right: 8px;
              }
              &-value {
                font-family: zcoolqingkehuangyouti;
                font-size: 32px;
                font-weight: normal;
                line-height: 48px;
                text-align: right;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                /* 主色/卓越青 */
                color: #00ffff;
              }
            }
            &-item1 {
              flex: 1;
              .last-month-score-text-item-title {
                font-family: Alibaba PuHuiTi 3;
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                color: #ffffff;
                margin-right: 8px;
              }
              .last-month-score-text-item-value {
                font-family: zcoolqingkehuangyouti;
                font-size: 16px;
                font-weight: normal;
                line-height: 24px;
                text-align: right;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                /* 主色/卓越青 */
                color: #00ffff;
              }
            }
          }
          .last-month-score-text1 {
            width: 121px;
            height: 78px;
            border-radius: 4px;
            background: linear-gradient(
              90deg,
              rgba(0, 128, 255, 0.2) 0%,
              rgba(0, 128, 255, 0) 100%
            );
            padding: 16px;
            box-sizing: border-box;
          }
        }

        .tabBox {
          padding: 4px 8px;
          display: flex;
          position: absolute;
          height: 32px;
          top: 16px;
          right: 16px;
          border-radius: 4px;
          background: rgba(0, 128, 255, 0.2);
          .tabItem {
            border-radius: 4px;
            /* 颜色/正文渐变 */
            padding: 1px 8px;
            display: flex;
            align-items: center;
            cursor: pointer;
            &-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
          .active {
            background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
            .tabItem-text {
              font-family: Alibaba PuHuiTi 3;
              font-size: 14px;
              font-weight: normal;
              line-height: 32px;
              letter-spacing: normal;
              background: #0055ff;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
      }
    }

    .workload,
    .ranking-analysis {
      margin-bottom: 24px;

      .chart-container {
        height: 200px;
        position: relative;

        .pentagon-chart {
          width: 100%;
          height: 100%;
        }

        .chart-labels {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          justify-content: space-around;
          align-items: center;
          flex-wrap: wrap;

          .chart-label {
            font-size: 14px;
            color: #ffffff;
          }
        }
      }
    }

    .total-score {
      display: flex;
      justify-content: space-between;
      gap: 16px;

      .score-item {
        flex: 1;
        background: rgba(0, 255, 255, 0.1);
        border-radius: 4px;
        padding: 12px;
        text-align: center;

        .score-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          margin-bottom: 4px;
        }

        .score-value {
          font-size: 20px;
          color: #00ffff;
          font-family: "zcoolqingkehuangyouti";
        }
      }
    }

    .workload {
      .info-content {
        // padding: 16px;
        border-radius: 4px;

        &-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          grid-template-rows: repeat(2, 1fr);
          gap: 16px;
        }

        &-item {
          padding: 16px;
          background: rgba(0, 128, 255, 0.1);
          border-radius: 3.2px;
          cursor: pointer;
          .item-title {
            font-family: Alibaba PuHuiTi 3;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            img {
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
          }

          .item-value {
            font-family: zcoolqingkehuangyouti;
            font-size: 16px;
            line-height: 24px;
            color: #00ffff;
          }
        }
      }
    }
    .workHeight {
      height: 72px;
    }
    .workload-bar {
      display: flex;
      padding: 24px 0px;
      box-sizing: border-box;
      position: absolute;
      bottom: 0px;
      width: 652px;
      justify-content: space-between;
      background: rgb(12, 33, 75);
      border-top: 1px solid rgba(0, 255, 255, 0.1);
      left: 0;
      &-item {
        display: flex;

        font-family: Alibaba PuHuiTi 3;
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: normal;
        /* 主色/卓越青 */
        color: #00ffff;
        align-items: center;

        border-radius: 4px;
        background: rgba(0, 255, 255, 0.1);
        padding: 8px 14px;
        box-sizing: border-box;
        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }
  }
  .imbox-line {
    width: 1px;
    background: rgba(0, 255, 255, 0.2);
    margin-left: 24px;
  }
  .imbox {
    width: 508px;
    height: 100%;
    background: rgba(12, 33, 75, 0.9);
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;

    .call-info-header {
      padding: 16px;
      background: rgba(0, 128, 255, 0.1);
      border-radius: 4px;
      margin-bottom: 16px;

      .call-basic-info {
        .info-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: rgba(255, 255, 255, 0.6);
            width: 80px;
          }

          .value {
            color: #00ffff;
            flex: 1;
          }
        }
      }
    }

    .conversation-list {
      .conversation-item {
        margin-bottom: 24px;

        .chat-content {
          display: flex;
          gap: 12px;

          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(
              180deg,
              #00ffff 0%,
              rgba(0, 255, 255, 0.2) 100%
            );
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .message {
            .role-name {
              color: rgba(255, 255, 255, 0.6);
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 4px;
              &.text-right {
                flex-direction: row-reverse;
              }
            }

            .text-content {
              color: #ffffff;
              font-size: 16px;
            }
          }
        }

        .agent-content {
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
