// 获取URL参数
export function getQueryParam(name, defaultValue = '') {
  const url = window.location.href
  const search = url.split('?')[1]
  if (!search) {
    return defaultValue
  }
  const params = search.split('&')
  for (let i = 0; i < params.length; i++) {
    const param = params[i].split('=')
    if (param[0] === name) {
      return decodeURIComponent(param[1])
    }
  }
  return defaultValue
}

// 格式化时间
export function formatTime(time) {
  if (!time) return '--'
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
} 