<template>
  <div class="drawer-mask" @click.stop="handleClose" v-if="visible"></div>
  <transition name="slide-right">
    <div class="drawer-container" v-if="visible">
      <div class="drawer-left"></div>
      <div class="drawer-center">
        <div class="drawer">
          <!-- 标题区域 -->
          <div class="drawer-header">
            <slot name="header">
              <div class="header-content">
                <base-title>{{ title }}</base-title>
              </div>
              <div class="close-btn" @click="handleClose">
                <img src="../../assets/image/close.png" alt="关闭" />
                <span class="fontStyle">关闭</span>
              </div>
            </slot>
          </div>

          <!-- 内容区域 -->
          <div class="drawer-content">
            <slot>
              <!-- 默认内容 -->
              <div class="default-content"></div>
            </slot>
          </div>

          <!-- 页脚区域 -->
          <div class="drawer-footer" v-if="showBtn">
            <div class="closeBtn" @click="handleClose">
              <img src="../../assets/image/closeBtn.png" alt="关闭" />
            </div>
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
      <div class="drawer-right"></div>
    </div>
  </transition>
</template>

<script setup>
import { defineOptions, defineProps, defineEmits } from "vue";

defineOptions({
  name: "BaseDrawer",
});

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "标题",
  },
  showBtn: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:visible", "confirm", "close"]);

const handleClose = () => {
  emit("close");
};
</script>

<style lang="scss" scoped>
.drawer-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.drawer-container {
  position: absolute;
  top: 0;
  min-width: 632px;
  max-width: none;
  height: 100%;
  transition: right 0.3s ease-in-out;
  z-index: 1000;
  display: flex;
  right: 0;
  width: fit-content;
}

.drawer-left {
  flex: none;
  width: 40px;
  height: 100%;
  background: url("../../assets/image/drawer-left.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 1;
}

.drawer-center {
  flex: none;
  height: 100%;
  background: url("../../assets/image/drawer-center.png") repeat-x;
  background-size: 100% 100%;
  position: relative;
  z-index: 1;
  display: flex;
  min-width: 632px;
  margin-left: -1px;
}

.drawer-right {
  flex: none;
  width: 24px;
  height: 100%;
  background: url("../../assets/image/drawer-right.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 1;
}

.drawer {
  display: flex;
  flex-direction: column;
  background-size: cover;
  color: #fff;
  position: relative;
  // top: 0;
  // bottom: 0;
  // left: -15px;
  // right: 0;
  margin-left: -15px;
}

.drawer-header {
  height: 60px;
  box-sizing: border-box;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  padding: 0 10px;
  position: absolute;
  left: -10px;
  top: 24px;
  display: flex;
  align-items: center;
  gap: 4px;
  span {
    font-size: 18px;
  }
  img {
    width: 24px;
    height: 24px;
  }
}

.drawer-content {
  flex: 1;
  overflow: auto;
}

.drawer-footer {
  height: 72px;
  // padding: 0 20px;
  // border-top: 1px solid rgba(0, 255, 255, 0.1);
  padding: 16px 0px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  .closeBtn {
    cursor: pointer;
    width: 112px;
    height: 40px;
    img {
      width: 100%;
      height: 100%;
    }
    &:hover {
      opacity: 0.8;
    }
  }
}

.footer-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.confirm-btn {
  background: #1890ff;
  color: #fff;
}

.default-content {
  color: #fff;
}

/* 添加右侧滑入动画 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease-out;
}

.slide-right-enter-from {
  transform: translateX(100%);
}

.slide-right-leave-to {
  transform: translateX(100%);
}
</style>
