<template>
  <div class="dialog-mask" @click.stop="handleClose" v-if="visible"></div>
  <div class="dialog-content" v-if="visible">
    <div class="dialog-content-top">
      <div class="close-btn" @click="handleClose">
        <img src="../assets/image/close.png" alt="关闭" />
        <span class="fontStyle">关闭</span>
      </div>
      <BaseTitle>坐席登录</BaseTitle>
    </div>
    <div class="dialog-content-middle">
      <el-form :model="loginForm" :rules="rules" label-width="70px" ref="formRef">
        <el-form-item label="话机号" prop="phone">
          <el-input
            v-model="loginForm.phone"
            placeholder="请输入话机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="密码" prop="credential">
          <el-input
            v-model="loginForm.credential"
            placeholder="请输入密码"
            clearable
            type="password"
          />
        </el-form-item>

        <el-form-item label="技能组" prop="skillId">
          <el-select
            v-model="loginForm.skillId"
            placeholder="请选择技能组"
            style="width: 100%"
          >
            <el-option
              v-for="group in skillGroups"
              :key="group.SKILL_GROUP_ID"
              :label="group.SKILL_GROUP_NAME"
              :value="group.SKILL_GROUP_ID"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-content-bottom">
      <div class="closeBtn" @click="handleClose"></div>
      <div
        class="submitBtn"
        @click="handleLogin"
        :class="{ disabled: loading }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps } from "vue";
import { ElMessage } from "element-plus";
import { useCCBar } from "../composables/useCCBar"; 
import BaseTitle from "./baseComponents/baseTitle.vue";

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  skillGroups: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:visible", "login-success"]);

const { ccbarService } = useCCBar();
const formRef = ref();
const loading = ref(false);

const loginForm = reactive({
  phone: "55553997",
  credential: "iU2#mW0^",
  skillId: "",
});

const rules = {
  phone: [{ required: true, message: "请输入话机号", trigger: "blur" }],
  credential: [{ required: true, message: "请输入密码", trigger: "blur" }],
  skillId: [{ required: true, message: "请选择技能组", trigger: "change" }],
};

const handleClose = () => {
  emit("update:visible", false);
  formRef.value?.resetFields();
};

const handleLogin = async () => {
  if (loading.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;
    loading.value = true;
    const loginData = {
      username: localStorage.getItem("userAccount"),
      phone: loginForm.phone,
      entId: localStorage.getItem("userEntId"),
      skillId: loginForm.skillId,
      credential: loginForm.credential,
      readyMode: "notReady", // 默认就绪模式
      password: ''
    };
    const result = await ccbarService.login(loginData);
    if (result.state) {
      emit("login-success", result);
      handleClose();
    } else {
      ElMessage.error(result.msg || "登录失败");
    }
  } catch (error) {
    console.error("登录失败:", error);
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  padding: 0 10px;
  position: absolute;
  left: 16px;
  top: 18px;
  display: flex;
  align-items: center;
  gap: 4px;
  span {
    font-size: 18px;
  }
  img {
    width: 24px;
    height: 24px;
  }
}

:deep(.is-disabled) {
  background: rgba(0, 128, 255, 0.2);
}

:deep(.el-select__selected-item) {
  font-family: Alibaba PuHuiTi 3;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.dialog-content {
  width: 500px;
  height: 320px;
  color: #fff;
  position: fixed;
  background: url("../assets/image/dialogBg.png") no-repeat center center;
  background-size: 100% 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;

  .dialog-content-top {
    padding-top: 12px;
    width: 500px;
    justify-content: center;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }

  .dialog-content-middle {
    justify-content: center;
    align-items: center;
    padding: 24px 48px;
    box-sizing: border-box;
  }

  .dialog-content-bottom {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    position: absolute;
    bottom: 24px;
    right: 24px;

    .closeBtn {
      width: 112px;
      height: 40px;
      background: url("../assets/image/closeBtn.png") no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }

    .submitBtn {
      width: 112px;
      height: 40px;
      background: url("../assets/image/submitBtn.png") no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

:deep(.el-form) {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

:deep(.el-form-item__label) {
  color: #ffffff;
  font-family: "Alibaba PuHuiTi 3";
  font-size: 14px;
  font-weight: normal;
}

:deep(.el-input) {
  .el-input__wrapper {
    background-color: rgba(0, 128, 255, 0.05);
    border: 1px solid rgba(0, 128, 255, 0.2);
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: rgba(0, 255, 255, 0.4);
    }

    &.is-focus {
      border-color: #00ffff;
      box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.1);
    }
  }

  .el-input__inner {
    color: #ffffff;
    background-color: transparent;

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .el-input__suffix {
    .el-input__clear {
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: #00ffff;
      }
    }
  }
}

:deep(.el-select) {
  width: 100%;

  .el-select__wrapper {
    background-color: rgba(0, 128, 255, 0.05);
    border: 1px solid rgba(0, 128, 255, 0.2);
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: rgba(0, 255, 255, 0.4);
    }

    &.is-focus {
      border-color: #00ffff;
      box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.1);
    }
  }

  .el-select__placeholder {
    color: rgba(255, 255, 255, 0.4);
  }

  .el-select__caret {
    color: rgba(255, 255, 255, 0.6);
  }
}

// 下拉选项样式
:deep(.el-select-dropdown) {
  background: rgba(12, 33, 75, 0.95);
  border: 1px solid rgba(0, 128, 255, 0.2);
  border-radius: 4px;
  backdrop-filter: blur(10px);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(0, 128, 255, 0.2);
      color: #00ffff;
    }

    &.is-selected {
      background: rgba(0, 255, 255, 0.1);
      color: #00ffff;
      font-weight: bold;
    }
  }
}
</style>
